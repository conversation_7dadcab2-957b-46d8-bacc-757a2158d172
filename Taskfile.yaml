# https://taskfile.dev

version: '3'
vars:
  #数据库升级文件
  DB1DIR: ddl/db1_migrations
  
includes:
  dev:
    taskfile: ./Taskfile.dev.yaml
    flatten: true
    internal: true
  platform: 
    taskfile: ./Taskfile_{{OS}}.yaml
    flatten: true
    internal: true

tasks:
  upbuild:
    desc: 全量编译并发布到生产环境
    cmds:
      - task: full-build
      - batchup -s release/vimecore -t /data/vime/vimecore -p 20
  dev:
    desc: 启动开发环境>后台
    cmds:
      - sohot dashboard
  dev-partner:
    desc: 启动开发环境>合作伙伴接口
    cmds:
      - sohot partner
  bufgen:
    desc: 生成buf代码
    cmds:
      - buf generate --template proto/buf.gen.enum.yaml
      - buf generate --template proto/buf.gen.msg.yaml
      - buf generate --template proto/buf.gen.flow.yaml
      - buf generate --template proto/buf.gen.admin.yaml
      - buf generate --template proto/buf.gen.partner.yaml
      - buf generate --template proto/buf.after.process.yaml
      - go-generate-fast api/flow/a.go
      - go-generate-fast api/admin/a.go
      - go-generate-fast api/partner/a.go
    silent: true
  build.comfyapi:
    desc: 编译comfyapi服务程序,此程序需要放在comfyui/ps的系统上
    cmds:
      - gg -build=./cmd/comfyapi -s -w -goos=windows -o=release/comfyapi.exe -xv=omnix/state.Version  
  build-win:
    cmds:
      - gg -build=./cmd/main -s -w -goos=windows -o=release/vime.exe -xv=omnix/state.Version
  build-dashboard:
    desc: 编译后台前端项目
    dir: apps/dashboard
    cmds:
      - npx rsbuild build
  full-build:
    desc: 全量编译linux
    cmds:
      - task: bufgen
      - go-generate-fast toolkit/as/as.go
      - task: db1GenOrm
      - task: build-dashboard
      - gg -build=./cmd/main -s -w -goos=linux -o=release/vimecore -xv=omnix/state.Version
###################################### postgresql 数据库 相关工具
  db1Hash:
    desc: 为migrations重新计算hash
    cmds:
      - atlas migrate hash --dir=file://{{.DB1DIR}}
  db1GenSchema:*:
    vars:
      TARGET: "{{index .MATCH 0}}"
    desc: "从数据库中生成schema结构,注意生成后的需要手动校对修改,然后执行 db1GenOrm 生成orm"
    cmds:
      - entimport -schema-path ./ddl/db1/schema -dsn "{{.DEVDB}}" -tables "{{.TARGET}}"
  db1GenOrm:
    desc: 生成db1 ORM
    cmds:
      - go generate ddl/db1/gen/entc.go
      - bobgen-psql -c db1gen.yaml
      - go run ddl/db1/gen/migrate.go "{{.DEVDB}}"
  
  db1GenSql:*:
    desc: 生成数据库升级文件
    vars: 
      NAME: "{{index .MATCH 0}}"
    cmds:
#      - go generate ddl/db1/gen/entc.go
      - atlas migrate hash --dir=file://{{.DB1DIR}}
#      - go run ddl/db1/gen/migrate.go {{.NAME}} "{{.TESTDB}}"
      - atlas migrate diff --dir "file://{{.DB1DIR}}" --dev-url "{{.TESTDB}}" --to "{{.DEVDB}}" {{.NAME}}
      - gg -goose={{.DB1DIR}}
  db1Create:*:
    desc: "创建数据表定义文件到 /ddl/db1/schema,然后手动创建字段"
    vars: 
      TABLE_NAME: "{{index .MATCH 0}}"
    cmds:
      - ent new "--target" ddl/db1/schema {{.TABLE_NAME}}
    silent: true
  default:
    desc: "列出所有任务"
    cmds:
      - "task -a"
    silent: true
  install-dashboard:
    dir: apps/dashboard
    cmds:
      - pnpm install
      - pnpm approve-builds
  install:
    desc: 安装项目工具
    deps:
      - check-pnpm
    cmds:
      - go install github.com/qwenode/sohot@latest
      - go install github.com/qwenode/goverter/cmd/goverter@main
      - go install github.com/bufbuild/buf/cmd/buf@latest
      - go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
      - go install connectrpc.com/connect/cmd/protoc-gen-connect-go@latest
      - go install github.com/cludden/protoc-gen-go-temporal/cmd/protoc-gen-go_temporal@latest
      - go install github.com/sudorandom/protoc-gen-connect-openapi@latest
      - go install github.com/pseudomuto/protoc-gen-doc/cmd/protoc-gen-doc@master
      - go install github.com/lcmaguire/protoc-gen-go-setters@latest
      - go install github.com/mfridman/protoc-gen-connectclient-go@latest
      - go install github.com/stephenafamo/bob/gen/bobgen-psql@latest
      - go install github.com/pressly/goose/v3/cmd/goose@latest
      - go install entgo.io/ent/cmd/ent@master
      - go install github.com/google/wire/cmd/wire@latest
      - go get github.com/oNaiPs/go-generate-fast
      - go install github.com/oNaiPs/go-generate-fast
      - go install github.com/qwenode/entimport/cmd/entimport@latest
      - pnpm add -g @bufbuild/protoc-gen-es@2.2.5
      - pnpm add -g @connectrpc/protoc-gen-connect-query@2.0.1
      - buf dep update
      - task: install-dashboard
