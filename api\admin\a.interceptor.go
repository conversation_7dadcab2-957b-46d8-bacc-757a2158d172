package adminservice

import (
    "bytes"
    "omnix/api/admin/jwt"
    "omnix/genpb/enumpb"
    "omnix/toolkit/kitctx"
    "omnix/toolkit/wrapper/kitprotovalidate"

    "io"
    "net/http"
    "strings"

    "connectrpc.com/connect"
    "github.com/gin-gonic/gin"
    "github.com/qwenode/rr"
    "github.com/rs/zerolog/log"
)

func NewValidateInterceptor() connect.Option {
    validator, err := kitprotovalidate.NewInterceptor()
    if err != nil {
        log.Panic().Err(err).Msg("验证引擎初始化失败")
    }
    return connect.WithInterceptors(validator)
}

// 20250410 基础数据设置 by Node
func NewCommonMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        ip := kitctx.GetClientIp(c)
        c.Set(enumpb.CTX_VAR_CLIENT_IP.String(), ip)
        // 20250410 支持connectrpc的get by Node
        if c.Request.Method == http.MethodGet {
            // 20250410 如果get没传encoding,会报415 by Node
            if c.Query("encoding") != "json" {
                c.Request.URL.RawQuery += "&encoding=json"
            }
            // 20250410 如果get有body,会报415 by Node
            c.Request.Body = io.NopCloser(bytes.NewReader([]byte{}))
            c.Request.ContentLength = -1
        }
        c.Next()
    }
}

// 20250410 传gin.context给grpc by Node
func NewGinContextAdapterMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Request = c.Request.WithContext(c)
        c.Next()
    }
}

// 20250410 登录校验 by Node
func NewAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        var (
            tokenHeader = c.GetHeader("Authorization")
        )
        err := rr.Try(
            func() error {
                noAuth := kitctx.NewUnauthenticated("token invalid")
                if len(tokenHeader) < 7 || !strings.EqualFold(tokenHeader[:7], "bearer ") {
                    return noAuth
                }
                identity, err := jwt.Parse(tokenHeader[7:])
                if err != nil {
                    return noAuth
                }
                // 这里要做用户状态检测 20250808
                c.Set(enumpb.CTX_VAR_JWT_CLAIMS.String(), identity)
                return nil
            },
        )
        if err != nil {
            _ = connect.NewErrorWriter().Write(
                c.Writer, c.Request, err,
            )
            c.Abort()
            return
        }
        c.Next()
    }
}
