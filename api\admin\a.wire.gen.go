// Code generated by proto-gen-gg. DO NOT EDIT.

//go:build wireinject
// +build wireinject

//
package adminservice

import "github.com/google/wire"

import (
	"omnix/api/admin/authservice"
	"omnix/api/admin/authedservice"
	"omnix/api/admin/materialservice"
	"omnix/api/admin/psdservice"
	"omnix/api/admin/psdgroupservice"
	"omnix/api/admin/temuservice"
	"omnix/api/admin/trademarkservice"
	"omnix/api/admin/uploaddataservice"
)

//go:generate go-generate-fast ./authservice/a.authservice.init.go
//go:generate go-generate-fast ./authedservice/a.authedservice.init.go
//go:generate go-generate-fast ./materialservice/a.materialservice.init.go
//go:generate go-generate-fast ./psdservice/a.psdservice.init.go
//go:generate go-generate-fast ./psdgroupservice/a.psdgroupservice.init.go
//go:generate go-generate-fast ./temuservice/a.temuservice.init.go
//go:generate go-generate-fast ./trademarkservice/a.trademarkservice.init.go
//go:generate go-generate-fast ./uploaddataservice/a.uploaddataservice.init.go

// 未登录的后台账号功能 20250827
func GetAuthService() *authservice.AuthService {
	wire.Build(AdditionsSet)
	return nil
}

// 已登录的后台账号管理 20250827
func GetAuthedService() *authedservice.AuthedService {
	wire.Build(AdditionsSet)
	return nil
}

// 素材管理 20250904
func GetMaterialService() *materialservice.MaterialService {
	wire.Build(AdditionsSet)
	return nil
}

// PSD模板管理 20250916
func GetPsdService() *psdservice.PsdService {
	wire.Build(AdditionsSet)
	return nil
}

// PSD分组管理 20250910
func GetPsdGroupService() *psdgroupservice.PsdGroupService {
	wire.Build(AdditionsSet)
	return nil
}

// Temu服务 20250827
func GetTemuService() *temuservice.TemuService {
	wire.Build(AdditionsSet)
	return nil
}

// 商标管理 20250808
func GetTrademarkService() *trademarkservice.TrademarkService {
	wire.Build(AdditionsSet)
	return nil
}

// 采集数据上报 20250808
func GetUploadDataService() *uploaddataservice.UploadDataService {
	wire.Build(AdditionsSet)
	return nil
}

var ProviderSet = wire.NewSet(
	authservice.ProviderSet,
	authedservice.ProviderSet,
	materialservice.ProviderSet,
	psdservice.ProviderSet,
	psdgroupservice.ProviderSet,
	temuservice.ProviderSet,
	trademarkservice.ProviderSet,
	uploaddataservice.ProviderSet,
)
