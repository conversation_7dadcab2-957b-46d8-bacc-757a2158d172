package authedservice

import (
	"context"
	"omnix/genpb/adminpb"
	"omnix/genpb/enumpb"
	"omnix/toolkit/kitctx"

	"connectrpc.com/connect"
)

// 获取当前账号资料 接口 20250827
func (r *AuthedService) Profile(
	c context.Context, request *connect.Request[adminpb.ProfileRequest],
) (*connect.Response[adminpb.ProfileResponse], error) {
	var (
		//msg = request.Msg
		//go:generate msgp -tests=false
		o = &adminpb.ProfileResponse{}
	)
	clains, err := kitctx.GetAdminClains(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	exist, err := r.db1c.R().Administrator.Query().WhereUsername(clains.Username).WhereState(enumpb.ADMINISTRATOR_STATE_ACTIVE.ToSnake()).Exist(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	if !exist {
		return nil, kitctx.NewUnauthenticated("登录失效")
	}
	o.Username = clains.Username
	return connect.NewResponse(o), nil
}
