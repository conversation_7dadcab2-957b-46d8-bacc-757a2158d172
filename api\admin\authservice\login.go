package authservice

import (
    "context"
    "omnix/api/admin/jwt"
    "omnix/ddl/db1"
    "omnix/genpb/adminpb"
    "omnix/genpb/enumpb"
    "omnix/toolkit/kitctx"
    "omnix/toolkit/kitencrypt"
    "omnix/types"
    "time"

    "connectrpc.com/connect"
    jwt2 "github.com/golang-jwt/jwt/v5"
)

// 登录
func (r *AuthService) Login(
    c context.Context, request *connect.Request[adminpb.LoginRequest],
) (*connect.Response[adminpb.LoginResponse], error) {
    var (
        msg = request.Msg
        o   = &adminpb.LoginResponse{}
        dbc = r.db1c.R()
    )
    user, err := dbc.Administrator.Query().WhereUsername(msg.GetUsername()).Only(c)
    if err != nil {
        if db1.IsNotFound(err) {
            return nil, kitctx.NewInvalidArgument("账号或密码错误")
        }
        return nil, kitctx.NewInternalErr(err)
    }
    if !enumpb.ADMINISTRATOR_STATE_ACTIVE.Is(enumpb.AdministratorState_FromString(user.State)) {
        return nil, kitctx.NewPermissionDenied("账号异常")
    }
    if !kitencrypt.ValidatePasswordHash(msg.GetPassword(), user.Password) {
        return nil, kitctx.NewInvalidArgument("账号或密码错误")
    }
    date := jwt2.NewNumericDate(time.Now().Add(time.Second * 86400 * 10))
    sign, err := jwt.Sign(types.JwtAdminClaims{
        RegisteredClaims: jwt2.RegisteredClaims{ExpiresAt: date},
        Username:         msg.GetUsername(),
    })
    if err != nil {
        return nil, kitctx.NewInternalErr(err)
    }
    o.Token = sign
    o.Username = msg.GetUsername()
    o.ExpireAt = date.Unix()
    
    return connect.NewResponse(o), nil
}
