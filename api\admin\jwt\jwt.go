package jwt

import (
    "crypto"
    "crypto/ed25519"
    "encoding/hex"
    "fmt"
    "omnix/state"
    "omnix/toolkit/kitctx"
    "omnix/types"
    "sync"

    "github.com/golang-jwt/jwt/v5"
    "github.com/rs/zerolog/log"
)

var instance *jwtx
var once sync.Once

type jwtx struct {
    key       ed25519.PrivateKey
    publicKey crypto.PublicKey
    algorithm jwt.SigningMethod
}

func Get() *jwtx {
    once.Do(
        func() {
            bytes, err := hex.DecodeString(state.RemoteOptions.Admin.JwtKey)
            if err != nil || len(bytes) != ed25519.SignatureSize {
                log.Fatal().Err(err).Str("Key",state.RemoteOptions.Admin.JwtKey).Msg("jwt key不正确")
                return
            }
            privateKey := ed25519.NewKeyFromSeed(bytes[:ed25519.SeedSize])
            instance = &jwtx{
                key:       privateKey,
                publicKey: privateKey.Public(),
                algorithm: jwt.SigningMethodEdDSA,
            }
        },
    )
    return instance
}
func Sign(claims types.JwtAdminClaims) (string, error) {
    return Get().Sign(claims)
}
func Parse(token string) (*types.JwtAdminClaims, error) {
    return Get().Parse(token)
}

// 签名
func (r *jwtx) Sign(claims types.JwtAdminClaims) (string, error) {
    c := jwt.NewWithClaims(r.algorithm, claims)
    return c.SignedString(r.key)
}

// 解析,验证
func (r *jwtx) Parse(token string) (*types.JwtAdminClaims, error) {
    identity := &types.JwtAdminClaims{}
    parsed, err := jwt.ParseWithClaims(
        token, identity, func(token *jwt.Token) (interface{}, error) {
            if _, ok := token.Method.(*jwt.SigningMethodEd25519); !ok {
                return nil, fmt.Errorf("Unexpected signing method")
            }
            return r.publicKey, nil
        },
        // 要求验证过期时间
        jwt.WithExpirationRequired(),
    )
    invalidToken := kitctx.NewInvalidArgument("token invalid")
    if err != nil {
        return nil, invalidToken
    }
    if !parsed.Valid {
        return nil, invalidToken
    }
    // 验证用户是否存在,token是否被吊销等
    if identity.Username == "" {
        return nil, invalidToken
    }
    
    return identity, nil
}
