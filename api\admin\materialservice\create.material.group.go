package materialservice

import (
    "context"
    "omnix/ddl/db1"
    "omnix/ddl/db1/materialgroup"
    "omnix/genpb/adminpb"
    "omnix/toolkit/as"
    "omnix/toolkit/kitctx"
    "omnix/toolkit/kitdb"

    "connectrpc.com/connect"
)

// 创建素材分组 接口 20250904
func (r *MaterialService) CreateMaterialGroup(
    c context.Context, request *connect.Request[adminpb.CreateMaterialGroupRequest],
) (*connect.Response[adminpb.CreateMaterialGroupResponse], error) {
    var (
        msg = request.Msg
        o   = &adminpb.CreateMaterialGroupResponse{}
        dbc = r.db1c.R()
    )
    err := kitdb.WithTx(c, dbc, func(tx *db1.Tx) error {
        // 检查分组名称是否已存在
        exists, err := tx.MaterialGroup.Query().Where(materialgroup.NameEQ(msg.GetName())).Exist(c)
        if err != nil {
            return kitctx.NewInternalErr(err)
        }
        if exists {
            return kitctx.NewAlreadyExists("分组名称已存在")
        }

        // 创建素材分组
        created, err := tx.MaterialGroup.Create().
            SetName(msg.GetName()).
            Save(c)
        if err != nil {
            return kitctx.NewInternalErr(err)
        }
        o.Item = as.CommonConvert.MaterialGroup_MsgpbMaterialGroup(created)
        return nil
    })
    if err != nil {
        return nil, err
    }

    return connect.NewResponse(o), nil
}
