package materialservice

import (
    "context"
    "omnix/genpb/adminpb"
    "omnix/toolkit/kitctx"

    "connectrpc.com/connect"
    "entgo.io/ent/dialect/sql"
)

// 删除素材分组 接口 20250904
func (r *MaterialService) DeleteMaterialGroup(
    c context.Context, request *connect.Request[adminpb.DeleteMaterialGroupRequest],
) (*connect.Response[adminpb.DeleteMaterialGroupResponse], error) {
    var (
        msg = request.Msg
        o   = &adminpb.DeleteMaterialGroupResponse{}
        dbc = r.db1c.R()
    )
	
    for _, id := range msg.GetId() {
        exist, err := dbc.Material.Query().Where(func(selector *sql.Selector) {
            selector.Where(sql.ExprP("$1=ANY(material_group)", id))
        }).Limit(1).Exist(c)
        if err != nil {
            return nil, kitctx.NewInternalErr(err)
        }
        if exist {
            return nil, kitctx.NewFailedPrecondition("分组下存在素材，无法删除")
        }

        // 删除分组
        err = dbc.MaterialGroup.DeleteOneID(id).Exec(c)
        if err != nil {
            return nil, kitctx.NewInternalErr(err)
        }
    }

    return connect.NewResponse(o), nil
}
