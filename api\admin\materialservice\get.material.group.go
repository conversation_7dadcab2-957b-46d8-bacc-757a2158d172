package materialservice

import (
    "context"
    "omnix/genpb/adminpb"
    "omnix/toolkit/as"
    "omnix/toolkit/kitctx"

    "connectrpc.com/connect"
)

// 获取素材分组详情 接口 20250904
func (r *MaterialService) GetMaterialGroup(
	c context.Context, request *connect.Request[adminpb.GetMaterialGroupRequest],
) (*connect.Response[adminpb.GetMaterialGroupResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.GetMaterialGroupResponse{}
        dbc = r.db1c.R()
	)
    get, err := dbc.MaterialGroup.Get(c, msg.GetId())
    if err != nil {
        return nil,kitctx.NewNotFoundErr(err)
    }
    o.Item = as.CommonConvert.MaterialGroup_MsgpbMaterialGroup(get)
    return connect.NewResponse(o), nil
}
