package materialservice

import (
    "context"
    "omnix/ddl/db1/materialgroup"
    "omnix/genpb/adminpb"
    "omnix/genpb/msgpb"
    "omnix/toolkit/as"
    "omnix/toolkit/kitctx"
    "omnix/toolkit/stdfmt"

    "connectrpc.com/connect"
)

// 查询素材分组列表 接口 20250904
func (r *MaterialService) QueryMaterialGroupList(
    c context.Context, request *connect.Request[adminpb.QueryMaterialGroupListRequest],
) (*connect.Response[adminpb.QueryMaterialGroupListResponse], error) {
    var (
        msg = request.Msg
        o   = &adminpb.QueryMaterialGroupListResponse{}
        dbc = r.db1c.R()
    )

    // 构建查询
    query := dbc.MaterialGroup.Query()

    // 添加名称筛选
    if msg.GetName() != "" {
        query = query.Where(materialgroup.NameContains(msg.GetName()))
    }

    // 获取总数
    total, err := query.Count(c)
    if err != nil {
        return nil, kitctx.NewInternalErr(err)
    }
    offset, limit, _ := stdfmt.PageSize(msg.GetPageSize())
    query = query.Offset(offset).Limit(limit)

    // 执行查询
    groups, err := query.All(c)
    if err != nil {
        return nil, kitctx.NewInternalErr(err)
    }
	
    // 转换结果
    o.Total = int64(total)
    o.Items = make([]*msgpb.MaterialGroup, len(groups))
    for i, group := range groups {
        o.Items[i] = as.CommonConvert.MaterialGroup_MsgpbMaterialGroup(group)
    }

    return connect.NewResponse(o), nil
}
