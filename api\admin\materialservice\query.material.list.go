package materialservice

import (
	"context"
    "omnix/ddl/db1"
    "omnix/ddl/db1/material"
	"omnix/genpb/adminpb"
	"omnix/genpb/msgpb"
	"omnix/toolkit/as"
	"omnix/toolkit/kitctx"
	"omnix/toolkit/stdfmt"
    stdfmtb2path "omnix/toolkit/stdfmt/b2path"

    "connectrpc.com/connect"
)

// 查询素材列表 接口 20250904
func (r *MaterialService) QueryMaterialList(
	c context.Context, request *connect.Request[adminpb.QueryMaterialListRequest],
) (*connect.Response[adminpb.QueryMaterialListResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.QueryMaterialListResponse{}
		dbc = r.db1c.R()
	)

	// 构建查询
	query := dbc.Material.Query()

	// 添加标题筛选
	if msg.GetTitle() != "" {
		 query.Where(material.TitleContains(msg.GetTitle()))
	}

	// 获取总数
	total, err := query.Count(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}

	// 分页处理
	offset, limit, _ := stdfmt.PageSize(msg.GetPageSize())
	query.Offset(offset).Limit(limit)

	// 按创建时间倒序排列
	query.Order(db1.Desc(material.FieldCreatedTime))

	// 执行查询
	materials, err := query.All(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}

	// 转换结果
	o.Total = int64(total)
	o.Items = make([]*msgpb.Material, len(materials))
	for i, mat := range materials {
        msgpbMaterial := as.CommonConvert.Material_MsgpbMaterial(mat)
        msgpbMaterial.Path = stdfmtb2path.GetVisitUrl(msgpbMaterial.Path)
        o.Items[i] = msgpbMaterial
	}

	return connect.NewResponse(o), nil
}
