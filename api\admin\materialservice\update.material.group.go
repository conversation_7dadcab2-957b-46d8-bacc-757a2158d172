package materialservice

import (
    "context"
    "omnix/ddl/db1"
    "omnix/ddl/db1/materialgroup"
    "omnix/genpb/adminpb"
    "omnix/toolkit/as"
    "omnix/toolkit/kitctx"
    "omnix/toolkit/kitdb"

    "connectrpc.com/connect"
)

// 更新素材分组 接口 20250904
func (r *MaterialService) UpdateMaterialGroup(
    c context.Context, request *connect.Request[adminpb.UpdateMaterialGroupRequest],
) (*connect.Response[adminpb.UpdateMaterialGroupResponse], error) {
    var (
        msg = request.Msg
        o   = &adminpb.UpdateMaterialGroupResponse{}
        dbc = r.db1c.R()
    )
    err := kitdb.WithTx(c, dbc, func(tx *db1.Tx) error {
        // 检查分组是否存在
        exists, err := tx.MaterialGroup.Query().Where(materialgroup.IDEQ(msg.GetId())).Exist(c)
        if err != nil {
            return kitctx.NewInternalErr(err)
        }
        if !exists {
            return kitctx.NewNotFound("分组不存在")
        }

        // 检查新名称是否与其他分组重复
        nameExists, err := tx.MaterialGroup.Query().
            Where(materialgroup.NameEQ(msg.GetName()), materialgroup.IDNEQ(msg.GetId())).
            Exist(c)
        if err != nil {
            return kitctx.NewInternalErr(err)
        }
        if nameExists {
            return kitctx.NewAlreadyExists("分组名称已存在")
        }

        // 更新素材分组
        updated, err := dbc.MaterialGroup.UpdateOneID(msg.GetId()).
            SetName(msg.GetName()).
            Save(c)
        if err != nil {
            return kitctx.NewInternalErr(err)
        }

        o.Item = as.CommonConvert.MaterialGroup_MsgpbMaterialGroup(updated)
        return nil
    })
    if err != nil {
        return nil, err
    }
    return connect.NewResponse(o), nil
}
