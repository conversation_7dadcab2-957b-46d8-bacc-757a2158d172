package psdgroupservice

import (
	"connectrpc.com/connect"
	"context"
	"omnix/ddl/db1"
	"omnix/ddl/db1/psdgroup"
	"omnix/genpb/adminpb"
	"omnix/toolkit/as"
	"omnix/toolkit/kitctx"
	"omnix/toolkit/kitdb"
)

// 创建Psd分组 接口 20250904
func (r *PsdGroupService) CreatePsdGroup(
	c context.Context, request *connect.Request[adminpb.CreatePsdGroupRequest],
) (*connect.Response[adminpb.CreatePsdGroupResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.CreatePsdGroupResponse{}
		dbc = r.db1c.R()
	)
	
	err := kitdb.WithTx(c, dbc, func(tx *db1.Tx) error {
		// 检查分组是否存在
		exists, err := tx.PsdGroup.Query().Where(psdgroup.NameEQ(msg.GetName())).Exist(c)
		if err != nil {
			return kitctx.NewInternalErr(err)
		}
		if exists {
			return kitctx.NewAlreadyExists("分组名称已存在")
		}
		
		// 创建psd分组
		created, err := tx.PsdGroup.Create().
			SetName(msg.GetName()).
			Save(c)
		if err != nil {
			return kitctx.NewInternalErr(err)
		}
		o.Item = as.CommonConvert.PSDGroup_MsgPSDGroup(created)
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	
	return connect.NewResponse(o), nil
}
