package psdgroupservice

import (
	"connectrpc.com/connect"
	"context"
	"entgo.io/ent/dialect/sql"
	"omnix/genpb/adminpb"
	"omnix/toolkit/kitctx"
)

// 删除Psd分组 接口 20250904
func (r *PsdGroupService) DeletePsdGroup(
	c context.Context, request *connect.Request[adminpb.DeletePsdGroupRequest],
) (*connect.Response[adminpb.DeletePsdGroupResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.DeletePsdGroupResponse{}
		dbc = r.db1c.R()
	)
	for _, id := range msg.GetId() {
		exist, err := dbc.Psd.Query().Where(func(selector *sql.Selector) {
			selector.Where(sql.ExprP("$1=ANY(psd_group_id)", id))
		}).Limit(1).Exist(c)
		if err != nil {
			return nil, kitctx.NewInternalErr(err)
		}
		if exist {
			return nil, kitctx.NewFailedPrecondition("分组下存在psd模板， 无法删除")
		}
		
		// 删除分组
		err = dbc.PsdGroup.DeleteOneID(id).Exec(c)
		if err != nil {
			return nil, kitctx.NewInternalErr(err)
		}
	}
	
	return connect.NewResponse(o), nil
}
