package psdgroupservice

import (
	"connectrpc.com/connect"
	"context"
	"omnix/genpb/adminpb"
	"omnix/toolkit/as"
	"omnix/toolkit/kitctx"
)

// 获取PSD分组详情 接口 20250910
func (r *PsdGroupService) GetPsdGroup(
	c context.Context, request *connect.Request[adminpb.GetPsdGroupRequest],
) (*connect.Response[adminpb.GetPsdGroupResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.GetPsdGroupResponse{}
		dbc = r.db1c.R()
	)
	get, err := dbc.PsdGroup.Get(c, msg.GetId())
	if err != nil {
		return nil, kitctx.NewNotFoundErr(err)
	}
	o.Item = as.CommonConvert.PSDGroup_MsgPSDGroup(get)
	return connect.NewResponse(o), nil
}
