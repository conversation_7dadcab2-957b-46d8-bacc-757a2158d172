package psdgroupservice

import (
	"connectrpc.com/connect"
	"context"
	"omnix/ddl/db1/psdgroup"
	"omnix/genpb/adminpb"
	"omnix/genpb/msgpb"
	"omnix/toolkit/as"
	"omnix/toolkit/kitctx"
	"omnix/toolkit/stdfmt"
)

// 查询PSD分组列表 接口 20250910
func (r *PsdGroupService) QueryPsdGroupList(
	c context.Context, request *connect.Request[adminpb.QueryPsdGroupListRequest],
) (*connect.Response[adminpb.QueryPsdGroupListResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.QueryPsdGroupListResponse{}
		dbc = r.db1c.R()
	)
	
	// 构建查询
	query := dbc.PsdGroup.Query()
	
	// 添加名称筛选
	if msg.GetName() != "" {
		query = query.Where(psdgroup.NameEQ(msg.GetName()))
	}
	
	// 获取总数
	total, err := query.Count(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	offset, limit, _ := stdfmt.PageSize(msg.GetPageSize())
	query = query.Offset(offset).Limit(limit)
	
	// 执行查询
	groups, err := query.Order(psdgroup.ByID()).All(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	
	// 转换结果
	o.Total = int64(total)
	o.Items = make([]*msgpb.PSDGroup, len(groups))
	for i, group := range groups {
		o.Items[i] = as.CommonConvert.PSDGroup_MsgPSDGroup(group)
	}
	
	return connect.NewResponse(o), nil
}
