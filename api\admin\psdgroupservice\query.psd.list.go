package psdgroupservice

import (
	"connectrpc.com/connect"
	"context"
	"omnix/ddl/db1/psd"
	"omnix/genpb/adminpb"
	"omnix/genpb/msgpb"
	"omnix/toolkit/as"
	"omnix/toolkit/kitctx"
	"omnix/toolkit/stdfmt"
	stdfmtb2path "omnix/toolkit/stdfmt/b2path"
)

// 查询PSD模板列表 接口 20250910
func (r *PsdGroupService) QueryPSDList(
	c context.Context, request *connect.Request[adminpb.QueryPSDListRequest],
) (*connect.Response[adminpb.QueryPSDListResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.QueryPSDListResponse{}
		dbc = r.db1c.R()
	)
	
	// 构建查询
	query := dbc.Psd.Query()
	
	// 添加Id筛选
	if msg.GetId() != 0 {
		query.Where(psd.IDEQ(msg.GetId()))
	}
	
	// 获取总数
	total, err := query.Count(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	
	// 分页
	offset, limit, _ := stdfmt.PageSize(msg.GetPageSize())
	query = query.Offset(offset).Limit(limit)
	
	// 执行查询
	psds, err := query.All(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	
	// 转换结果
	o.Total = int64(total)
	o.Items = make([]*msgpb.PSD, len(psds))
	for i, item := range psds {
		msgPsd := as.CommonConvert.PSDp_MsgPSD(item)
		msgPsd.FilePath = stdfmtb2path.GetVisitUrl(msgPsd.FilePath)
		o.Items[i] = msgPsd
	}
	
	return connect.NewResponse(o), nil
}
