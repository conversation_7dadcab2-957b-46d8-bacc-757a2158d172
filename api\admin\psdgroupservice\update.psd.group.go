package psdgroupservice

import (
	"connectrpc.com/connect"
	"context"
	"omnix/ddl/db1"
	"omnix/ddl/db1/psdgroup"
	"omnix/genpb/adminpb"
	"omnix/toolkit/as"
	"omnix/toolkit/kitctx"
	"omnix/toolkit/kitdb"
)

// 更新PSD分组 接口 20250910
func (r *PsdGroupService) UpdatePsdGroup(
	c context.Context, request *connect.Request[adminpb.UpdatePsdGroupRequest],
) (*connect.Response[adminpb.UpdatePsdGroupResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.UpdatePsdGroupResponse{}
		dbc = r.db1c.R()
	)
	
	err := kitdb.WithTx(c, dbc, func(tx *db1.Tx) error {
		// 检查分组是否存在
		exists, err := tx.PsdGroup.Query().Where(psdgroup.IDEQ(msg.GetId())).Exist(c)
		if err != nil {
			return kitctx.NewInternalErr(err)
		}
		if !exists {
			return kitctx.NewNotFound("分组不存在")
		}
		
		// 检查新名称是否与其他分组名称重复
		nameExists, err := tx.PsdGroup.Query().
			Where(psdgroup.NameEQ(msg.GetName()), psdgroup.IDNEQ(msg.GetId())).
			Exist(c)
		if err != nil {
			return kitctx.NewInternalErr(err)
		}
		if nameExists {
			return kitctx.NewAlreadyExists("分组名称已存在")
		}
		
		// 更新psd分组
		updated, err := dbc.PsdGroup.UpdateOneID(msg.GetId()).SetName(msg.GetName()).Save(c)
		if err != nil {
			return kitctx.NewInternalErr(err)
		}
		
		o.Item = as.CommonConvert.PSDGroup_MsgPSDGroup(updated)
		return nil
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(o), nil
}
