package psdservice

import (
	"context"
	"connectrpc.com/connect"
	"omnix/genpb/adminpb"
)

// 删除PSD分组 接口 20250916
func (r *PsdService) DeletePsdFile(
	c context.Context, request *connect.Request[adminpb.DeletePsdFileRequest],
) (*connect.Response[adminpb.DeletePsdFileResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.DeletePsdFileResponse{}
	)
	// TODO 请检查 init() 的鉴权配置,确认此接口是否要登录授权
	panic(msg)

	return connect.NewResponse(o), nil
}
