package psdservice

import (
	"context"
	"connectrpc.com/connect"
	"omnix/genpb/adminpb"
)

// 更新PSD分组 接口 20250916
func (r *PsdService) DownloadFile(
	c context.Context, request *connect.Request[adminpb.DownloadPsdFileRequest],
) (*connect.Response[adminpb.DownloadPsdFileResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.DownloadPsdFileResponse{}
	)
	// TODO 请检查 init() 的鉴权配置,确认此接口是否要登录授权
	panic(msg)

	return connect.NewResponse(o), nil
}
