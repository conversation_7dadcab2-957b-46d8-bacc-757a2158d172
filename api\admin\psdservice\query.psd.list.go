package psdservice

import (
	"context"
	"connectrpc.com/connect"
	"omnix/genpb/adminpb"
)

// 查询PSD模板列表 接口 20250916
func (r *PsdService) QueryPsdList(
	c context.Context, request *connect.Request[adminpb.QueryPsdListRequest],
) (*connect.Response[adminpb.QueryPsdListResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.QueryPsdListResponse{}
	)
	// TODO 请检查 init() 的鉴权配置,确认此接口是否要登录授权
	panic(msg)

	return connect.NewResponse(o), nil
}
