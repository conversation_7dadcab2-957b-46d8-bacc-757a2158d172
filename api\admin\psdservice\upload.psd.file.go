package psdservice

import (
	"context"
	"connectrpc.com/connect"
	"omnix/genpb/adminpb"
	"omnix/toolkit/kitctx"
)

// 创建PSD分组 接口 20250916
func (r *PsdService) UploadPsdFile(
	c context.Context, request *connect.Request[adminpb.UploadPsdFileRequest],
) (*connect.Response[adminpb.UploadPsdFileResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.UploadPsdFileResponse{}
	)
	
	// 检查文件内容
	//if msg.hash == nil || len(msg.FileData) == 0 {
	//	return nil, kitctx.NewNotFound("未获取到文件")
	//}
	
	return connect.NewResponse(o), nil
}
