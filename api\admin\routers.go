package adminservice

import (
	"net/http"
	"omnix/genpb/adminpb/adminpbconnect"
	
	"connectrpc.com/connect"
)

func addRouters() {
	// psd分组管理
	addService(authRequired, func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler) {
		return adminpbconnect.NewPsdGroupServiceHandler(GetPsdGroupService(), mustAppendInterceptors...)
	})
	
	// 商标数据
	addService(authRequired, func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler) {
		return adminpbconnect.NewTrademarkServiceHandler(GetTrademarkService(), mustAppendInterceptors...)
	})
	// 素材管理 20250904
	addService(authRequired, func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler) {
		return adminpbconnect.NewMaterialServiceHandler(GetMaterialService(), mustAppendInterceptors...)
	})
	
	addService(authRequired, func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler) {
		return adminpbconnect.NewTemuServiceHandler(GetTemuService(), mustAppendInterceptors...)
	})
	
	// 用户登录后服务 20250827
	addService(authRequired, func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler) {
		return adminpbconnect.NewAuthedServiceHandler(GetAuthedService(), mustAppendInterceptors...)
	})
	
	// 数据上报服务,鉴权由每个方法自行处理 20250808
	addService(authGuest, func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler) {
		return adminpbconnect.NewUploadDataServiceHandler(GetUploadDataService(), mustAppendInterceptors...)
	})
	// 用户登录服务 20250808
	addService(authGuest, func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler) {
		return adminpbconnect.NewAuthServiceHandler(GetAuthService(), mustAppendInterceptors...)
	})
}
