package temuservice

import (
    "context"
    "fmt"
    "omnix/ddl/db1"
    "omnix/ddl/db1/productitem"
    "omnix/genpb/msgpb"
    "omnix/toolkit/as"
    "omnix/toolkit/stdfmt"

    "omnix/genpb/adminpb"

    "connectrpc.com/connect"
    "entgo.io/ent/dialect/sql"
    "github.com/qwenode/rr"
)

// 查询Temu数据列表 接口 20250828
func (r *TemuService) QueryTemuList(
    c context.Context, request *connect.Request[adminpb.QueryTemuListRequest],
) (*connect.Response[adminpb.QueryTemuListResponse], error) {
    var (
        msg = request.Msg
        o   = &adminpb.QueryTemuListResponse{}
        //dbc = r.db1c.GetBobDB()
        en = r.db1c.R()
    )
    q := en.ProductItem.Query()
    seq:=1
    if msg.GetTitle() != "" {
         sprintf := fmt.Sprintf("to_tsvector('english', spec->>'title') @@ plainto_tsquery('english', $%d)", seq)
        q.Where(func(selector *sql.Selector) {
            selector.Where(sql.ExprP(
                sprintf,
                msg.GetTitle(),
            ))
        })
        seq++
    }
    if len(msg.GetTemuCats()) > 0 {
        sprintf := fmt.Sprintf("spec->'temu_cats' @> $%d", seq)
        q.Where(func(selector *sql.Selector) {
            selector.Where(sql.ExprP(sprintf,rr.JsonSerialize(msg.GetTemuCats())))
        })
        seq++
    }
    from, size, _ := stdfmt.PageSize(msg.GetPageSize())
    count, _ := q.Count(c)
    o.Total = int64(count)
    items, _ := q.Offset(from).Limit(size).Order(db1.Desc(productitem.FieldUpdatedTime)).All(c)
    o.Items = make([]*msgpb.ProductItem, 0, size)
    for _, item := range items {
        o.Items = append(o.Items, as.CommonConvert.ProductItem_MsgpbProductItem(item))
    }
    return connect.NewResponse(o), nil
}
