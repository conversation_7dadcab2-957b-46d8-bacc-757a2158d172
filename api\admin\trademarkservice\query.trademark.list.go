package trademarkservice

import (
	"connectrpc.com/connect"
	"context"
	"omnix/ddl/db1/registeredtrademark"
	"omnix/genpb/adminpb"
	"omnix/genpb/msgpb"
	"omnix/toolkit/as"
	"omnix/toolkit/kitctx"
	"omnix/toolkit/stdfmt"
)

// 商标列表数据查询 接口 20250827
func (r *TrademarkService) QueryTrademarkList(
	c context.Context, request *connect.Request[adminpb.QueryTrademarkListRequest],
) (*connect.Response[adminpb.QueryTrademarkListResponse], error) {
	var (
		msg = request.Msg
		o   = &adminpb.QueryTrademarkListResponse{Items: make([]*msgpb.RegisteredTrademark, 0)}
	)
	offset, limit, _ := stdfmt.PageSize(msg.GetPageSize())
	db := r.db1c.R()
	q := db.RegisteredTrademark.Query()
	count, err := q.Count(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	trademarkData, err := q.Order(registeredtrademark.ByID()).Limit(limit).Offset(offset).All(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	o.Total = int64(count)
	for _, trademark := range trademarkData {
		newData := as.CommonConvert.RegisteredTrademark_MsgpbRegisteredTrademark(trademark)
		o.Items = append(o.Items, newData)
	}
	
	return connect.NewResponse(o), nil
}
