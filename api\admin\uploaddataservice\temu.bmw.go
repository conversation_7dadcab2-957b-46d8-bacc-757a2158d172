package uploaddataservice

import (
    "context"
    "omnix/ddl/db1/productitem"
    "omnix/genpb/adminpb"
    "omnix/genpb/enumpb"
    "omnix/genpb/msgpb"
    "omnix/internal/temuparse"
    "omnix/state"
    "omnix/toolkit/hashkeys"
    "omnix/toolkit/kitctx"
    stdfmtidgen "omnix/toolkit/stdfmt/idgen"
    "strings"

    "connectrpc.com/connect"
    "github.com/rs/zerolog/log"
    "github.com/tidwall/gjson"
)

// Temu BMW数据上报 接口 20250808
func (r *UploadDataService) TemuBmw(
    c context.Context, request *connect.Request[adminpb.TemuBmwRequest],
) (*connect.Response[adminpb.TemuBmwResponse], error) {
    var (
        msg = request.Msg
        o   = &adminpb.TemuBmwResponse{}
    )
    if !hashkeys.ValidateBmw(msg.GetData(), msg.GetHash(), state.RemoteOptions.HashKeys.Bmw) {
        return nil, kitctx.NewInvalidArgument("校验失败")
    }
    parse := gjson.Parse(msg.GetData())
    dbc := r.db1c.R()
	tx, err := dbc.Tx(c)
	if err != nil {
		return nil, kitctx.NewInternalErr(err)
	}
	defer func() {
		tx.Commit()
	}()
    parse.ForEach(func(_, value gjson.Result) bool {
        item := value.String()
        spec, err := temuparse.ToProductSpec(item)
        if err != nil {
            log.Err(err).Msg("TemuBmw 解析失败")
            return true
        }
        if spec.Sales > 0 {
            o.Sales++
        }
        hash := stdfmtidgen.GenProductHash(enumpb.TARGET_PLATFORM_TEMU, spec.Id)
        err = tx.ProductItem.Create().
			SetItemID(spec.Id).
			SetPlatform(enumpb.TARGET_PLATFORM_TEMU.ToPascal()).
			SetHash(hash).SetData(item).SetFilter(&msgpb.ProductFilter{}).
			SetSpec(spec).
			OnConflictColumns(productitem.FieldHash).DoNothing().Exec(c)
		if err != nil {
			if !strings.Contains(err.Error(), "no rows in result set") {
				o.Error++
			}else{
				o.Skip++
			}
		}else{
			o.Success++
		}
        return true
    })
    return connect.NewResponse(o), nil
}
