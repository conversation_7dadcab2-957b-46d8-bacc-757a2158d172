package uploaddataservice

import (
    "context"
    "omnix/ddl/db1"
    "omnix/ddl/db1/registeredtrademark"
    "omnix/genpb/adminpb"
    "omnix/state"
    "omnix/toolkit/hashkeys"
    "omnix/toolkit/kitctx"

    "connectrpc.com/connect"
    "github.com/qwenode/rr"
    "github.com/tidwall/gjson"
)

// 商标数据上报 接口 20250827
func (r *UploadDataService) Trademark(
    c context.Context, request *connect.Request[adminpb.TrademarkRequest],
) (*connect.Response[adminpb.TrademarkResponse], error) {
    var (
        msg  = request.Msg
        o    = &adminpb.TrademarkResponse{}
        db1c = r.db1c.R()
    )
    if !hashkeys.ValidateBmw(msg.GetData(), msg.GetHash(), state.RemoteOptions.HashKeys.Bmw) {
        return nil, kitctx.NewInvalidArgument("校验失败")
    }
    id := rr.ToInt32(gjson.Get(msg.GetData(), "id").Int())
    if id <= 0 {
        return nil, kitctx.NewInvalidArgument("必须有ID字段,类型为int")
    }
    trademark := db1.RegisteredTrademark{Tid: id}
    err := rr.JsonUnmarshal([]byte(msg.GetData()), &trademark.Data)
    if err != nil {
        return nil, kitctx.NewInvalidArgumentErr(err)
    }
    err = db1c.RegisteredTrademark.Create().
        SetData(trademark.Data).
        SetTid(trademark.Tid).
        OnConflictColumns(registeredtrademark.FieldTid).UpdateData().Exec(c)
    if err != nil {
        return nil, kitctx.NewInternalErr(err)
    }
    return connect.NewResponse(o), nil
}
