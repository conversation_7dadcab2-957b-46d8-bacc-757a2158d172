// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package adminservice

import (
	"github.com/google/wire"
	"omnix/api/admin/authedservice"
	"omnix/api/admin/authservice"
	"omnix/api/admin/materialservice"
	"omnix/api/admin/psdgroupservice"
	"omnix/api/admin/psdservice"
	"omnix/api/admin/temuservice"
	"omnix/api/admin/trademarkservice"
	"omnix/api/admin/uploaddataservice"
	"omnix/provider/db1c"
)

// Injectors from a.wire.gen.go:

// 未登录的后台账号功能 20250827
func GetAuthService() *authservice.AuthService {
	holder := db1c.Get()
	authService := authservice.NewAuthService(holder)
	return authService
}

// 已登录的后台账号管理 20250827
func GetAuthedService() *authedservice.AuthedService {
	holder := db1c.Get()
	authedService := authedservice.NewAuthedService(holder)
	return authedService
}

// 素材管理 20250904
func GetMaterialService() *materialservice.MaterialService {
	holder := db1c.Get()
	materialService := materialservice.NewMaterialService(holder)
	return materialService
}

// PSD模板管理 20250916
func GetPsdService() *psdservice.PsdService {
	psdService := psdservice.NewPsdService()
	return psdService
}

// PSD分组管理 20250910
func GetPsdGroupService() *psdgroupservice.PsdGroupService {
	holder := db1c.Get()
	psdGroupService := psdgroupservice.NewPsdGroupService(holder)
	return psdGroupService
}

// Temu服务 20250827
func GetTemuService() *temuservice.TemuService {
	holder := db1c.Get()
	temuService := temuservice.NewTemuService(holder)
	return temuService
}

// 商标管理 20250808
func GetTrademarkService() *trademarkservice.TrademarkService {
	holder := db1c.Get()
	trademarkService := trademarkservice.NewTrademarkService(holder)
	return trademarkService
}

// 采集数据上报 20250808
func GetUploadDataService() *uploaddataservice.UploadDataService {
	holder := db1c.Get()
	uploadDataService := uploaddataservice.NewUploadDataService(holder)
	return uploadDataService
}

// a.wire.gen.go:

var ProviderSet = wire.NewSet(authservice.ProviderSet, authedservice.ProviderSet, materialservice.ProviderSet, psdservice.ProviderSet, psdgroupservice.ProviderSet, temuservice.ProviderSet, trademarkservice.ProviderSet, uploaddataservice.ProviderSet)
