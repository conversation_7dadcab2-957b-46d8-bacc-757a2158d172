package flowservice

import (
    "context"
    "omnix/genpb/enumpb"
    "omnix/genpb/flowpb"
    "omnix/types"

    "github.com/rs/zerolog/log"
    "go.temporal.io/sdk/client"
    "go.temporal.io/sdk/worker"
)

func Register(w worker.Registry) {

    // 全局锁 20250910
    GetLockerWorkflow().Register(w)
    GetLockerActivity().Register(w)

    GetTemuWorkflow().Register(w)
    GetTemuActivity().Register(w)

}

// 涉及到对外出口ip,流量费用等问题,可以把这类工作放到其他限制较少的服务器执行 20250902
func RegisterComfyui(w worker.Registry) {
    // ps自动化 20250910
    GetPhotoshopWorkflow().Register(w)
    GetPhotoshopActivity().Register(w)

    GetComfyuiWorkflow().Register(w)
    GetComfyuiActivity().Register(w)
}

// 注册定时任务 20250903
func RegisterSchedule(cli client.Client) {
    defer cli.Close()
    c := context.Background()
    list, err := cli.ScheduleClient().List(
        c, client.ScheduleListOptions{
            PageSize: 1000,
        },
    )
    if err != nil {
        log.Err(err).Msg("计划列表读取失败")
        return
    }
    schedules := map[string]types.ScheduleFunc{

        "定时自动抠图": func(name string) client.ScheduleOptions {
            return client.ScheduleOptions{
                ID: name,
                Spec: client.ScheduleSpec{
                    CronExpressions: []string{
                        "30 * * * *",
                    },
                },
                Action: &client.ScheduleWorkflowAction{
                    ID:        name,
                    Workflow:  flowpb.BatchKouKouTemuWorkflowName,
                    TaskQueue: enumpb.FLOW_QUEUE_DEFAULT.ToState(),
                    Args: []any{
                        &flowpb.BatchKouKouTemuRequest{Concurrent: 3},
                    },
                },
            }
        },
    }
    exist := map[string]bool{}
    for list.HasNext() {
        next, err := list.Next()
        if err != nil {
            break
        }
        exist[next.ID] = true
    }
    for name, f := range schedules {
        _, ok := exist[name]
        if ok {
            continue
        }
        scheduleOptions := f(name)
        create, err := cli.ScheduleClient().Create(c, scheduleOptions)
        if err != nil {
            log.Err(err).Msg("任务创建失败")
            continue
        }
        log.Info().Str("Name", name).Str("ID", create.GetID()).Msg("计划任务创建成功")
    }
}
