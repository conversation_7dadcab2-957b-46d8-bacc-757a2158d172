// Code generated by proto-gen-gg. DO NOT EDIT.

//go:build wireinject
// +build wireinject

//
package flowservice

import "github.com/google/wire"

import (
	"omnix/api/flow/actionlogflow"
	"omnix/api/flow/comfyuiflow"
	"omnix/api/flow/lockerflow"
	"omnix/api/flow/photoshopflow"
	"omnix/api/flow/temuflow"
)

//go:generate go-generate-fast ./actionlogflow/a.actionlogflow.init.go
//go:generate go-generate-fast ./comfyuiflow/a.comfyuiflow.init.go
//go:generate go-generate-fast ./lockerflow/a.lockerflow.init.go
//go:generate go-generate-fast ./photoshopflow/a.photoshopflow.init.go
//go:generate go-generate-fast ./temuflow/a.temuflow.init.go

func GetActionLogWorkflow() *actionlogflow.ActionLogWorkflow {
	wire.Build(AdditionsSet)
	return nil
}

func GetActionLogActivity() *actionlogflow.ActionLogActivity {
	wire.Build(AdditionsSet)
	return nil
}

// comfyui功能 20250901
func GetComfyuiWorkflow() *comfyuiflow.ComfyuiWorkflow {
	wire.Build(AdditionsSet)
	return nil
}

// comfyui功能 20250901
func GetComfyuiActivity() *comfyuiflow.ComfyuiActivity {
	wire.Build(AdditionsSet)
	return nil
}

// 全局锁 20250910
func GetLockerWorkflow() *lockerflow.LockerWorkflow {
	wire.Build(AdditionsSet)
	return nil
}

// 全局锁 20250910
func GetLockerActivity() *lockerflow.LockerActivity {
	wire.Build(AdditionsSet)
	return nil
}

// Ps自动化 20250910
func GetPhotoshopWorkflow() *photoshopflow.PhotoshopWorkflow {
	wire.Build(AdditionsSet)
	return nil
}

// Ps自动化 20250910
func GetPhotoshopActivity() *photoshopflow.PhotoshopActivity {
	wire.Build(AdditionsSet)
	return nil
}

func GetTemuWorkflow() *temuflow.TemuWorkflow {
	wire.Build(AdditionsSet)
	return nil
}

func GetTemuActivity() *temuflow.TemuActivity {
	wire.Build(AdditionsSet)
	return nil
}

var ProviderSet = wire.NewSet(
	actionlogflow.ProviderSet,
	comfyuiflow.ProviderSet,
	lockerflow.ProviderSet,
	photoshopflow.ProviderSet,
	temuflow.ProviderSet,
)
