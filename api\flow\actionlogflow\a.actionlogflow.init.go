package actionlogflow

import (
	"go.temporal.io/sdk/worker"
	"omnix/genpb/flowpb"
)

//go:generate gg -sets=true

// Workflow注册结构体
// newc
type ActionLogWorkflow struct{}

// Activity结构体
// newc
type ActionLogActivity struct {
	// inject services here
}

// 注册Workflow到队列
func (r *ActionLogWorkflow) Register(w worker.Registry) {
	flowpb.RegisterActionLogFlowWorkflows(w, r)

}

// 注册Activity到队列
func (r *ActionLogActivity) Register(w worker.Registry) {
	flowpb.RegisterActionLogFlowActivities(w, r)
}
