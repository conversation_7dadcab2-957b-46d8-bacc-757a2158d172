// Code generated newc; DO NOT EDIT.

package actionlogflow

import (
    "github.com/google/wire"
)

// NewActionLogWorkflow Create a new ActionLogWorkflow
func NewActionLogWorkflow() *ActionLogWorkflow {
    s := &ActionLogWorkflow{}
    return s
}

// NewActionLogActivity Create a new ActionLogActivity
func NewActionLogActivity() *ActionLogActivity {
    s := &ActionLogActivity{}
    return s
}

var ProviderSet = wire.NewSet(
    NewActionLogWorkflow,
    NewActionLogActivity,
)
