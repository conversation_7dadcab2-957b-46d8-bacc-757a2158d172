package actionlogflow

import (
	"go.temporal.io/sdk/workflow"
	"omnix/genpb/flowpb"
)

type writeActionLogWorkflow struct {
	input *flowpb.WriteActionLogWorkflowInput
}

// 入口配置
func (r *ActionLogWorkflow) WriteActionLog(c workflow.Context, input *flowpb.WriteActionLogWorkflowInput) (flowpb.WriteActionLogWorkflow, error) {

	if input.Req == nil {
		input.Req = &flowpb.WriteActionLogRequest{}
	}

	return &writeActionLogWorkflow{input: input}, nil
}

// 记录操作日志 接口 20250626
func (r *writeActionLogWorkflow) Execute(c workflow.Context) (*flowpb.WriteActionLogResponse, error) {

	var (
		msg = r.input.Req
	)

	println(msg)

	//TODO implement me
	panic("implement me")
}
