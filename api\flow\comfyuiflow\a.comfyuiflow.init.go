package comfyuiflow

import (
	"omnix/genpb/flowpb"
	"omnix/provider/b2"

	"go.temporal.io/sdk/worker"
)

// comfyui功能 20250901
// Workflow注册结构体
// newc
//
//go:generate gg -sets=true
type ComfyuiWorkflow struct{}

// comfyui功能 20250901
// Activity结构体
// newc
type ComfyuiActivity struct {
	b2 *b2.Holder
}

// 注册Workflow到队列
func (r *ComfyuiWorkflow) Register(w worker.Registry) {
	flowpb.RegisterComfyuiFlowWorkflows(w, r)

}

// 注册Activity到队列
func (r *ComfyuiActivity) Register(w worker.Registry) {
	flowpb.RegisterComfyuiFlowActivities(w, r)
}
