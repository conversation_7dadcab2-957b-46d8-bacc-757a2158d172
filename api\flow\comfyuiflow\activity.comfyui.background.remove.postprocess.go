package comfyuiflow

import (
    "bytes"
    "context"
    "fmt"
    "omnix/genpb/flowpb"
    "omnix/state"
    stdfmtb2path "omnix/toolkit/stdfmt/b2path"
    stdfmterror "omnix/toolkit/stdfmt/error"
    "omnix/toolkit/wrapper/kitb2"
    "omnix/toolkit/wrapper/kitcomfyui"
    "omnix/toolkit/wrapper/kitimg"
    "strings"
    "time"

    "github.com/qwenode/rr"
    "github.com/tidwall/gjson"
    "go.temporal.io/sdk/activity"
)

// 等待抠图完成>下载图片>上传到s3>返回s3链接 Activity 20250903
func (r *ComfyuiActivity) ComfyuiBackgroundRemovePostprocess(c context.Context, request *flowpb.ComfyuiBackgroundRemovePostprocessRequest) (*flowpb.ComfyuiBackgroundRemovePostprocessResponse, error) {

    var (
        o      = &flowpb.ComfyuiBackgroundRemovePostprocessResponse{}
        client = kitcomfyui.New(request.GetAdapterHost())
        idText = " 任务ID: " + request.GetPromptId()
    )
    // 查询任务是否在等待或执行中 20250903
    for {
        brk := false
        select {
        case <-c.Done():
            return nil, c.Err()
        default:
            activity.RecordHeartbeat(c, "查询是否队列中"+idText)
            queue, err := client.IsTaskInQueue(request.GetPromptId())
            if err != nil {
                return nil, err
            }
            if !queue {
                brk = true
            }
            time.Sleep(time.Second * 3)
        }
        if brk {
            break
        }
    }
    outputFileName := ""
    // 查询任务结果是否完成 20250903
    for {
        brk := false
        select {
        case <-c.Done():
            return nil, c.Err()
        default:
            activity.RecordHeartbeat(c, "查询任务结果"+idText)
            historyBody, err := client.GetHistoryList()
            if err != nil {
                return nil, err
            }
            outputFile, err := getOutputFile(request.GetPromptId(), historyBody)

            if err != nil {
                return stdfmterror.MustHaltAndHint(o, err.Error()), nil
            }
            if outputFile != "" {
                outputFileName = outputFile
                brk = true
            }

        }
        if brk {
            break
        }
        time.Sleep(time.Second * 3)
    }
    activity.RecordHeartbeat(c, "下载图片中..."+idText)
    imageBytes, err := client.DownloadFileToBytes(outputFileName)
    if err != nil {
        return nil, err
    }
    extension, contentType, err := kitimg.GetImageExtension(imageBytes)
    if err != nil {
        return stdfmterror.MustHaltAndHint(o, err.Error()), err
    }
    // 上传到S3 20250903
    activity.RecordHeartbeat(c, "上传图片到S3"+idText)
    adapter := kitb2.New(c, r.b2, state.RemoteOptions.Backblaze.BucketName)
    sha1 := rr.BytesSha1(imageBytes)
    b2Path := fmt.Sprintf("processed/koukou/%s.%s", sha1, extension)
    o.SetSha1(sha1)
    _, err = adapter.UploadBuffer(b2Path, bytes.NewReader(imageBytes), int64(len(imageBytes)), contentType)
    if err != nil {
        return stdfmterror.MustHaltAndHint(o, err.Error()), err
    }
    o.S3Url = stdfmtb2path.GetVisitUrl(b2Path)
    _, _ = client.DeleteFile(outputFileName)
    return o, nil
}
func getOutputFile(promptId, body string) (_file string, _err error) {
    taskBody := gjson.Get(body, promptId)
    if !taskBody.Exists() {
        return "", fmt.Errorf("prompt %s failed: %s", promptId, "在历史中没找到任务")
    }
    if taskBody.Get("status.status_str").String() != "success" {
        return "", fmt.Errorf("prompt %s failed: %s", promptId, taskBody.Get("status.status_str").String())
    }
    var (
        ctn      = true
        fileName = ""
    )
    taskBody.ForEach(func(key, value gjson.Result) bool {
        for _, result := range value.Map() {
            result.Get("images").ForEach(func(key, value gjson.Result) bool {
                s2 := value.Get("filename").String()
                if s2 != "" && strings.HasPrefix(s2, "aikoukou") {
                    fileName = s2
                    ctn = false
                    return false
                }
                return ctn
            })
            if ctn == false {
                break
            }
        }
        return ctn
    })
    return fileName, nil
}
