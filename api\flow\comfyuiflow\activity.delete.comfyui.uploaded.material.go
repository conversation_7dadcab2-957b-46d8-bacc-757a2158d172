package comfyuiflow

import (
    "context"
    "omnix/genpb/flowpb"
    "omnix/toolkit/wrapper/kitcomfyui"
)

// 删除上传到Comfyui的素材文件 接口 20250903
func (r *ComfyuiActivity) DeleteComfyuiUploadedMaterial(c context.Context, request *flowpb.DeleteComfyuiUploadedMaterialRequest) (*flowpb.DeleteComfyuiUploadedMaterialResponse, error) {
    client := kitcomfyui.New(request.GetAdapterHost())
    _, err := client.DeleteUploadFile(request.GetFilename())
    return &flowpb.DeleteComfyuiUploadedMaterialResponse{}, err
}
