package comfyuiflow

import (
    "bytes"
    "context"
    "errors"
    "fmt"
    "image"
    "io"
    "net/http"
    "omnix/genpb/flowpb"
    "omnix/toolkit/kitreq"
    stdfmterror "omnix/toolkit/stdfmt/error"
    "omnix/toolkit/wrapper/kitcomfyui"
    "os"
    "path/filepath"

    "github.com/disintegration/imaging"
    "github.com/qwenode/rr"
    "github.com/qwenode/rr/random"
    "go.temporal.io/sdk/activity"
)

// 下载URL资源并上传到Comfyui Activity 20250902
func (r *ComfyuiActivity) DownUrlAndUploadToComfyui(c context.Context, request *flowpb.DownUrlAndUploadToComfyuiRequest) (*flowpb.DownUrlAndUploadToComfyuiResponse, error) {
    if request.GetUrl() == "" {
        return nil, errors.New("empty url")
    }
    var (
        o            = &flowpb.DownUrlAndUploadToComfyuiResponse{}
        sha1FileName = rr.StringSha1(request.GetUrl()) + "." + rr.StringGetExtension(request.GetUrl())
        client       = kitreq.DefaultResty.R()
        // 加随机数,防止同名文件并发问题 20250902
        tempFile = filepath.Join(os.TempDir(), random.String(8)+"_"+sha1FileName)
    )
    defer func() {
        if rr.FileExist(tempFile) {
            os.Remove(tempFile)
        }
    }()
    client.SetOutput(tempFile)
    client.SetContext(c)
    activity.RecordHeartbeat(c, "正在下载URL数据")
    response, err := client.Get(request.GetUrl())
    if err != nil {
        return o, err
    }
    switch response.StatusCode() {
    case http.StatusRequestTimeout:
        return o, errors.New("request timeout")
    case http.StatusGatewayTimeout:
        return o, errors.New("gateway timeout")
    }
    if !response.IsSuccess() {
        return stdfmterror.MustHaltAndHint(o, fmt.Sprintf("图片下载失败: %s", response.Status())), nil
    }
    if !rr.FileExist(tempFile) {
        return o, errors.New("请求已经成功,但是文件不存在")
    }

    // 检查并调整图片尺寸
    activity.RecordHeartbeat(c, "正在检查图片尺寸")
    imageReader, err := r.resizeImageIfNeeded(tempFile)
    if err != nil {
        return stdfmterror.MustHaltAndHint(o, fmt.Sprintf("图片尺寸调整失败: %v", err)), nil
    }

    capi := kitcomfyui.New(request.GetAdapterHost())
    activity.RecordHeartbeat(c, "正在上传到ComfyUI服务器")
    uploadedFile, err := capi.UploadFile(filepath.Base(tempFile), imageReader)
    if err != nil {
        return o, err
    }
    o.Filename = uploadedFile.Filename
    return o, nil
}

// resizeImageIfNeeded 检查图片尺寸，如果宽或高大于1200px则按比例缩小到1200px，返回io.Reader
func (r *ComfyuiActivity) resizeImageIfNeeded(filePath string) (io.Reader, error) {
    // 读取文件内容到内存
    fileData, err := os.ReadFile(filePath)
    if err != nil {
        return nil, fmt.Errorf("无法读取文件: %w", err)
    }

    // 创建Reader用于解码配置
    reader := bytes.NewReader(fileData)

    // 解码图片获取配置信息
    config, format, err := image.DecodeConfig(reader)
    if err != nil {
        // 如果不是图片文件，直接返回原始内容
        return bytes.NewReader(fileData), nil
    }

    // 检查是否需要调整尺寸
    maxSize := 1200
    if config.Width <= maxSize && config.Height <= maxSize {
        // 不需要调整尺寸，返回原始内容
        return bytes.NewReader(fileData), nil
    }

    // 重新创建Reader进行图片解码
    reader = bytes.NewReader(fileData)
    img, err := imaging.Decode(reader)
    if err != nil {
        return nil, fmt.Errorf("无法解码图片: %w", err)
    }

    // 使用imaging.Resize的比例缩放特性
    var resizedImg image.Image
    if config.Width > config.Height {
        // 宽度较大，以宽度为准，高度传0自动按比例计算
        resizedImg = imaging.Resize(img, maxSize, 0, imaging.Lanczos)
    } else {
        // 高度较大，以高度为准，宽度传0自动按比例计算
        resizedImg = imaging.Resize(img, 0, maxSize, imaging.Lanczos)
    }

    // 将调整后的图片编码到内存缓冲区
    var buf bytes.Buffer
    switch format {
    case "jpeg":
        err = imaging.Encode(&buf, resizedImg, imaging.JPEG)
    case "png":
        err = imaging.Encode(&buf, resizedImg, imaging.PNG)
    case "gif":
        err = imaging.Encode(&buf, resizedImg, imaging.GIF)
    default:
        // 默认使用JPEG格式
        err = imaging.Encode(&buf, resizedImg, imaging.JPEG)
    }

    if err != nil {
        return nil, fmt.Errorf("无法编码调整后的图片: %w", err)
    }

    return bytes.NewReader(buf.Bytes()), nil
}
