package comfyuiflow

import (
    "context"
    "fmt"
    "omnix/genpb/flowpb"
    "omnix/state"
    "omnix/toolkit/wrapper/kitcomfyui"
    "time"

    "github.com/rs/zerolog/log"
    "github.com/tidwall/gjson"
    "go.temporal.io/sdk/activity"
)

// 查询可用的Comfyui服务器 Activity 20250902
func (r *ComfyuiActivity) QueryAvailableComfyuiServer(c context.Context, request *flowpb.QueryAvailableComfyuiServerRequest) (*flowpb.QueryAvailableComfyuiServerResponse, error) {
    var (
        o = &flowpb.QueryAvailableComfyuiServerResponse{}
    )

    for {
        for _, host := range state.RemoteOptions.ComfyuiAdapter.Hosts {
            activity.RecordHeartbeat(c, "检查服务器是否可用:"+host)
            client := kitcomfyui.New(host)
            body, err := client.QueryQueueList()
            if err != nil {
                continue
            }
            parse := gjson.Parse(body)
            if len(parse.Get("queue_pending").Array()) > 0 {
                continue
            }
            workflow, err := client.HasWorkflow(request.GetName())
            if err != nil {
                continue
            }
            if workflow {
                //return stdfmterror.MustHaltAndHint(o,"指定的工作流不存在:"+request.GetName()),nil
                o.AdapterHost = host
                return o, nil
            }else{
                log.Err(fmt.Errorf("指定的工作流不存在: %s",request.GetName()))
            }
        }
        time.Sleep(3 * time.Second)
    }
}
