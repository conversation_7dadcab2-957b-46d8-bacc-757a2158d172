package comfyuiflow

import (
	"context"
	"omnix/genpb/flowpb"
	"omnix/toolkit/wrapper/kitcomfyui"

	"github.com/tidwall/gjson"
)

// 获取Comfyui队列大小 Activity 20250901
func (r *ComfyuiActivity) QueryComfyuiQueueSize(c context.Context, request *flowpb.QueryComfyuiQueueSizeRequest) (*flowpb.QueryComfyuiQueueSizeResponse, error) {
	o:=&flowpb.QueryComfyuiQueueSizeResponse{}
	client := kitcomfyui.New(request.GetAdapterHost())
	body, err := client.QueryQueueList()
	if err != nil {
		return nil, err
	}
	parseBytes := gjson.Parse(body)
	o.Running = int32(len(parseBytes.Get("queue_running").Array()))
	o.Pending = int32(len(parseBytes.Get("queue_pending").Array()))
	return o, nil
}
