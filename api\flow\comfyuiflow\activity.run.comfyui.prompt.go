package comfyuiflow

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"omnix/genpb/flowpb"
	"omnix/toolkit/wrapper/kitcomfyui"

	"github.com/tidwall/gjson"
)

// 执行Comfyui任务 Activity 20250903
func (r *ComfyuiActivity) RunComfyuiPrompt(c context.Context, request *flowpb.RunComfyuiPromptRequest) (*flowpb.RunComfyuiPromptResponse, error) {
	var (
		o = &flowpb.RunComfyuiPromptResponse{}
	)
	data:=map[string]json.RawMessage{
		"prompt":[]byte(request.Prompt),
	}
	client := kitcomfyui.New(request.GetAdapterHost())
	response, err := client.ProxyPostJSON("/api/prompt",data, nil)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}
	promptId := gjson.GetBytes(body, "prompt_id").String()
	if promptId=="" {
		return nil,fmt.Errorf("任务执行失败: %s",string(body))
	}
	o.PromptId = promptId
	return o, nil
}
