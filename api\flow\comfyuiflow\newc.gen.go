// Code generated newc; DO NOT EDIT.

package comfyuiflow

import (
    "omnix/provider/b2"

    "github.com/google/wire"
)

// NewComfyuiWorkflow Create a new ComfyuiWorkflow
func NewComfyuiWorkflow() *ComfyuiWorkflow {
    s := &ComfyuiWorkflow{}
    return s
}

// NewComfyuiActivity Create a new ComfyuiActivity
func NewComfyuiActivity(b2 *b2.Holder) *ComfyuiActivity {
    s := &ComfyuiActivity{
        b2: b2,
    }
    return s
}

var ProviderSet = wire.NewSet(
    NewComfyuiWorkflow,
    NewComfyuiActivity,
)
