package comfyuiflow

import (
    "omnix/genpb/flowpb"
    stdfmterror "omnix/toolkit/stdfmt/error"
    kitcomfyuiprompt "omnix/toolkit/wrapper/kitcomfyui/prompt"

    "github.com/pkg/errors"
    "go.temporal.io/sdk/workflow"
)

type comfyuiBackgroundRemoveWorkflow struct {
    input *flowpb.ComfyuiBackgroundRemoveWorkflowInput
}

// 入口配置
func (r *ComfyuiWorkflow) ComfyuiBackgroundRemove(c workflow.Context, input *flowpb.ComfyuiBackgroundRemoveWorkflowInput) (flowpb.ComfyuiBackgroundRemoveWorkflow, error) {

    if input.Req == nil {
        input.Req = &flowpb.ComfyuiBackgroundRemoveRequest{}
    }

    return &comfyuiBackgroundRemoveWorkflow{input: input}, nil
}

// Comfyui一键抠图 任务编排 20250901
func (r *comfyuiBackgroundRemoveWorkflow) Execute(c workflow.Context) (*flowpb.ComfyuiBackgroundRemoveResponse, error) {

    var (
        msg = r.input.Req
        o   = &flowpb.ComfyuiBackgroundRemoveResponse{}
    )
    // 获取可用的comfyui服务器 20250902
    comfyuiServer, err := flowpb.QueryAvailableComfyuiServer(c, &flowpb.QueryAvailableComfyuiServerRequest{
        Name: kitcomfyuiprompt.AI一顿猛抠,
    })
    if err != nil {
        return o, err
    }
    if stdfmterror.CheckHaltAndTransfer(comfyuiServer, o) {
        return o, nil
    }
    uploadResult, err := flowpb.DownUrlAndUploadToComfyui(c, &flowpb.DownUrlAndUploadToComfyuiRequest{
        Url:         msg.GetUrl(),
        AdapterHost: comfyuiServer.AdapterHost,
    })

    if err != nil {
        return o, err
    }
    if stdfmterror.CheckHaltAndTransfer(uploadResult, o) {
        return o, nil
    }
    // 清理资源 20250903
    defer func() {
        _, _ = flowpb.DeleteComfyuiUploadedMaterial(c, &flowpb.DeleteComfyuiUploadedMaterialRequest{
            Filename:    uploadResult.GetFilename(),
            AdapterHost: comfyuiServer.GetAdapterHost(),
        })
    }()
    prompt, err := kitcomfyuiprompt.GenAI一顿猛抠Prompt(uploadResult.Filename)
    if err != nil {
        return o, err
    }
    // 执行抠图 20250902
    runPromptResponse, err := flowpb.RunComfyuiPrompt(c, &flowpb.RunComfyuiPromptRequest{
        Prompt:      prompt,
        AdapterHost: comfyuiServer.AdapterHost,
    })
    if err != nil {
        return o, err
    }
    if stdfmterror.CheckHaltAndTransfer(runPromptResponse, o) {
        return o, errors.New(runPromptResponse.GetFaultMessage().GetHint())
    }
    postprocess, err := flowpb.ComfyuiBackgroundRemovePostprocess(c, &flowpb.ComfyuiBackgroundRemovePostprocessRequest{
        PromptId:    runPromptResponse.GetPromptId(),
        AdapterHost: comfyuiServer.AdapterHost,
    })
    if err != nil {
        return o, err
    }
    if stdfmterror.CheckHaltAndTransfer(postprocess, o) {
        return o, errors.New(postprocess.GetFaultMessage().GetHint())
    }
    o.SetSha1(postprocess.GetSha1())
    o.SetUrl(postprocess.GetS3Url())
    return o, nil
}
