package lockerflow

import (
    "omnix/genpb/flowpb"
    "omnix/provider/flow"

    "go.temporal.io/sdk/worker"
)

// 全局锁 20250910
// Workflow注册结构体
// newc
//
//go:generate gg -sets=true
type LockerWorkflow struct{}

// 全局锁 20250910
// Activity结构体
// newc
type LockerActivity struct {
    flowc *flow.Holder
}

// 注册Workflow到队列
func (r *LockerWorkflow) Register(w worker.Registry) {
    flowpb.RegisterLockerFlowWorkflows(w, r)

}

// 注册Activity到队列
func (r *LockerActivity) Register(w worker.Registry) {
    flowpb.RegisterLockerFlowActivities(w, r)
}
