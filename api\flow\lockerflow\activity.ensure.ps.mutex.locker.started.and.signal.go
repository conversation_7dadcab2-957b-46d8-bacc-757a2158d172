package lockerflow

import (
    "context"
    "omnix/genpb/flowpb"

    "github.com/qwenode/rr"
)

// 获取PsMutexLocker锁 Activity 20250910
func (r *LockerActivity) EnsurePsMutexLockerStartedAndSignal(c context.Context, request *flowpb.EnsurePsMutexLockerStartedAndSignalRequest) (*flowpb.EnsurePsMutexLockerStartedAndSignalResponse, error) {
    hid := rr.StringSha1(request.GetAdapterHost())[:8]
    psMutexRequest := &flowpb.PsMutexLockerRequest{Id: hid}
    flowc := r.flowc.R()
    startWorkflowOptions, err := flowpb.NewPsMutexLockerOptions().Build(psMutexRequest.ProtoReflect())
    if err != nil {
        return nil, err
    }
    _, err = flowc.SignalWithStartWorkflow(c, startWorkflowOptions.ID,
        flowpb.PsMutexLockerWorkflowName, request.GetWorkflowId(),
        startWorkflowOptions, flowpb.PsMutexLockerWorkflowName)
    if err != nil {
        return nil, err
    }

    return &flowpb.EnsurePsMutexLockerStartedAndSignalResponse{LockerId: startWorkflowOptions.ID}, nil
}
