// Code generated newc; DO NOT EDIT.

package lockerflow

import (
    "omnix/provider/flow"

    "github.com/google/wire"
)

// NewLockerWorkflow Create a new LockerWorkflow
func NewLockerWorkflow() *LockerWorkflow {
    s := &LockerWorkflow{}
    return s
}

// NewLockerActivity Create a new LockerActivity
func NewLockerActivity(flowc *flow.Holder) *LockerActivity {
    s := &LockerActivity{
        flowc: flowc,
    }
    return s
}

var ProviderSet = wire.NewSet(
    NewLockerWorkflow,
    NewLockerActivity,
)
