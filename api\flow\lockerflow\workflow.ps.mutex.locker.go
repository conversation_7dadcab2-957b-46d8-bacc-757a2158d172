package lockerflow

import (
    "errors"
    "omnix/genpb/flowpb"
    "time"

    "go.temporal.io/sdk/workflow"
)

type psMutexLockerWorkflow struct {
    input *flowpb.PsMutexLockerWorkflowInput
}

// 入口配置
func (r *LockerWorkflow) PsMutexLocker(c workflow.Context, input *flowpb.PsMutexLockerWorkflowInput) (flowpb.PsMutexLockerWorkflow, error) {

    if input.Req == nil {
        input.Req = &flowpb.PsMutexLockerRequest{}
    }

    return &psMutexLockerWorkflow{input: input}, nil
}

// Photoshop全局信号锁 任务编排 20250910
func (r *psMutexLockerWorkflow) Execute(c workflow.Context) (*flowpb.PsMutexLockerResponse, error) {
    receiveChannel := workflow.GetSignalChannel(c, flowpb.PsMutexLockerWorkflowName)
    for {
        if errors.Is(c.Err(), workflow.ErrCanceled) {
            return nil, workflow.ErrCanceled
        }
        var requestWorkflowId string
        b := receiveChannel.Receive(c, &requestWorkflowId)
        if !b {
            workflow.Sleep(c, time.Second*3)
            continue
        }
        var releaseId string
        err := workflow.SideEffect(c, func(ctx workflow.Context) interface{} {
            return "PSRelease/" + requestWorkflowId
        }).Get(&releaseId)
        if err != nil {
            return nil, err
        }
        err = workflow.SignalExternalWorkflow(c, requestWorkflowId, "", flowpb.PsMutexLockerWorkflowName, releaseId).Get(c, nil)
        if err != nil {
            continue
        }

        sel := workflow.NewSelector(c)
        // 拿到锁的程序必须在30秒内完成任务 20250910
        sel.AddFuture(workflow.NewTimer(c, time.Second*30), func(f workflow.Future) {})
        sel.AddReceive(workflow.GetSignalChannel(c, releaseId), func(rc workflow.ReceiveChannel, more bool) {
            rc.Receive(c, nil)
        })
        sel.Select(c)
        if workflow.GetInfo(c).GetCurrentHistoryLength() > 500 {
            return nil, workflow.NewContinueAsNewError(c, flowpb.PsMutexLockerWorkflowName, r.input.Req)
        }
        if errors.Is(c.Err(), workflow.ErrCanceled) {
            return nil, workflow.ErrCanceled
        }

    }
}
