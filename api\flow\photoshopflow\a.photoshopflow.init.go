package photoshopflow

import (
	"go.temporal.io/sdk/worker"
	"omnix/genpb/flowpb"
)

// Ps自动化 20250910
// Workflow注册结构体
// newc
//
//go:generate gg -sets=true
type PhotoshopWorkflow struct{}

// Ps自动化 20250910
// Activity结构体
// newc
type PhotoshopActivity struct {
	// inject services here
}

// 注册Workflow到队列
func (r *PhotoshopWorkflow) Register(w worker.Registry) {
	flowpb.RegisterPhotoshopFlowWorkflows(w, r)

}

// 注册Activity到队列
func (r *PhotoshopActivity) Register(w worker.Registry) {
	flowpb.RegisterPhotoshopFlowActivities(w, r)
}
