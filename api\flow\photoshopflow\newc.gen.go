// Code generated newc; DO NOT EDIT.

package photoshopflow

import (
    "github.com/google/wire"
)

// NewPhotoshopWorkflow Create a new PhotoshopWorkflow
func NewPhotoshopWorkflow() *PhotoshopWorkflow {
    s := &PhotoshopWorkflow{}
    return s
}

// NewPhotoshopActivity Create a new PhotoshopActivity
func NewPhotoshopActivity() *PhotoshopActivity {
    s := &PhotoshopActivity{}
    return s
}

var ProviderSet = wire.NewSet(
    NewPhotoshopWorkflow,
    NewPhotoshopActivity,
)
