package photoshopflow

import (
    "omnix/genpb/flowpb"
    "time"

    "go.temporal.io/sdk/workflow"
)

type psCombineWorkflow struct {
    input *flowpb.PsCombineWorkflowInput
}

// 入口配置
func (r *PhotoshopWorkflow) PsCombine(c workflow.Context, input *flowpb.PsCombineWorkflowInput) (flowpb.PsCombineWorkflow, error) {

    if input.Req == nil {
        input.Req = &flowpb.PsCombineRequest{}
    }

    return &psCombineWorkflow{input: input}, nil
}

// PS合成素材与模板 任务编排 20250910
func (r *psCombineWorkflow) Execute(c workflow.Context) (*flowpb.PsCombineResponse, error) {
    //
    var (
        msg = r.input.Req
        wfi = workflow.GetInfo(c)
    )

    startOpt, err := flowpb.EnsurePsMutexLockerStartedAndSignal(c, &flowpb.EnsurePsMutexLockerStartedAndSignalRequest{
        AdapterHost: msg.GetAdapterHost(),
        WorkflowId:  wfi.WorkflowExecution.ID,
    })
    if err != nil {
        return nil, err
    }
    acquireCh := workflow.GetSignalChannel(c, flowpb.PsMutexLockerWorkflowName)
    releaseId := ""
    acquireCh.Receive(c, &releaseId)

    workflow.Sleep(c, time.Second*25)
    _ = workflow.SignalExternalWorkflow(c, startOpt.GetLockerId(), "", releaseId, nil).Get(c, nil)
    return &flowpb.PsCombineResponse{}, nil
}
