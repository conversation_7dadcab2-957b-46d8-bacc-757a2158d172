package photoshopflow

import (
    "errors"
    "omnix/genpb/flowpb"
    stdfmterror "omnix/toolkit/stdfmt/error"

    "go.temporal.io/sdk/workflow"
)

type psRemoveWhiteBackgroundWorkflow struct {
    input *flowpb.PsRemoveWhiteBackgroundWorkflowInput
}

// 入口配置
func (r *PhotoshopWorkflow) PsRemoveWhiteBackground(c workflow.Context, input *flowpb.PsRemoveWhiteBackgroundWorkflowInput) (flowpb.PsRemoveWhiteBackgroundWorkflow, error) {

    if input.Req == nil {
        input.Req = &flowpb.PsRemoveWhiteBackgroundRequest{}
    }

    return &psRemoveWhiteBackgroundWorkflow{input: input}, nil
}

// Ps白色背景转透明 任务编排 20250910
// 同一个id同一时间只能有一个在运行中, id为 adapter_host的hash
func (r *psRemoveWhiteBackgroundWorkflow) Execute(c workflow.Context) (*flowpb.PsRemoveWhiteBackgroundResponse, error) {

    var (
        msg = r.input.Req
        o   = &flowpb.PsRemoveWhiteBackgroundResponse{}
    )
    if msg.GetUrl() == "" && msg.GetFilename() == "" {
        return nil, errors.New("must specify either url or filename")
    }
    if msg.GetUrl() != "" {
        upload, err := flowpb.PsDownAndUpload(c, &flowpb.PsDownAndUploadRequest{
            Url: msg.GetUrl(),
        })
        if err != nil {
            return o, err
        }
        if stdfmterror.CheckHaltAndTransfer(upload, o) {
            return o, nil
        }
        o.Filename = upload.GetFilename()
    }
    if msg.GetFilename() == "" {
        return nil, errors.New("must specify either filename")
    }
    background, err := flowpb.PsRemoveWhiteBackground(c, &flowpb.PsRemoveWhiteBackgroundRequest{
        Filename:    msg.GetFilename(),
        AdapterHost: msg.GetAdapterHost(),
    })
    if err != nil {
        return o, err
    }
    if stdfmterror.CheckHaltAndTransfer(background, o) {
        return o, nil
    }
    return o, nil
}
