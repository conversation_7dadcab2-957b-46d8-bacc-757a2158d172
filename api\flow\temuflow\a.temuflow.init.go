package temuflow

import (
    "omnix/genpb/flowpb"
    "omnix/provider/db1c"
    "omnix/provider/flow"

    "go.temporal.io/sdk/worker"
)

//go:generate gg -sets=true

// Workflow注册结构体
// newc
type TemuWorkflow struct{}

// Activity结构体
// newc
type TemuActivity struct {
    flowc *flow.Holder
    db1c *db1c.Holder
}

// 注册Workflow到队列
func (r *TemuWorkflow) Register(w worker.Registry) {
	flowpb.RegisterTemuFlowWorkflows(w, r)

}

// 注册Activity到队列
func (r *TemuActivity) Register(w worker.Registry) {
	flowpb.RegisterTemuFlowActivities(w, r)
}
