package temuflow

import (
    "context"
    "omnix/genpb/flowpb"
    stdfmterror "omnix/toolkit/stdfmt/error"
    "time"

    "go.temporal.io/api/enums/v1"
    "go.temporal.io/sdk/activity"
)

// 执行temu抠图任务 Activity 20250904
func (r *TemuActivity) ExecKouKouTemu(c context.Context, request *flowpb.ExecKouKouTemuRequest) (*flowpb.ExecKouKouTemuResponse, error) {
    var (
        o = &flowpb.ExecKouKouTemuResponse{}
        flowc = r.flowc.R()
    )
    activity.RecordHeartbeat(c, "发送请求")
    client := flowpb.NewComfyuiFlowClient(flowc)
    async, err := client.ComfyuiBackgroundRemoveAsync(c, &flowpb.ComfyuiBackgroundRemoveRequest{
        Url: request.GetItem().GetSpec().GetFeaturedImage(),
        Id:  request.GetItem().GetItemId(),
    })
    if err != nil {
        return nil, err
    }
    for {
        select {
        case <-c.Done():
            return nil, c.Err()
        default:
            activity.RecordHeartbeat(c, "查询结果")
            workflowInfo, err := flowc.DescribeWorkflow(c, async.ID(), async.RunID())
            if err != nil {
                return nil, err
            }
            switch workflowInfo.Status {
            case enums.WORKFLOW_EXECUTION_STATUS_RUNNING,enums.WORKFLOW_EXECUTION_STATUS_UNSPECIFIED:
                time.Sleep(time.Second*5)
                continue
            }
            response, err := async.Get(c)
            if err != nil {
                return nil, err
            }
            if stdfmterror.CheckHaltAndTransfer(response,o) {
                return o, nil
            }
            o.SetResponse(response)
            return o, nil
        }
    }
}
