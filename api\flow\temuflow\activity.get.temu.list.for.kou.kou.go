package temuflow

import (
    "context"
    "omnix/ddl/db1"
    "omnix/ddl/db1_rawquery"
    "omnix/genpb/flowpb"
    "omnix/genpb/msgpb"
    "omnix/toolkit/as"

    "github.com/qwenode/rr"
)

// 查询待抠图的Temu数据 Activity 20250904
func (r *TemuActivity) GetTemuListForKouKou(c context.Context, request *flowpb.GetTemuListForKouKouRequest) (*flowpb.GetTemuListForKouKouResponse, error) {
    var (
        o  = &flowpb.GetTemuListForKouKouResponse{Items: make([]*msgpb.ProductItem, 0, request.GetCount())}
        bb = r.db1c.GetBobDB()
    )
    rows, err := db1_rawquery.GetProductItemsForKou(rr.ToInt64(request.GetCount())).All(c, bb)
    if err != nil {
        return o, err
    }
    for _, row := range rows {
        item := &db1.ProductItem{
            ID:          row.ID,
            CreatedTime: row.CreatedTime,
            UpdatedTime: row.UpdatedTime,
            Hash:        row.Hash,
            ItemID:      row.ItemID,
            Platform:    row.Platform,
            //Spec:        ,
            //Filter:      nil,
            //Mark:        nil,
            Data: row.Data,
        }
        b, _ := row.Spec.MarshalJSON()
        if b != nil {
            rr.JsonUnmarshal(b, &item.Spec)
        }
        bbb, _ := row.Filter.MarshalJSON()
        if bbb != nil {
            rr.JsonUnmarshal(bbb, &item.Filter)
        }
        b3, _ := row.Mark.MarshalJSON()
        if b3 != nil {
            rr.JsonUnmarshal(b3, &item.Mark)
        }
        o.Items = append(o.Items, as.CommonConvert.ProductItem_MsgpbProductItem(item))
    }
    return o, nil
}
