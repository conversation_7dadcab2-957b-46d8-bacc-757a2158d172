package temuflow

import (
    "context"
    "database/sql"
    "omnix/genpb/enumpb"
    "omnix/genpb/flowpb"
    stdfmtb2path "omnix/toolkit/stdfmt/b2path"
)

// 保存抠图结果到素材库 Activity 20250904
func (r *TemuActivity) SaveTemuKouKou(c context.Context, request *flowpb.SaveTemuKouKouRequest) (*flowpb.SaveTemuKouKouResponse, error) {

    var (
        o              = &flowpb.SaveTemuKouKouResponse{}
        dbc            = r.db1c.R()
        removeResponse = request.GetResponse()
        productItem    = request.GetItem()
    )
    tx, err := dbc.BeginTx(c,&sql.TxOptions{
       
    })
    if err != nil {
        return o, err
    }

    _, err = tx.Material.Create().SetMaterialGroup([]int64{1}).
        SetFlag([]string{}).SetHash(removeResponse.GetSha1()).
        SetSourceTemuID(productItem.GetId()).
        SetPath(stdfmtb2path.RemoveCdnPrefix(removeResponse.GetUrl())).
        SetSourceURL(productItem.GetSpec().GetFeaturedImage()).
        SetTitle(productItem.GetSpec().GetTitle()).Save(c)
    if err != nil {
        tx.Rollback()
        return o, err
    }
    mark := append(productItem.Mark, enumpb.PRODUCT_ITEM_MARK_KOU.ToState())
    _, err = tx.ProductItem.Update().WhereItemID(productItem.GetItemId()).SetMark(mark).Save(c)
    if err != nil {
        tx.Rollback()
        return o, err
    }
    err = tx.Commit()
    return o, err
}
