package temuflow

import (
    "context"
    "omnix/genpb/flowpb"
)

// 更新抠图原材料状态 接口 20250910
func (r *TemuActivity) UpdateTemuStatus(c context.Context, request *flowpb.UpdateTemuStatusRequest) (*flowpb.UpdateTemuStatusResponse, error) {
    var (
        dbc  = r.db1c.R()
        o    = &flowpb.UpdateTemuStatusResponse{}
        mark = request.GetItem().GetMark()
    )
    mark = append(mark, request.GetMark())
    _, err := dbc.ProductItem.Update().WhereItemID(request.GetItem().GetItemId()).SetMark(mark).Save(c)
    if err != nil {
        return nil, err
    }
    return o, nil
}
