// Code generated newc; DO NOT EDIT.

package temuflow

import (
    "omnix/provider/db1c"
    "omnix/provider/flow"

    "github.com/google/wire"
)

// NewTemuWorkflow Create a new TemuWorkflow
func NewTemuWorkflow() *TemuWorkflow {
    s := &TemuWorkflow{}
    return s
}

// NewTemuActivity Create a new TemuActivity
func NewTemuActivity(flowc *flow.Holder, db1c *db1c.Holder) *TemuActivity {
    s := &TemuActivity{
        flowc: flowc,
        db1c:  db1c,
    }
    return s
}

var ProviderSet = wire.NewSet(
    NewTemuWorkflow,
    NewTemuActivity,
)
