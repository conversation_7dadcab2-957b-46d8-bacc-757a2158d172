package temuflow

import (
    "omnix/genpb/enumpb"
    "omnix/genpb/flowpb"
    stdfmterror "omnix/toolkit/stdfmt/error"
    "strings"
    "time"

    "go.temporal.io/sdk/workflow"
)

type batchKouKouTemuWorkflow struct {
    input *flowpb.BatchKouKouTemuWorkflowInput
}

// 入口配置
func (r *TemuWorkflow) BatchKouKouTemu(c workflow.Context, input *flowpb.BatchKouKouTemuWorkflowInput) (flowpb.BatchKouKouTemuWorkflow, error) {

    if input.Req == nil {
        input.Req = &flowpb.BatchKouKouTemuRequest{}
    }
    if input.Req.Concurrent <= 0 {
        input.Req.Concurrent = 1
    }
    return &batchKouKouTemuWorkflow{input: input}, nil
}

// 对Temu数据全量抠图 任务编排 20250904
func (r *batchKouKouTemuWorkflow) Execute(c workflow.Context) (*flowpb.BatchKouKouTemuResponse, error) {

    var (
        msg = r.input.Req
        o   = &flowpb.BatchKouKouTemuResponse{}
    )

    wg := workflow.NewWaitGroup(c)
    semaphore := workflow.NewBufferedChannel(c, int(msg.Concurrent))
    for {
        kouList, err := flowpb.GetTemuListForKouKou(c, &flowpb.GetTemuListForKouKouRequest{Count: msg.GetConcurrent() * 10})
        if err != nil {
            return nil, err
        }
        if len(kouList.GetItems()) <= 0 {
            break
        }
        for _, kItem := range kouList.GetItems() {
            semaphore.Send(c, struct{}{})
            wg.Add(1)
            tItem := kItem
            workflow.Go(c, func(ctx workflow.Context) {
                defer wg.Done()
                defer semaphore.Receive(ctx, nil)
                lowerTitle := strings.ToLower(tItem.GetSpec().GetTitle())
                if strings.Contains(lowerTitle, " sign") ||
                    strings.Contains(lowerTitle, "2d") ||
                    strings.Contains(lowerTitle, "wallpape") ||
                    strings.Contains(lowerTitle, "plate") ||
                    strings.Contains(lowerTitle, "frame") ||
                    strings.Contains(lowerTitle, "plaque") ||
                    strings.Contains(lowerTitle, "poster") ||
                    strings.Contains(lowerTitle, "batteries") ||
                    strings.Contains(lowerTitle, "electri") ||
                    strings.Contains(lowerTitle, "banner") ||
                    strings.Contains(lowerTitle, "handcrafte") ||
                    strings.Contains(lowerTitle, "led") ||
                    strings.Contains(lowerTitle, "plastic") ||
                    strings.Contains(lowerTitle, "lights") ||
                    strings.Contains(lowerTitle, "solar-powered") ||
                    strings.Contains(lowerTitle, "wood") ||
                    strings.Contains(lowerTitle, "custom") ||
                    strings.Contains(lowerTitle, "needed") ||
                    strings.Contains(lowerTitle, "aluminum") ||
                    strings.Contains(lowerTitle, "grommets") ||
                    strings.Contains(lowerTitle, "polyester") ||
                    strings.Contains(lowerTitle, "handmade") ||
                    strings.Contains(lowerTitle, "wooden") ||
                    strings.Contains(lowerTitle, "paper") ||
                    strings.Contains(lowerTitle, " bag") ||
                    strings.Contains(lowerTitle, " hook") ||
                    strings.Contains(lowerTitle, "sticker") ||
                    strings.Contains(lowerTitle, "fabric") ||
                    strings.Contains(lowerTitle, "3d") {
                    _, _ = flowpb.UpdateTemuStatus(ctx, &flowpb.UpdateTemuStatusRequest{Mark: enumpb.PRODUCT_ITEM_MARK_KOU_IGNORE.ToState(), Item: tItem})
                    return
                }
                execKouKouTemu, err2 := flowpb.ExecKouKouTemu(ctx, &flowpb.ExecKouKouTemuRequest{Item: tItem})
                // 这里需要加日志 20250904
                if err2 != nil {
                    o.Failed++
                    return
                }
                // 这里需要加日志 20250904
                if stdfmterror.IsHalt(execKouKouTemu) {
                    o.Failed++
                    // 更新数据库,不重试此错误 20250910
                    _, _ = flowpb.UpdateTemuStatus(ctx, &flowpb.UpdateTemuStatusRequest{Mark: enumpb.PRODUCT_ITEM_MARK_KOU_IGNORE.ToState(), Item: tItem})
                    return
                }
                _, err2 = flowpb.SaveTemuKouKou(ctx, &flowpb.SaveTemuKouKouRequest{Item: tItem, Response: execKouKouTemu.GetResponse()})
                if err2 != nil {
                    o.Failed++
                    return
                }
                o.Success++
            })
        }
        wg.Wait(c)
        if o.Success >= 300 {
            return o, workflow.NewContinueAsNewError(c, flowpb.BatchKouKouTemuWorkflowName, msg)
        }
        workflow.Sleep(c, time.Millisecond*100)
    }
    return o, nil
}
