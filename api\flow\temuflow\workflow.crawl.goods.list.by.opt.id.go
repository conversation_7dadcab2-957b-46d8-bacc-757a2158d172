package temuflow

import (
	"go.temporal.io/sdk/workflow"
	"omnix/genpb/flowpb"
)

type crawlGoodsListByOptIdWorkflow struct {
	input *flowpb.CrawlGoodsListByOptIdWorkflowInput
}

// 入口配置
func (r *TemuWorkflow) CrawlGoodsListByOptId(c workflow.Context, input *flowpb.CrawlGoodsListByOptIdWorkflowInput) (flowpb.CrawlGoodsListByOptIdWorkflow, error) {

	if input.Req == nil {
		input.Req = &flowpb.CrawlGoodsListByOptIdRequest{}
	}

	return &crawlGoodsListByOptIdWorkflow{input: input}, nil
}

// 根据分类ID遍历产品,通过API访问,数据量有限 任务编排 20250807
func (r *crawlGoodsListByOptIdWorkflow) Execute(c workflow.Context) (*flowpb.CrawlGoodsListByOptIdResponse, error) {

	var (
		msg = r.input.Req
	)

	println(msg)

	//TODO implement me
	panic("implement me")
}
