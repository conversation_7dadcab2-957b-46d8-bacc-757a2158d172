// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package flowservice

import (
	"github.com/google/wire"
	"omnix/api/flow/actionlogflow"
	"omnix/api/flow/comfyuiflow"
	"omnix/api/flow/lockerflow"
	"omnix/api/flow/photoshopflow"
	"omnix/api/flow/temuflow"
	"omnix/provider/b2"
	"omnix/provider/db1c"
	"omnix/provider/flow"
)

// Injectors from a.wire.gen.go:

func GetActionLogWorkflow() *actionlogflow.ActionLogWorkflow {
	actionLogWorkflow := actionlogflow.NewActionLogWorkflow()
	return actionLogWorkflow
}

func GetActionLogActivity() *actionlogflow.ActionLogActivity {
	actionLogActivity := actionlogflow.NewActionLogActivity()
	return actionLogActivity
}

// comfyui功能 20250901
func GetComfyuiWorkflow() *comfyuiflow.ComfyuiWorkflow {
	comfyuiWorkflow := comfyuiflow.NewComfyuiWorkflow()
	return comfyuiWorkflow
}

// comfyui功能 20250901
func GetComfyuiActivity() *comfyuiflow.ComfyuiActivity {
	holder := b2.Get()
	comfyuiActivity := comfyuiflow.NewComfyuiActivity(holder)
	return comfyuiActivity
}

// 全局锁 20250910
func GetLockerWorkflow() *lockerflow.LockerWorkflow {
	lockerWorkflow := lockerflow.NewLockerWorkflow()
	return lockerWorkflow
}

// 全局锁 20250910
func GetLockerActivity() *lockerflow.LockerActivity {
	holder := flow.Get()
	lockerActivity := lockerflow.NewLockerActivity(holder)
	return lockerActivity
}

// Ps自动化 20250910
func GetPhotoshopWorkflow() *photoshopflow.PhotoshopWorkflow {
	photoshopWorkflow := photoshopflow.NewPhotoshopWorkflow()
	return photoshopWorkflow
}

// Ps自动化 20250910
func GetPhotoshopActivity() *photoshopflow.PhotoshopActivity {
	photoshopActivity := photoshopflow.NewPhotoshopActivity()
	return photoshopActivity
}

func GetTemuWorkflow() *temuflow.TemuWorkflow {
	temuWorkflow := temuflow.NewTemuWorkflow()
	return temuWorkflow
}

func GetTemuActivity() *temuflow.TemuActivity {
	holder := flow.Get()
	db1cHolder := db1c.Get()
	temuActivity := temuflow.NewTemuActivity(holder, db1cHolder)
	return temuActivity
}

// a.wire.gen.go:

var ProviderSet = wire.NewSet(actionlogflow.ProviderSet, comfyuiflow.ProviderSet, lockerflow.ProviderSet, photoshopflow.ProviderSet, temuflow.ProviderSet)
