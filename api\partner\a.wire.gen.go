// Code generated by proto-gen-gg. DO NOT EDIT.

//go:build wireinject
// +build wireinject

//
package partnerservice

import "github.com/google/wire"

import (
	"omnix/api/partner/temuservice"
)

//go:generate go-generate-fast ./temuservice/a.temuservice.init.go

// Temu服务 20250827
func GetTemuService() *temuservice.TemuService {
	wire.Build(AdditionsSet)
	return nil
}

var ProviderSet = wire.NewSet(
	temuservice.ProviderSet,
)
