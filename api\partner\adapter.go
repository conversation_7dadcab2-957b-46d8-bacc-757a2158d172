package partnerservice

import (
    "net/http"
    "omnix/toolkit/codec"

    "connectrpc.com/connect"
    "github.com/gin-gonic/gin"
)

func LoadRouter(route *gin.Engine) {
    addRouters()
    notAuthR := route.Group(
        "",
        NewCommonMiddleware(),
        NewGinContextAdapterMiddleware(),
    )
    authR := route.Group(
        "",
        NewCommonMiddleware(),
        //NewAuthMiddleware(),
        NewGinContextAdapterMiddleware(),
    )
    for _, s := range services {
        if s.Auth == authGuest {
            notAuthR.Handle(s.Method, s.Path, s.Handler)
        } else {
            authR.Handle(s.Method, s.Path, s.Handler)
        }
    }
    for _, s := range externalServices {
        if s.Auth == authGuest {
            s.Create(notAuthR)
        } else {
            s.Create(authR)
        }
    }
}

type service struct {
    Method  string
    Path    string
    Handler gin.HandlerFunc
    Auth    authType
}
type externalService struct {
    Auth   authType
    Create createExternal
}

var (
    services = make([]service, 0, 50)
    // 20250409 未登录中间件 by Node
    interceptors = []connect.HandlerOption{
        codec.WithProtoJSON(),
        NewValidateInterceptor(),
    }
    externalServices = make([]externalService, 0)
)

type authType int

const (
    // 20250410 要求必须登录 by Node
    authRequired authType = iota
    // 20250410 允许未登录访问 by Node
    authGuest
)

type createService func(mustAppendInterceptors []connect.HandlerOption) (string, http.Handler)

func addService(auth authType, callback createService) {
    p, h := callback(interceptors)
    services = append(
        services, service{
            Method:  http.MethodPost,
            Path:    p + "*any",
            Handler: gin.WrapH(h),
            Auth:    auth,
        },
    )
}

type createExternal func(route *gin.RouterGroup)

func addExternal(auth authType, callback createExternal) {
    externalServices = append(
        externalServices, externalService{
            Auth:   auth,
            Create: callback,
        },
    )
}
