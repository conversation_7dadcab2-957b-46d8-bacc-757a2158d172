// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package partnerservice

import (
	"github.com/google/wire"
	"omnix/api/partner/temuservice"
	"omnix/provider/db1c"
)

// Injectors from a.wire.gen.go:

// Temu服务 20250827
func GetTemuService() *temuservice.TemuService {
	holder := db1c.Get()
	temuService := temuservice.NewTemuService(holder)
	return temuService
}

// a.wire.gen.go:

var ProviderSet = wire.NewSet(temuservice.ProviderSet)
