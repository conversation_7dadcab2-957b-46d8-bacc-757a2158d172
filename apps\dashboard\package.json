{"name": "vime-admin", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "rsbuild build", "check": "biome check --write", "dev": "rsbuild dev --open", "format": "biome format --write", "preview": "rsbuild preview"}, "dependencies": {"@bufbuild/protobuf": "^2.7.0", "@connectrpc/connect": "^2.1.0", "@connectrpc/connect-query": "^2.1.1", "@connectrpc/connect-web": "^2.1.0", "@douyinfe/semi-icons": "^2.85.0", "@douyinfe/semi-icons-lab": "^2.85.0", "@douyinfe/semi-ui": "^2.85.0", "@tailwindcss/postcss": "^4.1.12", "@tanstack/react-query": "^5.85.5", "@tanstack/react-router": "^1.131.28", "ahooks": "^3.9.4", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.6.0", "tailwind-merge": "^3.3.1", "postcss": "^8.5.6", "tw-animate-css": "^1.3.7", "tailwindcss": "^4.1.12", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "^2.2.2", "@rsbuild/core": "^1.5.0", "@rsbuild/plugin-react": "^1.3.5", "@tanstack/react-router-devtools": "^1.131.28", "@tanstack/router-devtools": "^1.131.28", "@tanstack/router-plugin": "^1.131.28", "@types/node": "^22.18.0", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "typescript": "^5.9.2"}}