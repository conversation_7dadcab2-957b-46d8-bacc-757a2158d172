import { defineConfig } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';
import { TanStackRouterRspack } from '@tanstack/router-plugin/rspack'

export default defineConfig({
  plugins: [pluginReact()],
  html:{
    title: 'Dashboard',
  },
  output:{
    distPath:{
      image: 'assets',
      svg: 'assets',
      font: 'assets',
      media: 'assets',
      css: 'assets',
      js: 'assets',
      // root:'../dashboard_dist'
    }
  },
  tools: {
    rspack: {
      plugins: [
        TanStackRouterRspack({ target: 'react', autoCodeSplitting: true }),
      ],
    },
  },
  source: {
    define: {
      'process.env.PUBLIC_API_BASE_URL': JSON.stringify(process.env.PUBLIC_API_BASE_URL),
    },
  },
});
