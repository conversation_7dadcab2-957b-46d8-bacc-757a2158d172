import { RouterProvider } from '@tanstack/react-router'

import './App.css';
import './styles/globals.css';
import { TransportProvider } from '@connectrpc/connect-query';
import { QueryClientProvider } from '@tanstack/react-query';
import {finalTransport, queryClient} from "@/internal/client/transport.ts";
import {router} from "@/internal/router.ts";


const App = () => {
    return (
       <TransportProvider transport={finalTransport}>
           <QueryClientProvider client={queryClient}>
               <RouterProvider router={router} />
           </QueryClientProvider>
       </TransportProvider>
    )
}

export default App
