import React, {useState} from "react";
import {LoginRequest} from "@/api/auth.admin_pb";

interface Props {
   formData: LoginRequest;
   onSubmit: React.FormEventHandler<HTMLFormElement>;
   setForm: (formData: LoginRequest) => void;
   isPending?: boolean;
}

export const LightLogin = ({
                              formData,
                              onSubmit,
                              setForm,
                              isPending,
                           }: Props) => {
   const [showPassword, setShowPassword] = useState(false);
   const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const {name, value} = e.target;
      setForm({...formData, [name]: value});
   };
   return (
      <form onSubmit={onSubmit}>
         <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
            <div
               className="w-full max-w-md bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-100 relative">
               <div
                  className="absolute top-0 left-0 right-0 h-48 bg-gradient-to-b from-blue-100 via-blue-50 to-transparent opacity-40 blur-3xl -mt-20"></div>
               <div className="p-8">
                  <div className="flex flex-col items-center mb-8">
                     <div className="bg-white p-4 rounded-2xl shadow-lg mb-6">
                        <svg
                           width="48"
                           height="48"
                           viewBox="0 0 110 106"
                           fill="none"
                           xmlns="http://www.w3.org/2000/svg"
                        >
                           <path
                              d="M100.83 28.63L66.86 3.95c-7.25-5.26-17.07-5.26-24.35 0L8.54 28.63C1.29 33.89-1.76 43.23 1.01 51.77l12.98 39.93c2.77 8.53 10.72 14.3 19.7 14.3h41.97c8.98 0 16.93-5.76 19.7-14.3l12.98-39.93c2.77-8.53-.28-17.88-7.53-23.14ZM64.81 63.13l-10.13 18.55-10.13-18.55-18.55-10.13 18.55-10.13 10.13-18.55 10.13 18.55 18.55 10.13-18.55 10.13Z"
                              fill="#3B82F6"
                           />
                        </svg>
                     </div>
                     <div className="p-0">
                        <h2 className="text-2xl font-bold text-gray-900 text-center">
                           欢迎回来
                        </h2>
                        <p className="text-center text-gray-500 mt-2">
                           登录以继续访问您的账户
                        </p>
                     </div>
                  </div>
                  
                  <div className="space-y-6 p-0">
                     <div className="space-y-1">
                        <label className="text-sm font-medium text-gray-700">
                           用户名
                        </label>
                        <input
                           name="username"
                           value={formData.username}
                           onChange={onInputChange}
                           className="bg-gray-50 border-gray-200 text-gray-900 placeholder:text-gray-400 h-12 rounded-lg focus-visible:ring-2 focus-visible:ring-blue-500/50 focus:border-blue-500 w-full px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                           placeholder="请输入用户名"
                        />
                     </div>
                     
                     <div className="space-y-1">
                        <div className="flex justify-between items-center">
                           <label className="text-sm font-medium text-gray-700">
                              密码
                           </label>
                          
                        </div>
                        <div className="relative">
                           <input
                              name="password"
                              value={formData.password}
                              onChange={onInputChange}
                              type={showPassword ? "text" : "password"}
                              className="bg-gray-50 border-gray-200 text-gray-900 pr-12 h-12 rounded-lg focus-visible:ring-2 focus-visible:ring-blue-500/50 focus:border-blue-500 w-full px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                              placeholder="请输入密码"
                           />
                           <button
                              type="button"
                              className="absolute right-1 top-1/2 -translate-y-1/2 text-gray-400 hover:text-blue-600 hover:bg-gray-100 inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 px-3"
                              onClick={() => setShowPassword(!showPassword)}
                           >
                              {showPassword ? "隐藏" : "显示"}
                           </button>
                        </div>
                     </div>
                     
                     <button disabled={isPending}
                             className="w-full h-12 bg-gradient-to-t from-blue-600 via-blue-500 to-blue-400 hover:from-blue-700 hover:via-blue-600 hover:to-blue-500 text-white font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md hover:shadow-blue-100 active:scale-[0.98] inline-flex items-center justify-center whitespace-nowrap text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
                        {isPending ? "登录中..." : "登录"}
                     </button>
                     
                     
                     
                  </div>
                  
                  
               </div>
            </div>
         </div>
      </form>
   );
};
