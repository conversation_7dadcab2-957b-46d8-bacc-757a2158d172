import {IconBadge, IconBreadcrumb} from '@douyinfe/semi-icons-lab';
import {ReactNode} from 'react';


// 定义菜单项接口
interface MenuItem {
   itemKey: string;  // 可以是路由路径或者非路由的菜单项（比如父级菜单）
   text: string;
   icon?: ReactNode;
   items?: MenuItem[];
}

// 菜单项配置
export const menuItems: MenuItem[] = [
   {
      itemKey: '/',
      text: '控制台',
      icon: <IconBadge/>
   },
   {
      itemKey: '#material',
      text: '素材管理',
      icon: <IconBreadcrumb/>,
      items: [
         {
            itemKey: '/material/list',
            text: '素材库'
         },
         {
            itemKey: '/material/group',
            text: '素材组'
         }
      ]
   },
   {
      itemKey: '#temu',
      text: 'Temu数据',
      icon: <IconBreadcrumb/>,
      items: [
         {
            itemKey: '/temu/list',
            text: 'Temu产品数据'
         },
         {
            itemKey: '/trademark/list',
            text: '商标数据'
         }
      ]
   },
   // {
   //    itemKey: '#task',
   //    text: '任务平台',
   //    icon: <IconSteps/>,
   //    items: [
   //       {
   //          itemKey: 'task-management',
   //          text: '任务管理',
   //       },
   //       {
   //          itemKey: 'user-task-query',
   //          text: '用户任务查询',
   //       }
   //    ]
   // }
   {
      itemKey: '#psd',
      text: 'psd管理',
      icon: <IconBreadcrumb/>,
      items: [
         {
            itemKey: '/psd/group',
            text: 'psd分组'
         }
      ]
   }
];
