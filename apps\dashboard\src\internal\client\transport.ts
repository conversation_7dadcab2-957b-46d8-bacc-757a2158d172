import { Code } from '@connectrpc/connect';
import { createConnectTransport } from '@connectrpc/connect-web';
import { QueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { authClearUser, authGetToken } from '@/stores/auth.state.ts';
import {router} from "@/internal/router.ts";


export const finalTransport = createConnectTransport({
   baseUrl: process.env.PUBLIC_API_BASE_URL || 'http://127.0.0.1:57040',
   interceptors: [
      // 请求拦截器：添加认证token
      (next) => async (req) => {
         const token = authGetToken();
         if (token) {
            req.header.set('Authorization', `Bearer ${token}`);
         }
         return next(req);
      },
      // 响应拦截器：处理401错误
      (next) => async (req) => {
         try {
            const resp = await next(req);
            return resp;
         } catch (err: any) {
            // 检查是否是401错误
            if (err && err.code === Code.Unauthenticated) {
               // 显示登录失效提示
               authClearUser();
               toast.error('登录已失效，请重新登录');
               router.navigate({
                  to: '/login',
               })
               return new Promise(() => {}); // 阻止后续执行
            }
            // 继续抛出错误，让调用者也能处理
            throw err;
         }
      },
   ],
});

export const queryClient = new QueryClient({
   defaultOptions: {
      queries: {
         retry: (failureCount: number, error: any) => {
            // 如果是认证错误（401），不重试
            // 这是React Query级别的重试策略，响应拦截器已处理认证错误的跳转和清理
            if (error && error.code === Code.Unauthenticated) {
               return false;
            }
            // 其他错误使用默认重试策略（最多2次）
            return failureCount < 2;
         },
      },
   },
});
