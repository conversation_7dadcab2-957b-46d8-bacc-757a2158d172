import type { Timestamp } from "@bufbuild/protobuf/wkt"

/**
 * 格式化 protobuf Timestamp 为指定格式
 * @param timestamp - protobuf Timestamp 对象
 * @param format - 格式化字符串，支持以下占位符：
 *   - YYYY: 四位年份
 *   - MM: 两位月份
 *   - DD: 两位日期
 *   - HH: 两位小时（24小时制）
 *   - mm: 两位分钟
 *   - ss: 两位秒
 * @returns 格式化后的时间字符串，如果 timestamp 为空则返回 '-'
 * 
 * @example
 * formatTimestamp(timestamp, 'YYYY-MM-DD HH:mm:ss') // '2023-12-25 14:30:45'
 * formatTimestamp(timestamp, 'YYYY/MM/DD') // '2023/12/25'
 * formatTimestamp(timestamp, 'MM-DD HH:mm') // '12-25 14:30'
 */
export function formatTimestamp(
  timestamp?: Timestamp,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  if (!timestamp) {
    return '-'
  }

  let timestampSeconds: number = 0
  
  // Handle different types of timestamp.seconds (string, bigint, number)
  if (timestamp.seconds) {
    if (typeof timestamp.seconds === 'string') {
      timestampSeconds = parseInt(timestamp.seconds, 10)
    } else if (typeof timestamp.seconds === 'bigint') {
      timestampSeconds = Number(timestamp.seconds)
    } else {
      timestampSeconds = timestamp.seconds
    }
  }
  
  // Check if timestamp is valid (should be a reasonable Unix timestamp)
  // Unix timestamps should be greater than 946684800 (2000-01-01) for modern applications
  if (timestampSeconds <= 0 || timestampSeconds < 946684800) {
    return '-'
  }

  // Convert protobuf timestamp to JavaScript Date
  // protobuf timestamp.seconds is seconds since epoch
  const date = new Date(timestampSeconds * 1000)

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '-'
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化 protobuf Timestamp 为相对时间
 * @param timestamp - protobuf Timestamp 对象
 * @returns 相对时间字符串，如 '2小时前'、'3天前' 等
 */
export function formatRelativeTime(timestamp?: Timestamp): string {
  if (!timestamp) {
    return '-'
  }

  let timestampSeconds: number = 0
  
  // Handle different types of timestamp.seconds (string, bigint, number)
  if (timestamp.seconds) {
    if (typeof timestamp.seconds === 'string') {
      timestampSeconds = parseInt(timestamp.seconds, 10)
    } else if (typeof timestamp.seconds === 'bigint') {
      timestampSeconds = Number(timestamp.seconds)
    } else {
      timestampSeconds = timestamp.seconds
    }
  }
  
  // Check if timestamp is valid
  if (timestampSeconds <= 0 || timestampSeconds < 946684800) {
    return '-'
  }

  const date = new Date(timestampSeconds * 1000)
  if (isNaN(date.getTime())) {
    return '-'
  }

  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes}分钟前`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}小时前`
  } else if (diffInSeconds < 2592000) {
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}天前`
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000)
    return `${months}个月前`
  } else {
    const years = Math.floor(diffInSeconds / 31536000)
    return `${years}年前`
  }
}

/**
 * 获取标准日期时间格式
 * @param timestamp - protobuf Timestamp 对象
 * @returns 标准格式的日期时间字符串 'YYYY-MM-DD HH:mm:ss'
 */
export function formatStandardDateTime(timestamp?: Timestamp): string {
  return formatTimestamp(timestamp, 'YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取标准日期格式
 * @param timestamp - protobuf Timestamp 对象
 * @returns 标准格式的日期字符串 'YYYY-MM-DD'
 */
export function formatStandardDate(timestamp?: Timestamp): string {
  return formatTimestamp(timestamp, 'YYYY-MM-DD')
}

/**
 * 获取标准时间格式
 * @param timestamp - protobuf Timestamp 对象
 * @returns 标准格式的时间字符串 'HH:mm:ss'
 */
export function formatStandardTime(timestamp?: Timestamp): string {
  return formatTimestamp(timestamp, 'HH:mm:ss')
}

/**
 * 判断timestamp是否为今天
 * @param timestamp - protobuf Timestamp 对象
 * @returns 是否为今天
 */
export function isToday(timestamp?: Timestamp): boolean {
  if (!timestamp) {
    return false
  }

  let timestampSeconds: number = 0
  
  // Handle different types of timestamp.seconds (string, bigint, number)
  if (timestamp.seconds) {
    if (typeof timestamp.seconds === 'string') {
      timestampSeconds = parseInt(timestamp.seconds, 10)
    } else if (typeof timestamp.seconds === 'bigint') {
      timestampSeconds = Number(timestamp.seconds)
    } else {
      timestampSeconds = timestamp.seconds
    }
  }
  
  // Check if timestamp is valid
  if (timestampSeconds <= 0 || timestampSeconds < 946684800) {
    return false
  }

  const date = new Date(timestampSeconds * 1000)
  const today = new Date()
  
  return date.toDateString() === today.toDateString()
}

/**
 * 判断timestamp是否为昨天
 * @param timestamp - protobuf Timestamp 对象
 * @returns 是否为昨天
 */
export function isYesterday(timestamp?: Timestamp): boolean {
  if (!timestamp) {
    return false
  }

  let timestampSeconds: number = 0
  
  // Handle different types of timestamp.seconds (string, bigint, number)
  if (timestamp.seconds) {
    if (typeof timestamp.seconds === 'string') {
      timestampSeconds = parseInt(timestamp.seconds, 10)
    } else if (typeof timestamp.seconds === 'bigint') {
      timestampSeconds = Number(timestamp.seconds)
    } else {
      timestampSeconds = timestamp.seconds
    }
  }
  
  // Check if timestamp is valid
  if (timestampSeconds <= 0 || timestampSeconds < 946684800) {
    return false
  }

  const date = new Date(timestampSeconds * 1000)
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  
  return date.toDateString() === yesterday.toDateString()
}

/**
 * 智能格式化时间：今天显示时间，昨天显示"昨天 时间"，其他显示完整日期时间
 * @param timestamp - protobuf Timestamp 对象
 * @returns 智能格式化的时间字符串
 */
export function formatSmartDateTime(timestamp?: Timestamp): string {
  if (!timestamp) {
    return '-'
  }

  let timestampSeconds: number = 0
  
  // Handle different types of timestamp.seconds (string, bigint, number)
  if (timestamp.seconds) {
    if (typeof timestamp.seconds === 'string') {
      timestampSeconds = parseInt(timestamp.seconds, 10)
    } else if (typeof timestamp.seconds === 'bigint') {
      timestampSeconds = Number(timestamp.seconds)
    } else {
      timestampSeconds = timestamp.seconds
    }
  }
  
  // Check if timestamp is valid
  if (timestampSeconds <= 0 || timestampSeconds < 946684800) {
    return '-'
  }

  if (isToday(timestamp)) {
    return formatTimestamp(timestamp, 'HH:mm:ss')
  } else if (isYesterday(timestamp)) {
    return `昨天 ${formatTimestamp(timestamp, 'HH:mm')}`
  } else {
    return formatTimestamp(timestamp, 'YYYY-MM-DD HH:mm')
  }
}