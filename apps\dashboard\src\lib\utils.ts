import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
   return twMerge(clsx(inputs))
}


import { createRouter, createMemoryHistory } from '@tanstack/react-router'
import { routeTree } from '../routeTree.gen'

export const router = createRouter({
   history: createMemoryHistory(),
   routeTree,
})

// 导出类型（可选）
export type Router = typeof router


