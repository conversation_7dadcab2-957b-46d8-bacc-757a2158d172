/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as GuestRouteImport } from './routes/_guest'
import { Route as AuthedRouteImport } from './routes/_authed'
import { Route as AuthedIndexRouteImport } from './routes/_authed/index'
import { Route as GuestLoginRouteImport } from './routes/_guest/login'
import { Route as AuthedTrademarkListRouteImport } from './routes/_authed/trademark/list'
import { Route as AuthedTemuListRouteImport } from './routes/_authed/temu/list'
import { Route as AuthedPsdGroupRouteImport } from './routes/_authed/psd/group'
import { Route as AuthedMaterialListRouteImport } from './routes/_authed/material/list'
import { Route as AuthedMaterialGroupRouteImport } from './routes/_authed/material/group'

const GuestRoute = GuestRouteImport.update({
  id: '/_guest',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedRoute = AuthedRouteImport.update({
  id: '/_authed',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedIndexRoute = AuthedIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthedRoute,
} as any)
const GuestLoginRoute = GuestLoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => GuestRoute,
} as any)
const AuthedTrademarkListRoute = AuthedTrademarkListRouteImport.update({
  id: '/trademark/list',
  path: '/trademark/list',
  getParentRoute: () => AuthedRoute,
} as any)
const AuthedTemuListRoute = AuthedTemuListRouteImport.update({
  id: '/temu/list',
  path: '/temu/list',
  getParentRoute: () => AuthedRoute,
} as any)
const AuthedPsdGroupRoute = AuthedPsdGroupRouteImport.update({
  id: '/psd/group',
  path: '/psd/group',
  getParentRoute: () => AuthedRoute,
} as any)
const AuthedMaterialListRoute = AuthedMaterialListRouteImport.update({
  id: '/material/list',
  path: '/material/list',
  getParentRoute: () => AuthedRoute,
} as any)
const AuthedMaterialGroupRoute = AuthedMaterialGroupRouteImport.update({
  id: '/material/group',
  path: '/material/group',
  getParentRoute: () => AuthedRoute,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof GuestLoginRoute
  '/': typeof AuthedIndexRoute
  '/material/group': typeof AuthedMaterialGroupRoute
  '/material/list': typeof AuthedMaterialListRoute
  '/psd/group': typeof AuthedPsdGroupRoute
  '/temu/list': typeof AuthedTemuListRoute
  '/trademark/list': typeof AuthedTrademarkListRoute
}
export interface FileRoutesByTo {
  '/login': typeof GuestLoginRoute
  '/': typeof AuthedIndexRoute
  '/material/group': typeof AuthedMaterialGroupRoute
  '/material/list': typeof AuthedMaterialListRoute
  '/psd/group': typeof AuthedPsdGroupRoute
  '/temu/list': typeof AuthedTemuListRoute
  '/trademark/list': typeof AuthedTrademarkListRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_authed': typeof AuthedRouteWithChildren
  '/_guest': typeof GuestRouteWithChildren
  '/_guest/login': typeof GuestLoginRoute
  '/_authed/': typeof AuthedIndexRoute
  '/_authed/material/group': typeof AuthedMaterialGroupRoute
  '/_authed/material/list': typeof AuthedMaterialListRoute
  '/_authed/psd/group': typeof AuthedPsdGroupRoute
  '/_authed/temu/list': typeof AuthedTemuListRoute
  '/_authed/trademark/list': typeof AuthedTrademarkListRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/'
    | '/material/group'
    | '/material/list'
    | '/psd/group'
    | '/temu/list'
    | '/trademark/list'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/'
    | '/material/group'
    | '/material/list'
    | '/psd/group'
    | '/temu/list'
    | '/trademark/list'
  id:
    | '__root__'
    | '/_authed'
    | '/_guest'
    | '/_guest/login'
    | '/_authed/'
    | '/_authed/material/group'
    | '/_authed/material/list'
    | '/_authed/psd/group'
    | '/_authed/temu/list'
    | '/_authed/trademark/list'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthedRoute: typeof AuthedRouteWithChildren
  GuestRoute: typeof GuestRouteWithChildren
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_guest': {
      id: '/_guest'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof GuestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed': {
      id: '/_authed'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/': {
      id: '/_authed/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthedIndexRouteImport
      parentRoute: typeof AuthedRoute
    }
    '/_guest/login': {
      id: '/_guest/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof GuestLoginRouteImport
      parentRoute: typeof GuestRoute
    }
    '/_authed/trademark/list': {
      id: '/_authed/trademark/list'
      path: '/trademark/list'
      fullPath: '/trademark/list'
      preLoaderRoute: typeof AuthedTrademarkListRouteImport
      parentRoute: typeof AuthedRoute
    }
    '/_authed/temu/list': {
      id: '/_authed/temu/list'
      path: '/temu/list'
      fullPath: '/temu/list'
      preLoaderRoute: typeof AuthedTemuListRouteImport
      parentRoute: typeof AuthedRoute
    }
    '/_authed/psd/group': {
      id: '/_authed/psd/group'
      path: '/psd/group'
      fullPath: '/psd/group'
      preLoaderRoute: typeof AuthedPsdGroupRouteImport
      parentRoute: typeof AuthedRoute
    }
    '/_authed/material/list': {
      id: '/_authed/material/list'
      path: '/material/list'
      fullPath: '/material/list'
      preLoaderRoute: typeof AuthedMaterialListRouteImport
      parentRoute: typeof AuthedRoute
    }
    '/_authed/material/group': {
      id: '/_authed/material/group'
      path: '/material/group'
      fullPath: '/material/group'
      preLoaderRoute: typeof AuthedMaterialGroupRouteImport
      parentRoute: typeof AuthedRoute
    }
  }
}

interface AuthedRouteChildren {
  AuthedIndexRoute: typeof AuthedIndexRoute
  AuthedMaterialGroupRoute: typeof AuthedMaterialGroupRoute
  AuthedMaterialListRoute: typeof AuthedMaterialListRoute
  AuthedPsdGroupRoute: typeof AuthedPsdGroupRoute
  AuthedTemuListRoute: typeof AuthedTemuListRoute
  AuthedTrademarkListRoute: typeof AuthedTrademarkListRoute
}

const AuthedRouteChildren: AuthedRouteChildren = {
  AuthedIndexRoute: AuthedIndexRoute,
  AuthedMaterialGroupRoute: AuthedMaterialGroupRoute,
  AuthedMaterialListRoute: AuthedMaterialListRoute,
  AuthedPsdGroupRoute: AuthedPsdGroupRoute,
  AuthedTemuListRoute: AuthedTemuListRoute,
  AuthedTrademarkListRoute: AuthedTrademarkListRoute,
}

const AuthedRouteWithChildren =
  AuthedRoute._addFileChildren(AuthedRouteChildren)

interface GuestRouteChildren {
  GuestLoginRoute: typeof GuestLoginRoute
}

const GuestRouteChildren: GuestRouteChildren = {
  GuestLoginRoute: GuestLoginRoute,
}

const GuestRouteWithChildren = GuestRoute._addFileChildren(GuestRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthedRoute: AuthedRouteWithChildren,
  GuestRoute: GuestRouteWithChildren,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
