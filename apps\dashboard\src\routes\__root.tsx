import {  Outlet, createRootRoute } from '@tanstack/react-router';
import {Suspense} from "react";
import {TanStackRouterDevtools} from "@tanstack/router-devtools";

export const Route = createRootRoute({
    component: () => (
        <>
            <Outlet />
            {process.env.NODE_ENV === 'development' && (
                <Suspense>
                    <TanStackRouterDevtools />
                </Suspense>
            )}
        </>
    ),
});
