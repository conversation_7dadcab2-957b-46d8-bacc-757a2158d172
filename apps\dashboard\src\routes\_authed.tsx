import {createFileRoute, <PERSON>, Outlet, useNavigate} from '@tanstack/react-router'
import {Button, Dropdown, Layout, Nav} from "@douyinfe/semi-ui";
import {IconBell, IconBytedanceLogo, IconHelpCircle, IconSemiLogo} from "@douyinfe/semi-icons";
import {menuItems} from '../config/menu.tsx';
import {useMemo} from 'react';
import {toast, Toaster} from "react-hot-toast";
import {authClearUser, authIsLogin} from "@/stores/auth.state.ts";
import {useQuery} from "@connectrpc/connect-query";
import {ProfileRequestSchema} from "@/api/authed.admin_pb";
import {create} from "@bufbuild/protobuf";
import {profile} from "@/api/authed.admin-AuthedService_connectquery";
import {router} from "@/internal/router.ts";

const {Header, Footer, Content} = Layout;
export const Route = createFileRoute('/_authed')({
   component: RouteComponent,
   beforeLoad() {
      if (!authIsLogin()) {
         authClearUser()
         router.navigate({
            to: '/login',
         })
      }
   }
})

function RouteComponent() {

   const {data} = useQuery(profile, create(ProfileRequestSchema, {}))
   
   const navigate = useNavigate()
   // 使用 useMemo 包裹 menuItems，避免不必要的重新渲染
   const memoizedMenuItems = useMemo(() => menuItems, []);
   
   const handleLogout = () => {
      toast.success('安全退出')
      authClearUser()
      navigate({to: "/login"})
   };
   
   
   return (
      <Layout style={{
         border: '1px solid var(--semi-color-border)',
         minHeight: '100vh',
         display: 'flex',
         flexDirection: 'column'
      }}>
         <Toaster/>
         <Header style={{
            boxShadow: 'var(--global-shadow)',
            position: "fixed",
            top: '0',
            zIndex: '999',
            width: '100%',
            backgroundColor: 'var(--semi-color-bg-1)'
         }}>
            <div>
               <Nav
                  style={{
                     maxWidth: '1880px',
                     width: '100%',
                     borderBottom: 'none',
                     display: 'flex',
                     alignItems: 'center',
                     margin: '0 auto',
                     padding: '0 24px'
                  }}
                  mode={'horizontal'}
                  items={memoizedMenuItems}
                  renderWrapper={({itemElement, props}) => {
                     if (props.itemKey?.toString().startsWith('#')) {
                        return itemElement;
                     }
                     // console.log('匹配路由:',props);
                     return props.itemKey ? (
                        <Link
                           style={{textDecoration: "none"}}
                           to={props.itemKey as string}
                        >
                           {itemElement}
                        </Link>
                     ) : itemElement;
                  }}
                  onSelect={key => console.log(key)}
                  header={{
                     logo: <IconSemiLogo style={{height: '36px', fontSize: 36}}/>,
                     text: '管理后台'
                  }}
                  footer={
                     
                     <>
                        <Button
                           theme="borderless"
                           icon={<IconBell size="large"/>}
                           style={{
                              color: 'var(--semi-color-text-2)',
                              marginRight: '12px',
                           }}
                        />
                        <Button
                           theme="borderless"
                           icon={<IconHelpCircle size="large"/>}
                           style={{
                              color: 'var(--semi-color-text-2)',
                              marginRight: '12px',
                           }}
                        />
                        <Dropdown
                           position="bottomRight"
                           render={
                              <Dropdown.Menu>
                                 <Dropdown.Item>修改密码</Dropdown.Item>
                                 <Dropdown.Item onClick={handleLogout}>安全退出</Dropdown.Item>
                              </Dropdown.Menu>
                           }
                        >
                           <span>{data ? data.username : 'loading'}</span>
                        </Dropdown>
                     </>
                  }
               />
            </div>
         </Header>
         <Content
            style={{
               padding: '24px',
               maxWidth: '1880px',
               width: '100%',
               // boxShadow: 'var(--global-shadow)',
               backgroundColor: 'var(--semi-color-bg-1)',
               margin: '75px auto 0 auto',
               flex: '1'
            }}
         >
            <Outlet/>
         </Content>
         <Footer
            style={{
               padding: '20px',
               marginTop: '20px',
               backgroundColor: 'var(--semi-color-bg-1)',
               color: 'var(--semi-color-text-2)',
            }}
         >
            <div style={{
               maxWidth: '1880px',
               width: '100%',
               display: 'flex',
               justifyContent: 'space-between',
               margin: '0 auto',
               padding: '0 24px'
            }}>
                <span
                   style={{
                      display: 'flex',
                      alignItems: 'center',
                   }}
                >
                    <IconBytedanceLogo size="large" style={{marginRight: '8px'}}/>
                    <span>Copyright © 2025 图小蜜. All Rights Reserved. </span>
                </span>
               {/*<span>*/}
               {/*<span style={{marginRight: '24px'}}>xxx</span>*/}
               {/*<span>xxx</span>*/}
               {/*</span>*/}
            </div>
         </Footer>
      </Layout>
   );
}
