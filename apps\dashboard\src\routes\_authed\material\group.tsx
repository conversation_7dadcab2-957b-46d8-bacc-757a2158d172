import { createFileRoute } from '@tanstack/react-router'
import { Button, Form, Modal, Space, Table, Popconfirm, Divider } from '@douyinfe/semi-ui'
import { useQuery, useMutation } from "@connectrpc/connect-query"
import {
  QueryMaterialGroupListRequest,
  QueryMaterialGroupListRequestSchema,
  CreateMaterialGroupRequestSchema,
  UpdateMaterialGroupRequestSchema,
  DeleteMaterialGroupRequestSchema
} from "@/api/material.admin_pb"
import { MaterialGroup } from "@/api/material.msg_pb"
import {
  queryMaterialGroupList,
  createMaterialGroup,
  updateMaterialGroup,
  deleteMaterialGroup
} from "@/api/material.admin-MaterialService_connectquery"
import { create } from "@bufbuild/protobuf"
import { useEffect, useState } from "react"
import { toast } from "react-hot-toast"
import { PageSizeRequestSchema } from "@/api/common.msg_pb"
import { formatStandardDateTime } from "@/lib/timestamp.ts"
import type { Timestamp } from "@bufbuild/protobuf/wkt"
import Section from "@douyinfe/semi-ui/lib/es/form/section"

export const Route = createFileRoute('/_authed/material/group')({
  component: RouteComponent,
})

interface GroupFormData {
  name: string
}

function RouteComponent() {
  const [queryRequest, setQueryRequest] = useState<QueryMaterialGroupListRequest>(
    create(QueryMaterialGroupListRequestSchema, {
      pageSize: create(PageSizeRequestSchema, { page: 1, size: 20 }),
      name: ""
    })
  )

  const [searchFormApi, setSearchFormApi] = useState<any>(null)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [createFormApi, setCreateFormApi] = useState<any>(null)
  const [editFormApi, setEditFormApi] = useState<any>(null)
  const [editingGroup, setEditingGroup] = useState<MaterialGroup | null>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([])

  // 查询列表
  const {
    data,
    error,
    isLoading,
    refetch
  } = useQuery(queryMaterialGroupList, queryRequest)

  // 创建分组
  const createMutation = useMutation(createMaterialGroup, {
    onSuccess: () => {
      toast.success('创建成功')
      setCreateModalVisible(false)
      createFormApi?.reset()
      refetch()
    },
    onError: (error) => {
      toast.error(`创建失败: ${error.message}`)
    }
  })

  // 更新分组
  const updateMutation = useMutation(updateMaterialGroup, {
    onSuccess: () => {
      toast.success('更新成功')
      setEditModalVisible(false)
      setEditingGroup(null)
      editFormApi?.reset()
      refetch()
    },
    onError: (error) => {
      toast.error(`更新失败: ${error.message}`)
    }
  })

  // 删除分组
  const deleteMutation = useMutation(deleteMaterialGroup, {
    onSuccess: () => {
      toast.success('删除成功')
      setSelectedRowKeys([])
      refetch()
    },
    onError: (error) => {
      toast.error(`删除失败: ${error.message}`)
    }
  })

  useEffect(() => {
    if (error) {
      toast.error(error.message)
    }
  }, [error])

  // 分页处理
  const handlePageChange = (page: number, size: number) => {
    setQueryRequest(prevRequest =>
      create(QueryMaterialGroupListRequestSchema, {
        ...prevRequest,
        pageSize: create(PageSizeRequestSchema, { page, size })
      })
    )
  }

  // 搜索处理
  const handleSearch = () => {
    const values = searchFormApi?.getValues()
    setQueryRequest(prevRequest =>
      create(QueryMaterialGroupListRequestSchema, {
        ...prevRequest,
        name: values?.name || "",
        pageSize: create(PageSizeRequestSchema, { page: 1, size: prevRequest.pageSize?.size || 20 })
      })
    )
  }

  // 重置搜索
  const handleReset = () => {
    setQueryRequest(
      create(QueryMaterialGroupListRequestSchema, {
        name: "",
        pageSize: create(PageSizeRequestSchema, { page: 1, size: queryRequest.pageSize?.size || 20 })
      })
    )
    searchFormApi?.reset()
  }

  // 创建分组
  const handleCreate = () => {
    createFormApi?.validate().then((values: GroupFormData) => {
      const request = create(CreateMaterialGroupRequestSchema, {
        name: values.name
      })
      createMutation.mutate(request)
    }).catch(() => {
      toast.error('请检查表单输入')
    })
  }

  // 编辑分组
  const handleEdit = (group: MaterialGroup) => {
    setEditingGroup(group)
    setEditModalVisible(true)
  }

  // 更新分组
  const handleUpdate = () => {
    if (!editingGroup) return

    editFormApi?.validate().then((values: GroupFormData) => {
      const request = create(UpdateMaterialGroupRequestSchema, {
        id: editingGroup.id,
        name: values.name
      })
      updateMutation.mutate(request)
    }).catch(() => {
      toast.error('请检查表单输入')
    })
  }

  // 删除单个分组
  const handleDeleteSingle = (id: bigint) => {
    const request = create(DeleteMaterialGroupRequestSchema, {
      id: [id]
    })
    deleteMutation.mutate(request)
  }

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      toast.error('请选择要删除的分组')
      return
    }

    const ids = selectedRowKeys.map(key => BigInt(key))
    const request = create(DeleteMaterialGroupRequestSchema, {
      id: ids
    })
    deleteMutation.mutate(request)
  }

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      render: (id: bigint) => String(id)
    },
    {
      title: '分组名称',
      dataIndex: 'name',
      width: 200
    },
    {
      title: '创建时间',
      dataIndex: 'createdTime',
      width: 180,
      render: (timestamp: Timestamp) => formatStandardDateTime(timestamp)
    },
    {
      title: '更新时间',
      dataIndex: 'updatedTime',
      width: 180,
      render: (timestamp: Timestamp) => formatStandardDateTime(timestamp)
    },
    {
      title: '操作',
      width: 150,
      render: (_: any, record: MaterialGroup) => (
        <Space>
          <Button
            size="small"
            type="primary"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分组吗？"
            onConfirm={() => handleDeleteSingle(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button size="small" type="danger">删除</Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: (string | number)[] | undefined) => {
      setSelectedRowKeys(selectedRowKeys || [])
    }
  }

  return (
    <>
      <Section text='素材分组管理'>
        {/* 搜索区域 */}

        <Form
          getFormApi={(api) => setSearchFormApi(api)}
          labelPosition='inset'
          layout='horizontal'
        >
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            flexWrap: 'wrap'
          }}>
            <Form.Input
              field='name'
              label='分组名称'
              style={{ width: 300 }}
              placeholder='请输入分组名称进行搜索'
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch()
                }
              }}
            />
            <Space>
              <Button type="primary" onClick={handleSearch}>
                查询
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </div>
        </Form>
        <Divider dashed={true} margin='12px'/>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          {/* 左侧操作按钮 */}
          <div>
            <Space spacing="medium">
              <Button
                type="primary"
                onClick={() => setCreateModalVisible(true)}
                theme="solid"
              >
                新建分组
              </Button>
              <Popconfirm
                title={`确定要删除选中的 ${selectedRowKeys.length} 个分组吗？`}
                onConfirm={handleBatchDelete}
                disabled={selectedRowKeys.length === 0}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="danger"
                  disabled={selectedRowKeys.length === 0}
                  theme="solid"
                >
                  批量删除
                  {selectedRowKeys.length > 0 && ` (${selectedRowKeys.length})`}
                </Button>
              </Popconfirm>
            </Space>
          </div>


        </div>
        <Divider margin='12px' />

        {/* 数据表格 */}

        <Table
          columns={columns}
          dataSource={data?.items || []}
          rowKey="id"
          rowSelection={rowSelection}
          pagination={{
            currentPage: queryRequest.pageSize?.page || 1,
            pageSize: queryRequest.pageSize?.size || 20,
            total: Number(data?.total || 0),
            onChange: handlePageChange,
            pageSizeOpts: [20, 50, 100],
            showSizeChanger: true,

          }}
          loading={isLoading}
          empty="暂无数据"
        />

      </Section>

      {/* 创建分组弹窗 */}
      <Modal
        title="新建分组"
        visible={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false)
          createFormApi?.reset()
        }}
        footer={
          <Space>
            <Button onClick={() => {
              setCreateModalVisible(false)
              createFormApi?.reset()
            }}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleCreate}
              loading={createMutation.isPending}
            >
              确定
            </Button>
          </Space>
        }
      >
        <Form
          getFormApi={(api) => setCreateFormApi(api)}
        >
          <Form.Input
            field='name'
            label='分组名称'
            placeholder='请输入分组名称'
            rules={[
              { required: true, message: '分组名称不能为空' },
              { max: 50, message: '分组名称不能超过50个字符' }
            ]}
          />
        </Form>
      </Modal>

      {/* 编辑分组弹窗 */}
      <Modal
        title="编辑分组"
        visible={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false)
          setEditingGroup(null)
          editFormApi?.reset()
        }}
        afterOpen={() => {
          // 在弹窗打开后设置表单初始值
          if (editingGroup) {
            editFormApi?.setValues({ name: editingGroup.name })
          }
        }}
        footer={
          <Space>
            <Button onClick={() => {
              setEditModalVisible(false)
              setEditingGroup(null)
              editFormApi?.reset()
            }}>
              取消
            </Button>
            <Button
              type="primary"
              onClick={handleUpdate}
              loading={updateMutation.isPending}
            >
              确定
            </Button>
          </Space>
        }
      >
        <Form
          getFormApi={(api) => setEditFormApi(api)}
          initValues={editingGroup ? { name: editingGroup.name } : undefined}
        >
          <Form.Input
            field='name'
            label='分组名称'
            placeholder='请输入分组名称'
            rules={[
              { required: true, message: '分组名称不能为空' },
              { max: 50, message: '分组名称不能超过50个字符' }
            ]}
          />
        </Form>
      </Modal>
    </>
  )
}
