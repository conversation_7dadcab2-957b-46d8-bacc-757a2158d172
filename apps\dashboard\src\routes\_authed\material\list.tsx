import { createFileRoute } from '@tanstack/react-router'
import { Button, Form, Image, Space, Table, Tag } from '@douyinfe/semi-ui'
import { useQuery } from "@connectrpc/connect-query"
import {
  QueryMaterialListRequest,
  QueryMaterialListRequestSchema
} from "@/api/material.admin_pb"
import { queryMaterialList } from "@/api/material.admin-MaterialService_connectquery"
import { create } from "@bufbuild/protobuf"
import { useEffect, useState } from "react"
import { toast } from "react-hot-toast"
import { PageSizeRequestSchema } from "@/api/common.msg_pb"
import { formatStandardDateTime } from "@/lib/timestamp.ts"
import type { Timestamp } from "@bufbuild/protobuf/wkt"
import Section from "@douyinfe/semi-ui/lib/es/form/section"

export const Route = createFileRoute('/_authed/material/list')({
  component: RouteComponent,
})

const columns = [
  {
    title: '原图',
    dataIndex: 'sourceUrl',
    width: 120,
    render: (sourceUrl: string) => {
      return (
         <div>
           <Image
              width={100}
              height={100}
              src={`${sourceUrl}?imageView2/2/w/150/q/50/format/webp`}
              crossOrigin={"anonymous"}
              referrerPolicy="no-referrer"
              preview={{
                src: sourceUrl,
                crossOrigin: "anonymous"
              }}
           />
         </div>
      )
    },
  },
  {
    title: '素材图',
    dataIndex: 'path',
    width: 120,
    render: (path: string) => {
      return (
         <div>
           <Image
              width={100}
              height={100}
              src={`${path}`}
              crossOrigin={"anonymous"}
              referrerPolicy="no-referrer"
              preview={{
                src: path,
                crossOrigin: "anonymous"
              }}
           />
         </div>
      )
    },
  },
  {
    title: '素材标题',
    dataIndex: 'title',
    width: 300,
    render: (title: string) => {
      return (
        <div style={{ wordBreak: 'break-word' }}>
          {title || '无标题'}
        </div>
      )
    },
  },
  {
    title: 'ID',
    dataIndex: 'id',
    width: 100,
    render: (id: bigint) => String(id)
  },
  {
    title: '素材分组',
    dataIndex: 'materialGroup',
    width: 150,
    render: (groups: bigint[]) => {
      if (!groups || groups.length === 0) {
        return <Tag color="grey">未分组</Tag>
      }
      return (
        <div>
          {groups.map((groupId, index) => (
            <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
              {String(groupId)}
            </Tag>
          ))}
        </div>
      )
    }
  },
  {
    title: '标记',
    dataIndex: 'flag',
    width: 150,
    render: (flags: string[]) => {
      if (!flags || flags.length === 0) {
        return <span style={{ color: '#999' }}>无标记</span>
      }
      return (
        <div>
          {flags.map((flag, index) => (
            <Tag key={index} color="green" style={{ marginBottom: 4 }}>
              {flag}
            </Tag>
          ))}
        </div>
      )
    }
  },
  {
    title: '来源Temu ID',
    dataIndex: 'sourceTemuId',
    width: 120,
    render: (sourceTemuId: bigint) => {
      return sourceTemuId && sourceTemuId > 0 ? String(sourceTemuId) : '-'
    }
  },
  {
    title: '文件哈希',
    dataIndex: 'hash',
    width: 120,
    render: (hash: string) => {
      return hash ? (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {hash.substring(0, 8)}...
        </span>
      ) : '-'
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    width: 160,
    render: (timestamp: Timestamp) => {
      return (
        <div>
          {formatStandardDateTime(timestamp)}
        </div>
      )
    }
  },
  {
    title: '更新时间',
    dataIndex: 'updatedTime',
    width: 160,
    render: (timestamp: Timestamp) => {
      return (
        <div>
          {formatStandardDateTime(timestamp)}
        </div>
      )
    }
  }
]

function RouteComponent() {
  const [queryRequest, setQueryRequest] = useState<QueryMaterialListRequest>(
    create(QueryMaterialListRequestSchema, {
      pageSize: create(PageSizeRequestSchema, { page: 1, size: 20 }),
      title: ""
    })
  )
  const [formApiRef, setFormApiRef] = useState<any>(null)

  const {
    data,
    error,
    isLoading
  } = useQuery(queryMaterialList, queryRequest)

  useEffect(() => {
    if (error) {
      toast.error(error.message)
    }
  }, [error])

  const handlePageChange = (page: number, size: number) => {
    setQueryRequest(prevRequest =>
      create(QueryMaterialListRequestSchema, {
        ...prevRequest,
        pageSize: create(PageSizeRequestSchema, { page, size })
      })
    )
  }

  const handleSearch = () => {
    const values = formApiRef?.getValues()
    setQueryRequest(prevRequest =>
      create(QueryMaterialListRequestSchema, {
        ...prevRequest,
        title: values?.title || "",
        pageSize: create(PageSizeRequestSchema, { page: 1, size: prevRequest.pageSize?.size || 20 })
      })
    )
  }

  const handleReset = () => {
    setQueryRequest(
      create(QueryMaterialListRequestSchema, {
        title: "",
        pageSize: create(PageSizeRequestSchema, { page: 1, size: queryRequest.pageSize?.size || 20 })
      })
    )
    formApiRef?.reset()
  }

  return (
    <>
      <Section text='素材管理'>
        <Form getFormApi={(api) => setFormApiRef(api)} labelPosition='inset' layout='horizontal'>
          <Form.Input
            field='title'
            label='素材标题'
            trigger='blur'
            style={{ width: 300 }}
            placeholder='搜索素材标题'
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearch()
              }
            }}
          />
          <Space>
            <Button type="primary" onClick={handleSearch} className="btn-margin-right">
              查询
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
          </Space>
        </Form>

        <Table
          columns={columns}
          dataSource={data?.items || []}
          rowKey="id"
          pagination={{
            currentPage: queryRequest.pageSize?.page || 1,
            pageSize: queryRequest.pageSize?.size || 20,
            total: Number(data?.total || 0),
            onChange: handlePageChange,
            pageSizeOpts: [20, 50, 100, 300, 500, 1000],
            showSizeChanger: true,
          }}
          loading={isLoading}
          empty="暂无素材数据"
        />
      </Section>
    </>
  )
}
