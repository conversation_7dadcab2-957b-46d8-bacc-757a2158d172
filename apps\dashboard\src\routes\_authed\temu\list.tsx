import {createFileRoute} from '@tanstack/react-router'
import {Button, Form, Image, Space, Table} from '@douyinfe/semi-ui';
import {useQuery} from "@connectrpc/connect-query";
import {QueryTemuListRequest, QueryTemuListRequestSchema} from "@/api/temu.admin_pb";
import {ProductItem} from "@/api/product.msg_pb";
import {queryTemuList} from "@/api/temu.admin-TemuService_connectquery";
import {create} from "@bufbuild/protobuf";
import {useEffect, useState} from "react";
import {toast} from "react-hot-toast";
import {PageSizeRequestSchema} from "@/api/common.msg_pb";
import {formatStandardDateTime} from "@/lib/timestamp.ts";
import type {Timestamp} from "@bufbuild/protobuf/wkt";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
//aaa
export const Route = createFileRoute('/_authed/temu/list')({
   component: RouteComponent,
})

const columns = [
   {
      title: '主图',
      dataIndex: 'spec.featuredImage',
      width: 100,
      render: (text: string) => {
         return (
            <div>
               <Image
                  width={100}
                  height={100}
                  src={`${text}?imageView2/2/w/150/q/50/format/webp`}
                  crossOrigin={"anonymous"}
                  referrerPolicy="no-referrer"
                  preview={{
                     src: text,
                     crossOrigin: "anonymous"
                  }}
               />
            </div>
         );
      },
   },
   {
      title: '标题',
      dataIndex: 'spec.title',
      width: 350,
      render: (text: string) => {
         return (
            <div>
               {text}
            </div>
         );
      },
   },
   {
      title: '产品ID',
      dataIndex: 'spec.id',
   },
   {
      title: '分类ID',
      dataIndex: 'spec.temuCats',
      render: (text: string[]) => {
         return (
            <div>
               {text?.map((cat, index) => (
                  <span key={index}>
                     {cat}
                     {index < text.length - 1 && '/'}
                  </span>
               ))}
            </div>
         )
      }
   },
   {
      title: '价格',
      dataIndex: 'spec.price',
      render: (text: bigint, row: ProductItem) => {
         
         return (
            <div>
               {Number(text) / 100} {row?.spec?.currency}
            </div>
         );
      },
   },
   {
      title: '创建时间',
      dataIndex: 'createdTime',
      render: (timestamp: Timestamp) => {
         return (
            <div>
               {formatStandardDateTime(timestamp)}
            </div>
         )
      }
   }, {
      title: '更新时间',
      dataIndex: 'updatedTime',
      render: (timestamp: Timestamp) => {
         return (
            <div>
               {formatStandardDateTime(timestamp)}
            </div>
         )
      }
   }
];


function RouteComponent() {
   const [queryRequest, setQueryRequest] = useState<QueryTemuListRequest>(
      create(QueryTemuListRequestSchema, {
         pageSize: create(PageSizeRequestSchema, {page: 1, size: 20})
      })
   );
   const [formApiRef, setFormApiRef] = useState<any>(null);
   
   const {
      data,
      error,
      isLoading
   } = useQuery(queryTemuList, queryRequest);
   
   useEffect(() => {
      if (error) {
         toast.error(error.message);
      }
   }, [error]);
   
   const handlePageChange = (page: number, size: number) => {
      setQueryRequest(prevRequest =>
         create(QueryTemuListRequestSchema, {
            ...prevRequest,
            pageSize: create(PageSizeRequestSchema, {page, size})
         })
      );
   };
   
   const handleSearch = () => {
      const values = formApiRef?.getValues();
      setQueryRequest(prevRequest =>
         create(QueryTemuListRequestSchema, {
            ...prevRequest,
            title: values?.title || undefined,
            temuCats: (values?.temuCats && Array.isArray(values.temuCats) && values.temuCats.length > 0) 
               ? values.temuCats.filter((cat: string) => cat && cat.trim() !== '')
               : [],
            pageSize: create(PageSizeRequestSchema, {page: 1, size: prevRequest.pageSize?.size || 20})
         })
      );
   };
   
   const handleReset = () => {
      setQueryRequest(
         create(QueryTemuListRequestSchema, {
            pageSize: create(PageSizeRequestSchema, {page: 1, size: queryRequest.pageSize?.size || 20})
         })
      );
      formApiRef?.reset();
   };
   
   return (
      <>
         <Section text='Temu产品数据'>
            <Form getFormApi={(api) => setFormApiRef(api)} labelPosition='inset' layout='horizontal'>
               
               <Form.Input
                  field='title'
                  label='标题'
                  trigger='blur'
                  style={{width: 250}}
                  placeholder='搜索标题'
                  onKeyDown={(e) => {
                     if (e.key === 'Enter') {
                        handleSearch();
                     }
                  }}
               />
               <Form.TagInput
                  field='temuCats'
                  label='分类筛选'
                  placeholder='输入分类ID'
                  style={{width: 450}}
                  allowDuplicates={false}
                  showClear
                  addOnBlur={true}
                  separator={[',', '/', '|', ' ']}
                  onKeyDown={(e) => {
                     if (e.key === 'Enter') {
                        // Prevent form submission when adding tags
                        handleSearch();
                     }
                  }}
               />
               <Space>
                  <Button type="primary" onClick={handleSearch} className="btn-margin-right">查询</Button>
                  <Button onClick={handleReset}>重置</Button>
               </Space>
            
            </Form>
            
           
            
            <Table
               columns={columns}
               dataSource={data?.items || []}
               pagination={{
                  currentPage: queryRequest.pageSize?.page || 1,
                  pageSize: queryRequest.pageSize?.size || 20,
                  total: Number(data?.total || 0),
                  onChange: handlePageChange,
                  pageSizeOpts: [20, 50, 100, 300, 500, 1000],
                  showSizeChanger: true,
               }}
               loading={isLoading}
            />
         </Section>
      </>
   );
}
