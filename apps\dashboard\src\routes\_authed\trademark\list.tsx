import {createFileRoute} from '@tanstack/react-router'
import {Table} from '@douyinfe/semi-ui';
import {useQuery} from "@connectrpc/connect-query";
import {QueryTrademarkListRequest, QueryTrademarkListRequestSchema} from "@/api/trademark.admin_pb";
import {queryTrademarkList} from "@/api/trademark.admin-TrademarkService_connectquery";
import {create} from "@bufbuild/protobuf";
import {useEffect, useState} from "react";
import {toast} from "react-hot-toast";
import {PageSizeRequestSchema} from "@/api/common.msg_pb";
import {formatStandardDateTime} from "@/lib/timestamp.ts";
import type {Timestamp} from "@bufbuild/protobuf/wkt";
import Section from "@douyinfe/semi-ui/lib/es/form/section";
//aaa
export const Route = createFileRoute('/_authed/trademark/list')({
  component: RouteComponent,
})

const columns = [
    {
        title: 'ID',
        dataIndex: 'id',
        render: (id: bigint) => String(id)
    },
    {
        title: '商标ID',
        dataIndex: 'tid',
    },
    {
        title: '商标数据',
        dataIndex: 'data',
    },
  {
    title: '创建时间',
    dataIndex: 'createdTime',
    render: (timestamp: Timestamp) => {
      return (
          <div>
            {formatStandardDateTime(timestamp)}
          </div>
      )
    }
  }, {
    title: '更新时间',
    dataIndex: 'updatedTime',
    render: (timestamp: Timestamp) => {
      return (
          <div>
            {formatStandardDateTime(timestamp)}
          </div>
      )
    }
  }
];


function RouteComponent() {
  const [queryRequest, setQueryRequest] = useState<QueryTrademarkListRequest>(
      create(QueryTrademarkListRequestSchema, {
        pageSize: create(PageSizeRequestSchema, {page: 1, size: 20})
      })
  );

  const {
    data,
    error,
    isLoading
  } = useQuery(queryTrademarkList, queryRequest);

  useEffect(() => {
    if (error) {
      toast.error(error.message);
    }
  }, [error]);

  const handlePageChange = (page: number, size: number) => {
    setQueryRequest(prevRequest =>
        create(QueryTrademarkListRequestSchema, {
          ...prevRequest,
          pageSize: create(PageSizeRequestSchema, {page, size})
        })
    );
  };

  return (
      <>
        <Section text='商标数据'>
          <Table
              columns={columns}
              dataSource={data?.items || []}
              pagination={{
                currentPage: queryRequest.pageSize?.page || 1,
                pageSize: queryRequest.pageSize?.size || 20,
                total: Number(data?.total || 0),
                onChange: handlePageChange,
                pageSizeOpts: [20, 50, 100, 300, 500, 1000],
                showSizeChanger: true,
              }}
              loading={isLoading}
          />
        </Section>
      </>
  );
}
