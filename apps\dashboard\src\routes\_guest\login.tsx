import {createFileRoute, useNavigate} from '@tanstack/react-router'
// import styles from './index.module.css';
import {toast, Toaster} from "react-hot-toast";
import {useMutation} from "@connectrpc/connect-query";
import {login} from "@/api/auth.admin-AuthService_connectquery";
import {LightLogin} from "@/components/ui/sign-in.tsx";
import {LoginRequest, LoginRequestSchema, LoginResponse} from "@/api/auth.admin_pb";
import {create} from "@bufbuild/protobuf";
import {FormEvent, useState} from "react";
import {ConnectError} from "@connectrpc/connect";
import {useAuthSetUser} from "@/stores/auth.state.ts";

export const Route = createFileRoute('/_guest/login')({
   component: RouteComponent,
})

function RouteComponent() {
   
   const navigate = useNavigate();
  const setUserData=  useAuthSetUser()
   const [formData, setFormData] = useState<LoginRequest>(create(LoginRequestSchema, {}))
   
   const {mutate, isPending} = useMutation(login);
   const handleSubmit = (e: FormEvent) => {
      e.preventDefault();
      mutate(formData, {
         onSuccess: (data: LoginResponse) => {
            setUserData(data);
            toast.success('登录成功');
            navigate({to:"/"})
         },
         onError: (error: ConnectError) => {
            toast.error(error.message);
         }
      });
   }
   return (
      <>
         <Toaster/>
         <LightLogin formData={formData} onSubmit={handleSubmit} setForm={setFormData} isPending={isPending}/>
      </>
   );
}
