import type { LoginResponse } from '@/api/auth.admin_pb';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

/**
 * 定义AuthStore接口，用于管理用户的认证状态
 */
interface AuthStore {
   // 状态
   userData: LoginResponse | null;
   
   // 操作方法
   setUserData: (data: LoginResponse) => void;
   clearUserData: () => void;
   
   // 辅助方法
   isLoggedIn: () => boolean;
   getRemainingTime: () => number | null; // 返回token剩余有效时间(秒)
}

/**
 * 定义序列化状态接口，用于处理bigint类型的序列化
 */
interface SerializedState {
   userData: {
      username: string;
      token: string;
      expireAt: string;
   } | null;
}

/**
 * 创建并导出认证状态管理store
 */
export const useAuthStore = create<AuthStore>()(
   persist(
      (set, get) => ({
         // 初始状态
         userData: null,
         
         /**
          * 设置用户数据
          * @param data 登录响应数据
          */
         setUserData: (data: LoginResponse) => {
            set({ userData: data });
         },
         
         /**
          * 清除用户数据，用于登出操作
          */
         clearUserData: () => {
            console.log('清除用户数据');
            set({ userData: null });
         },
         
         /**
          * 检查用户是否已登录且token未过期
          * @returns 是否已登录
          */
         isLoggedIn: () => {
            const { userData } = get();
            if (!userData) return false;
            
            // 检查token是否过期
            const now = BigInt(Math.floor(Date.now() / 1000)); // 转换为秒级时间戳
            return userData.expireAt > now;
         },
         
         /**
          * 获取token剩余有效时间(秒)
          * @returns 剩余时间(秒)，若未登录返回null
          */
         getRemainingTime: () => {
            const { userData } = get();
            if (!userData) return null;
            
            const now = BigInt(Math.floor(Date.now() / 1000)); // 转换为秒级时间戳
            const remaining = userData.expireAt - now;
            
            // 如果token已过期，返回0
            return remaining > 0n ? Number(remaining) : 0;
         },
      }),
      {
         name: 'auth-storage', // localStorage的key名称
         
         /**
          * 处理bigint序列化 - 将状态转换为可序列化的格式
          */
         partialize: (state) => {
            if (!state.userData) {
               return { userData: null } as SerializedState;
            }
            
            // 返回序列化友好的对象
            return {
               userData: {
                  username: state.userData.username,
                  token: state.userData.token,
                  expireAt: state.userData.expireAt.toString(),
               },
            } as SerializedState;
         },
         
         /**
          * 处理反序列化 - 将从存储中恢复的数据转换回原始格式
          */
         onRehydrateStorage: () => (state) => {
            if (state?.userData) {
               // 确保expireAt是bigint类型
               state.userData.expireAt = BigInt(
                  state.userData.expireAt as unknown as string,
               );
            }
         },
      },
   ),
);

/**
 * 导出便捷hooks，全部以useAuth开头
 */
export const useAuthIsLogin = () => useAuthStore((state) => state.isLoggedIn());
export const useAuthGetUser = () => useAuthStore((state) => state.userData);
export const useAuthClearUser = () =>
   useAuthStore((state) => state.clearUserData);
export const useAuthSetUser = () => useAuthStore((state) => state.setUserData);

/**
 * 提供非hook版本的函数，用于在非React组件环境中使用（如路由配置）
 */
export const authIsLogin = () => useAuthStore.getState().isLoggedIn();
export const authClearUser = () => useAuthStore.getState().clearUserData();
export const authGetUser = () => useAuthStore.getState().userData;
export const authGetToken = () => useAuthStore.getState().userData?.token;
export const authGetExpireAt = () => useAuthStore.getState().userData?.expireAt;
