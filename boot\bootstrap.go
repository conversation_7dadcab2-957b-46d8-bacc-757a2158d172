package boot

import (
    "omnix/state"
    "omnix/toolkit/wrapper/kitviper"
    "os"
    "time"

    "github.com/qwenode/rr"
    "github.com/rs/zerolog"
    "github.com/rs/zerolog/log"
    _ "github.com/spf13/viper/remote"
)

// 加载配置 20250806
func LoadOptions() {
    log.Logger = log.
        Output(zerolog.ConsoleWriter{Out: os.Stdout, NoColor: true, TimeFormat: "2006-01-02T15:04:05Z"}).
        Level(zerolog.Level(state.LogLevel))
    time.Local = time.UTC
    envFile := state.EnvFile
    if !rr.FileExist(envFile) {
        envFile = rr.FileWithWorkDirectory(envFile)
    }
    if !rr.FileExist(envFile) {
        log.Fatal().Str("file", envFile).Msg("no config file found")
    }
    err := kitviper.ReadToml(envFile, &state.LocalOptions)
    if err != nil {
        log.Fatal().Err(err).Str("文件",envFile).Msg("配置文件加载失败")
    }
    
    if state.LocalOptions.Consul.Host == "" || state.LocalOptions.Consul.Key == "" {
        log.Fatal().Msg("请先配置[配置中心]服务器")
        return
    }
    log.Info().Str("配置中心", state.LocalOptions.Consul.Host).Str("配置文件",state.LocalOptions.Consul.Key).Send()
    err = kitviper.WatchRemoteTomlOnConsul(state.LocalOptions.Consul.Host, state.LocalOptions.Consul.Key, &state.RemoteOptions)
    if err != nil {
        log.Fatal().Err(err).Msg("远程配置初始化失败")
    }
}
