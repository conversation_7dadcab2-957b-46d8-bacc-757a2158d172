package main

import (
    "fmt"
    "omnix/cmd/comfyapi/internal"
    "omnix/toolkit/ginzerolog"

    "github.com/gin-gonic/gin"
    "github.com/rs/zerolog/log"
    "github.com/spf13/cobra"
)

var commandDashboard = &cobra.Command{
    Use:   "api",
    Short: "启动Comfyui API服务",
    Run: func(cmd *cobra.Command, args []string) {
        gin.SetMode(gin.ReleaseMode)
        r := gin.New()
        r.SetTrustedProxies(nil)
        r.Use(gin.Recovery())
        r.Use(ginzerolog.DefaultZerologLogger())
        r.GET("/ping", func(c *gin.Context) {
            c.JSON(200, gin.H{"message": "pong"})
        })

        // Register all ComfyUI file management and proxy routes
        internal.RegisterRoutes(r)

        port := internal.Options.Service.Port
        if port <= 0 || port > 65535 {
            port = 28880
            log.Warn().Int("APP_API_PORT", port).Msg("未配置端口,使用系统默认")
        }
        r.Run(
            fmt.Sprintf(
                "0.0.0.0:%d",
                port,
            ),
        )
    },
    Args: cobra.NoArgs,
}

func init() {
    mainCommand.AddCommand(commandDashboard)
}
