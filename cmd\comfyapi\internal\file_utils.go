package internal

import (
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// Common file operation functions

// sanitizeFilename removes dangerous characters from filename
func sanitizeFilename(filename string) string {
	// Remove path separators and other dangerous characters
	filename = strings.ReplaceAll(filename, "/", "")
	filename = strings.ReplaceAll(filename, "\\", "")
	filename = strings.ReplaceAll(filename, "..", "")
	filename = strings.ReplaceAll(filename, ":", "")
	filename = strings.ReplaceAll(filename, "*", "")
	filename = strings.ReplaceAll(filename, "?", "")
	filename = strings.ReplaceAll(filename, "\"", "")
	filename = strings.ReplaceAll(filename, "<", "")
	filename = strings.ReplaceAll(filename, ">", "")
	filename = strings.ReplaceAll(filename, "|", "")
	
	// Trim whitespace
	filename = strings.TrimSpace(filename)
	
	// Ensure filename is not empty and not just dots
	if filename == "" || filename == "." || filename == ".." {
		return ""
	}
	
	return filename
}

// ensureDirectoryExists creates directory if it doesn't exist
func ensureDirectoryExists(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, 0755)
	}
	return nil
}

// isPathSafe checks if the resolved path is within the allowed directory
func isPathSafe(filePath, allowedDir string) bool {
	// Clean and resolve paths
	cleanFilePath := filepath.Clean(filePath)
	cleanAllowedDir := filepath.Clean(allowedDir)
	
	// Get absolute paths
	absFilePath, err := filepath.Abs(cleanFilePath)
	if err != nil {
		return false
	}
	
	absAllowedDir, err := filepath.Abs(cleanAllowedDir)
	if err != nil {
		return false
	}
	
	// Check if file path starts with allowed directory
	rel, err := filepath.Rel(absAllowedDir, absFilePath)
	if err != nil {
		return false
	}
	
	// If relative path starts with "..", it's outside the allowed directory
	return !strings.HasPrefix(rel, "..")
}

// calculateFileSHA1 calculates the SHA1 hash of a file
func calculateFileSHA1(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	hash := sha1.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", fmt.Errorf("failed to calculate hash: %w", err)
	}

	return hex.EncodeToString(hash.Sum(nil)), nil
}

// detectContentType determines the MIME type based on file extension
func detectContentType(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	
	switch ext {
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	case ".bmp":
		return "image/bmp"
	case ".svg":
		return "image/svg+xml"
	case ".pdf":
		return "application/pdf"
	case ".txt":
		return "text/plain"
	case ".json":
		return "application/json"
	case ".xml":
		return "application/xml"
	case ".html", ".htm":
		return "text/html"
	case ".css":
		return "text/css"
	case ".js":
		return "application/javascript"
	case ".mp4":
		return "video/mp4"
	case ".avi":
		return "video/x-msvideo"
	case ".mov":
		return "video/quicktime"
	case ".wmv":
		return "video/x-ms-wmv"
	case ".mp3":
		return "audio/mpeg"
	case ".wav":
		return "audio/wav"
	case ".zip":
		return "application/zip"
	case ".rar":
		return "application/x-rar-compressed"
	case ".7z":
		return "application/x-7z-compressed"
	default:
		return "application/octet-stream"
	}
}

// Generic file operation handlers

// handleGenericUpload processes multipart form file uploads to a specified directory
func handleGenericUpload(c *gin.Context, targetDir, dirType string) {
	// Parse multipart form with size limit
	err := c.Request.ParseMultipartForm(MaxFileSize)
	if err != nil {
		log.Error().Err(err).Str("dirType", dirType).Msg("Failed to parse multipart form")
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"invalid_form",
			"Failed to parse multipart form data",
		))
		return
	}

	// Get the file from form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"missing_file",
			"No file provided in the request",
		))
		return
	}
	defer file.Close()

	// Validate file size
	if header.Size > MaxFileSize {
		c.JSON(http.StatusRequestEntityTooLarge, NewErrorResponse(
			http.StatusRequestEntityTooLarge,
			"file_too_large",
			fmt.Sprintf("File size %d bytes exceeds maximum allowed size of %d bytes", header.Size, MaxFileSize),
		))
		return
	}

	// Sanitize filename
	filename := sanitizeFilename(header.Filename)
	if filename == "" {
		c.JSON(http.StatusBadRequest, NewValidationError("Invalid filename provided"))
		return
	}

	// Ensure target directory exists
	if targetDir == "" {
		c.JSON(http.StatusInternalServerError, NewConfigurationError(fmt.Sprintf("%s directory not configured", dirType)))
		return
	}

	err = ensureDirectoryExists(targetDir)
	if err != nil {
		log.Error().Err(err).Str("dirType", dirType).Str("dir", targetDir).Msg("Failed to create directory")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"directory_error",
			fmt.Sprintf("Failed to create %s directory", dirType),
		))
		return
	}

	// Create full file path
	filePath := filepath.Join(targetDir, filename)

	// Validate that the file path is within the target directory (prevent path traversal)
	if !isPathSafe(filePath, targetDir) {
		c.JSON(http.StatusBadRequest, NewSecurityError("Invalid file path detected"))
		return
	}

	// Create the destination file
	dst, err := os.Create(filePath)
	if err != nil {
		log.Error().Err(err).Str("filePath", filePath).Str("dirType", dirType).Msg("Failed to create destination file")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"file_creation_error",
			"Failed to create destination file",
		))
		return
	}
	defer dst.Close()

	// Copy file content
	bytesWritten, err := io.Copy(dst, file)
	if err != nil {
		os.Remove(filePath) // Clean up on failure
		log.Error().Err(err).Str("filePath", filePath).Str("dirType", dirType).Msg("Failed to write file content")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"file_write_error",
			"Failed to write file content",
		))
		return
	}



	// Return success response
	c.JSON(http.StatusCreated, UploadResponse{
		Success:  true,
		Filename: filename,
		Size:     bytesWritten,
		Path:     filePath,
		Message:  fmt.Sprintf("File uploaded successfully to %s directory", dirType),
	})
}

// handleGenericList lists all files in the specified directory
func handleGenericList(c *gin.Context, targetDir, dirType string) {
	if targetDir == "" {
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"config_error",
			fmt.Sprintf("%s directory not configured", dirType),
		))
		return
	}

	// Check if directory exists, create if it doesn't
	if _, err := os.Stat(targetDir); os.IsNotExist(err) {
		err = os.MkdirAll(targetDir, 0755)
		if err != nil {
			log.Error().Err(err).Str("dir", targetDir).Str("dirType", dirType).Msg("Failed to create directory")
			c.JSON(http.StatusInternalServerError, NewErrorResponse(
				http.StatusInternalServerError,
				"directory_error",
				fmt.Sprintf("Failed to create %s directory", dirType),
			))
			return
		}
	}

	// Read directory contents
	entries, err := os.ReadDir(targetDir)
	if err != nil {
		log.Error().Err(err).Str("dir", targetDir).Str("dirType", dirType).Msg("Failed to read directory")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"directory_read_error",
			fmt.Sprintf("Failed to read %s directory", dirType),
		))
		return
	}

	// Collect file information
	var files []FileInfo
	for _, entry := range entries {
		info, err := entry.Info()
		if err != nil {
			log.Warn().Err(err).Str("file", entry.Name()).Str("dirType", dirType).Msg("Failed to get file info, skipping")
			continue
		}

		fileInfo := FileInfo{
			Name:        info.Name(),
			Size:        info.Size(),
			ModTime:     info.ModTime(),
			IsDirectory: info.IsDir(),
		}
		files = append(files, fileInfo)
	}

	response := FileListResponse{
		Files: files,
		Count: len(files),
	}
	c.JSON(http.StatusOK, response)
}

// handleGenericDownload serves files from the specified directory
func handleGenericDownload(c *gin.Context, targetDir, dirType string) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"missing_filename",
			"Filename parameter is required",
		))
		return
	}

	// Sanitize filename to prevent path traversal
	sanitizedFilename := sanitizeFilename(filename)
	if sanitizedFilename == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"invalid_filename",
			"Invalid filename provided",
		))
		return
	}

	if targetDir == "" {
		log.Error().Str("dirType", dirType).Msg("Directory not configured for download")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"config_error",
			fmt.Sprintf("%s directory not configured", dirType),
		))
		return
	}

	// Create full file path
	filePath := filepath.Join(targetDir, sanitizedFilename)

	// Validate that the file path is within the target directory (prevent path traversal)
	if !isPathSafe(filePath, targetDir) {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"invalid_path",
			"Invalid file path detected",
		))
		return
	}

	// Check if file exists and get file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			c.JSON(http.StatusNotFound, NewNotFoundError(fmt.Sprintf("file '%s' in %s directory", sanitizedFilename, dirType)))
			return
		}
		log.Error().Err(err).Str("filePath", filePath).Str("dirType", dirType).Msg("Failed to access file")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"file_access_error",
			"Failed to access the requested file",
		))
		return
	}

	// Check if it's a directory (not allowed for download)
	if fileInfo.IsDir() {
		log.Warn().Str("filename", sanitizedFilename).Str("dirType", dirType).Msg("Attempted to download a directory")
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"invalid_file_type",
			"Cannot download a directory",
		))
		return
	}

	// Open the file for reading
	file, err := os.Open(filePath)
	if err != nil {
		log.Error().Err(err).Str("path", filePath).Str("dirType", dirType).Msg("Failed to open file for reading")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"file_read_error",
			"Failed to open the requested file",
		))
		return
	}
	defer file.Close()

	// Detect content type based on file extension
	contentType := detectContentType(sanitizedFilename)

	// Set response headers
	c.Header("Content-Type", contentType)
	c.Header("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", sanitizedFilename))
	c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
	c.Header("Pragma", "no-cache")
	c.Header("Expires", "0")



	// Stream the file content to the client
	c.Status(http.StatusOK)
	_, err = io.Copy(c.Writer, file)
	if err != nil {
		log.Error().Err(err).Str("filename", sanitizedFilename).Str("dirType", dirType).Msg("Failed to stream file content")
		// Note: We can't send a JSON error response here since we've already started streaming
		return
	}


}

// handleGenericDelete removes files from the specified directory
func handleGenericDelete(c *gin.Context, targetDir, dirType string) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"missing_filename",
			"Filename parameter is required",
		))
		return
	}

	// Sanitize filename to prevent path traversal
	sanitizedFilename := sanitizeFilename(filename)
	if sanitizedFilename == "" {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"invalid_filename",
			"Invalid filename provided",
		))
		return
	}

	if targetDir == "" {
		log.Error().Str("dirType", dirType).Msg("Directory not configured for deletion")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"config_error",
			fmt.Sprintf("%s directory not configured", dirType),
		))
		return
	}

	// Create full file path
	filePath := filepath.Join(targetDir, sanitizedFilename)

	// Validate that the file path is within the target directory (prevent path traversal)
	if !isPathSafe(filePath, targetDir) {
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"invalid_path",
			"Invalid file path detected",
		))
		return
	}

	// Check if file exists and get file info
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			c.JSON(http.StatusNotFound, NewErrorResponse(
				http.StatusNotFound,
				"file_not_found",
				fmt.Sprintf("The requested file '%s' was not found in the %s directory", sanitizedFilename, dirType),
			))
			return
		}
		log.Error().Err(err).Str("filePath", filePath).Str("dirType", dirType).Msg("Failed to access file")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"file_access_error",
			"Failed to access the requested file",
		))
		return
	}

	// Check if it's a directory (not allowed for deletion via this endpoint)
	if fileInfo.IsDir() {
		log.Warn().Str("filename", sanitizedFilename).Str("dirType", dirType).Msg("Attempted to delete a directory")
		c.JSON(http.StatusBadRequest, NewErrorResponse(
			http.StatusBadRequest,
			"invalid_file_type",
			"Cannot delete a directory using this endpoint",
		))
		return
	}

	// Attempt to delete the file
	err = os.Remove(filePath)
	if err != nil {
		// Check if it's a permission error
		if os.IsPermission(err) {
			log.Error().Err(err).Str("filePath", filePath).Str("dirType", dirType).Msg("Permission denied for file deletion")
			c.JSON(http.StatusForbidden, NewErrorResponse(
				http.StatusForbidden,
				"permission_denied",
				"Permission denied: cannot delete the requested file",
			))
			return
		}
		// Generic error for other cases
		log.Error().Err(err).Str("filePath", filePath).Str("dirType", dirType).Msg("Failed to delete file")
		c.JSON(http.StatusInternalServerError, NewErrorResponse(
			http.StatusInternalServerError,
			"file_deletion_error",
			"Failed to delete the requested file",
		))
		return
	}



	// Return success response
	c.JSON(http.StatusOK, DeleteResponse{
		Success:  true,
		Message:  fmt.Sprintf("File '%s' deleted successfully from %s directory", sanitizedFilename, dirType),
		Filename: sanitizedFilename,
	})
}