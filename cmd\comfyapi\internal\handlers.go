package internal

import (
    "io"
    "net/http"
    "strings"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/rs/zerolog/log"
)

const (
    // Maximum file size: 100MB
    MaxFileSize = 100 * 1024 * 1024
    // Proxy timeout: 30 seconds
    ProxyTimeout = 30 * time.Second
)

// handleFileUpload processes multipart form file uploads
func handleFileUpload(c *gin.Context) {
    handleGenericUpload(c, Options.Comfyui.UploadDir, "upload")
}

// handleListFiles lists all files in the ComfyUI output directory
func handleListFiles(c *gin.Context) {
    handleGenericList(c, Options.Comfyui.OutputDir, "output")
}

// handleDownloadFile serves files from the ComfyUI output directory
func handleDownloadFile(c *gin.Context) {
    handleGenericDownload(c, Options.Comfyui.OutputDir, "output")
}

// handleDeleteFile removes files from the ComfyUI output directory
func handleDeleteFile(c *gin.Context) {
    handleGenericDelete(c, Options.Comfyui.OutputDir, "output")
}

// handleDeleteUploadFile removes files from the ComfyUI upload directory
func handleDeleteUploadFile(c *gin.Context) {
    handleGenericDelete(c, Options.Comfyui.UploadDir, "upload")
}

// handleAdapterProxy forwards all requests to ComfyUI service
func handleAdapterProxy(c *gin.Context) {
    // Get ComfyUI URL from configuration
    comfyuiURL := strings.TrimRight(Options.Comfyui.Url, "/")
    if comfyuiURL == "" {
        log.Error().
            Str("clientIP", c.ClientIP()).
            Str("requestPath", c.Request.URL.Path).
            Msg("ComfyUI URL not configured for proxy request")
        c.JSON(http.StatusInternalServerError, NewErrorResponse(
            http.StatusInternalServerError,
            "config_error",
            "ComfyUI URL not configured",
        ))
        return
    }

    // Get the path after /api/adapter
    // The route should capture everything after /api/adapter/
    requestPath := c.Param("path")
    if requestPath == "" {
        // If no path parameter, this might be a direct /api/adapter request
        requestPath = "/"
    }

    // Ensure the path starts with /
    if !strings.HasPrefix(requestPath, "/") {
        requestPath = "/" + requestPath
    }

    // Construct the target URL
    targetURL := comfyuiURL + requestPath

    // Add query parameters if they exist
    if c.Request.URL.RawQuery != "" {
        targetURL += "?" + c.Request.URL.RawQuery
    }

    // Create HTTP client with timeout
    client := &http.Client{
        Timeout: ProxyTimeout,
    }

    // Create new request
    req, err := http.NewRequestWithContext(c.Request.Context(), c.Request.Method, targetURL, c.Request.Body)
    if err != nil {
        c.JSON(http.StatusInternalServerError, NewErrorResponse(
            http.StatusInternalServerError,
            "proxy_error",
            "Failed to create proxy request",
        ))
        return
    }

    // Copy headers from original request (excluding hop-by-hop headers)
    for name, values := range c.Request.Header {
        // Skip hop-by-hop headers
        if isHopByHopHeader(name) {
            continue
        }
        for _, value := range values {
            req.Header.Add(name, value)
        }
    }

    // Set X-Forwarded-For header
    //if clientIP := c.ClientIP(); clientIP != "" {
    //    req.Header.Set("X-Forwarded-For", clientIP)
    //}

    // Make the request to ComfyUI
    resp, err := client.Do(req)
    if err != nil {
        log.Error().Err(err).Str("targetURL", targetURL).Msg("Failed to proxy request to ComfyUI")

        // Check if it's a timeout error
        if strings.Contains(err.Error(), "timeout") {
            c.JSON(http.StatusGatewayTimeout, NewTimeoutError("Request to ComfyUI service timed out"))
            return
        }

        // Check if it's a connection error
        if strings.Contains(err.Error(), "connection refused") || strings.Contains(err.Error(), "no such host") {
            c.JSON(http.StatusBadGateway, NewServiceUnavailableError("ComfyUI"))
            return
        }

        // Generic network error
        c.JSON(http.StatusBadGateway, NewErrorResponse(
            http.StatusBadGateway,
            "proxy_error",
            "Failed to connect to ComfyUI service",
        ))
        return
    }
    defer resp.Body.Close()

    // Copy response headers (excluding hop-by-hop headers)
    for name, values := range resp.Header {
        if isHopByHopHeader(name) {
            continue
        }
        for _, value := range values {
            c.Header(name, value)
        }
    }

    // Set response status code
    c.Status(resp.StatusCode)

    // Stream response body
    _, err = io.Copy(c.Writer, resp.Body)
    if err != nil {
        log.Error().Err(err).Str("targetURL", targetURL).Msg("Failed to stream response from ComfyUI")
        // Note: We can't send a JSON error response here since we've already started streaming
        return
    }
}

// isHopByHopHeader checks if a header is a hop-by-hop header that should not be forwarded
func isHopByHopHeader(header string) bool {
    hopByHopHeaders := []string{
        "Connection",
        "Keep-Alive",
        "Proxy-Authenticate",
        "Proxy-Authorization",
        "Te",
        "Trailers",
        "Transfer-Encoding",
        "Upgrade",
    }

    headerLower := strings.ToLower(header)
    for _, hopHeader := range hopByHopHeaders {
        if strings.ToLower(hopHeader) == headerLower {
            return true
        }
    }
    return false
}
