package internal

import (
    "omnix/toolkit/wrapper/kitcomfyui"
)

// Type aliases for backward compatibility and cleaner code
type FileInfo = kitcomfyui.FileInfo
type FileListResponse = kitcomfyui.FileListResponse
type UploadResponse = kitcomfyui.UploadResponse
type DeleteResponse = kitcomfyui.DeleteResponse
type ErrorResponse = kitcomfyui.ErrorResponse

// Re-export helper functions for convenience
var (
    NewErrorResponse = kitcomfyui.NewErrorResponse
    NewValidationError = kitcomfyui.NewValidationError
    NewNotFoundError = kitcomfyui.NewNotFoundError
    NewPermissionError = kitcomfyui.NewPermissionError
    NewConfigurationError = kitcomfyui.NewConfigurationError
    NewFileSystemError = kitcomfyui.NewFileSystemError
    NewNetworkError = kitcomfyui.NewNetworkError
    NewProxyError = kitcomfyui.NewProxyError
    NewSecurityError = kitcomfyui.NewSecurityError
    NewTimeoutError = kitcomfyui.NewTimeoutError
    NewServiceUnavailableError = kitcomfyui.NewServiceUnavailableError
)


