package internal

import (
    "fmt"
    "net/http"
    "os"
    "os/exec"
    "path/filepath"
    "strconv"
    "strings"
    "time"

    "omnix/cmd/comfyapi/pscript"
    "omnix/toolkit/wrapper/kitcomfyui"

    "github.com/gin-gonic/gin"
)

// photoshop自动化 20250908

// RegisterPhotoshopRoutes adds all Photoshop file management routes to the Gin router
func RegisterPhotoshopRoutes(r *gin.Engine) {
    // Create Photoshop API route group
    ps := r.Group("/ps")
    {
        // Photoshop automation operations
        ps.POST("/remove-white-background", handleRemoveWhiteBackground)
        ps.POST("/combine", handleCombine)

        // PersistDir operations
        persist := ps.Group("/persist")
        {
            persist.POST("/upload", handlePersistUpload)
            persist.GET("/files", handlePersistList)
            persist.GET("/files/:filename", handlePersistDownload)
            persist.DELETE("/files/:filename", handlePersistDelete)
            persist.POST("/check", handlePersistCheck)
        }

        // UploadDir operations
        upload := ps.Group("/upload")
        {
            upload.POST("/upload", handleUploadUpload)
            upload.GET("/files", handleUploadList)
            upload.GET("/files/:filename", handleUploadDownload)
            upload.DELETE("/files/:filename", handleUploadDelete)
        }

        // OutputDir operations (read-only)
        output := ps.Group("/output")
        {
            output.GET("/files", handleOutputList)
            output.GET("/files/:filename", handleOutputDownload)
            output.DELETE("/files/:filename", handleOutputDelete)
        }

        // Copy file from output to upload directory
        ps.POST("/copy-to-upload", handleCopyOutputToUpload)
    }
}

// PhotoshopScriptParams 定义 Photoshop 脚本执行的参数
type PhotoshopScriptParams struct {
    ScriptName     string            // 脚本文件名
    OutputFilename string            // 输出文件名
    Replacements   map[string]string // 脚本模板中的变量替换
}

// PhotoshopScriptResult 定义 Photoshop 脚本执行的结果
type PhotoshopScriptResult struct {
    Success        bool
    OutputFilename string
    LogContent     string
    Message        string
}

// executePhotoshopScript 执行 Photoshop 脚本的通用函数
func executePhotoshopScript(params PhotoshopScriptParams) (*PhotoshopScriptResult, error) {
    // 确保输出目录存在
    if err := ensureDirectoryExists(Options.Photoshop.OutputDir); err != nil {
        return nil, fmt.Errorf("failed to create output directory: %w", err)
    }

    // 从pscript包读取embed的脚本模板
    scriptTemplate, err := pscript.Scripts.ReadFile(params.ScriptName)
    if err != nil {
        return nil, fmt.Errorf("failed to read Photoshop script template: %w", err)
    }

    // 构建日志文件路径并删除旧日志
    logFilePath := filepath.Join(Options.Photoshop.OutputDir, "run.log")
    _ = os.Remove(logFilePath)

    // 替换脚本模板中的变量
    scriptContent := string(scriptTemplate)
    for placeholder, value := range params.Replacements {
        // 使用strconv.Quote自动处理路径转义，然后去掉外层引号
        escapedValue := strings.Trim(strconv.Quote(value), "\"")
        scriptContent = strings.ReplaceAll(scriptContent, placeholder, escapedValue)
    }

    // 将脚本写入执行文件
    execScriptPath := filepath.Join(Options.Photoshop.UploadDir, "exec.jsx")
    if err := os.WriteFile(execScriptPath, []byte(scriptContent), 0644); err != nil {
        return nil, fmt.Errorf("failed to write execution script: %w", err)
    }
    defer os.Remove(execScriptPath)

    // 执行Photoshop脚本
    _ = exec.Command("cmd.exe", "/c", Options.Photoshop.Exec, "-r", execScriptPath).Run()
    os.Remove(execScriptPath)

    // 检查输出文件是否存在
    outputFilePath := filepath.Join(Options.Photoshop.OutputDir, params.OutputFilename)
    var finalOutputFilename string
    if _, err := os.Stat(outputFilePath); err == nil {
        finalOutputFilename = params.OutputFilename
    } else {
        finalOutputFilename = ""
    }

    time.Sleep(time.Millisecond * 150)

    // 读取日志文件内容
    var logContent string
    if logData, err := os.ReadFile(logFilePath); err == nil {
        logContent = string(logData)
    } else {
        logContent = "No log available"
    }

    // 构建结果
    var message string
    if finalOutputFilename != "" {
        message = "ok"
    } else {
        message = "Processing completed but output file not found"
    }

    return &PhotoshopScriptResult{
        Success:        finalOutputFilename != "",
        OutputFilename: finalOutputFilename,
        LogContent:     logContent,
        Message:        message,
    }, nil
}

func handleRemoveWhiteBackground(c *gin.Context) {
    req := kitcomfyui.RemoveWhiteBackgroundRequest{}
    err := c.BindJSON(&req)
    if err != nil {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid JSON request body"))
        return
    }

    // 对filename进行检查,文件是否存在于ps的upload文件夹
    if req.Filename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("Filename is required"))
        return
    }

    // 清理文件名防止路径遍历攻击
    sanitizedFilename := sanitizeFilename(req.Filename)
    if sanitizedFilename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid filename provided"))
        return
    }

    // 检查输入文件是否存在
    inputFilePath := filepath.Join(Options.Photoshop.UploadDir, sanitizedFilename)
    if !isPathSafe(inputFilePath, Options.Photoshop.UploadDir) {
        c.JSON(http.StatusBadRequest, NewSecurityError("Invalid file path detected"))
        return
    }

    if _, err := os.Stat(inputFilePath); os.IsNotExist(err) {
        c.JSON(http.StatusNotFound, NewNotFoundError(fmt.Sprintf("file '%s' in upload directory", sanitizedFilename)))
        return
    }

    // 去掉原文件的扩展名，然后添加.png扩展名
    filenameWithoutExt := strings.TrimSuffix(sanitizedFilename, filepath.Ext(sanitizedFilename))
    outputFilename := filenameWithoutExt + ".png"
    outputFilePath := filepath.Join(Options.Photoshop.OutputDir, outputFilename)
    logFilePath := filepath.Join(Options.Photoshop.OutputDir, "run.log")

    // 准备脚本执行参数
    params := PhotoshopScriptParams{
        ScriptName:     "rmwhitebg.tsx",
        OutputFilename: outputFilename,
        Replacements: map[string]string{
            "{{FILE_INPUT}}":  inputFilePath,
            "{{FILE_OUTPUT}}": outputFilePath,
            "{{FILE_LOG}}":    logFilePath,
        },
    }

    // 执行脚本
    result, err := executePhotoshopScript(params)
    if err != nil {
        c.JSON(http.StatusInternalServerError, NewFileSystemError(err.Error()))
        return
    }

    // 返回响应
    response := kitcomfyui.RemoveWhiteBackgroundResponse{
        Success:  result.Success,
        Filename: result.OutputFilename,
        Log:      result.LogContent,
        Message:  result.Message,
    }

    c.JSON(http.StatusOK, response)
}

func handleCombine(c *gin.Context) {
    req := kitcomfyui.CombineRequest{}
    err := c.BindJSON(&req)
    if err != nil {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid JSON request body"))
        return
    }

    // 验证必需的参数
    if req.PsdFilename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("PsdFilename is required"))
        return
    }
    if req.MaterialFilename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("MaterialFilename is required"))
        return
    }

    // 清理文件名防止路径遍历攻击
    sanitizedPsdFilename := sanitizeFilename(req.PsdFilename)
    sanitizedMaterialFilename := sanitizeFilename(req.MaterialFilename)

    if sanitizedPsdFilename == "" || sanitizedMaterialFilename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid filename provided"))
        return
    }

    // 生成输出文件名
    materialFilenameWithoutExt := strings.TrimSuffix(sanitizedMaterialFilename, filepath.Ext(sanitizedMaterialFilename))
    outputFilename := materialFilenameWithoutExt + "_combined.png"

    // 构建完整文件路径
    psdFilePath := filepath.Join(Options.Photoshop.PersistDir, sanitizedPsdFilename)
    materialFilePath := filepath.Join(Options.Photoshop.UploadDir, sanitizedMaterialFilename)
    outputFilePath := filepath.Join(Options.Photoshop.OutputDir, outputFilename)
    logFilePath := filepath.Join(Options.Photoshop.OutputDir, "run.log")

    // 检查路径安全性
    if !isPathSafe(psdFilePath, Options.Photoshop.PersistDir) ||
        !isPathSafe(materialFilePath, Options.Photoshop.UploadDir) ||
        !isPathSafe(outputFilePath, Options.Photoshop.OutputDir) ||
        !isPathSafe(logFilePath, Options.Photoshop.OutputDir) {
        c.JSON(http.StatusBadRequest, NewSecurityError("Invalid file path detected"))
        return
    }

    // 检查输入文件是否存在
    if _, err := os.Stat(psdFilePath); os.IsNotExist(err) {
        c.JSON(http.StatusNotFound, NewNotFoundError(fmt.Sprintf("PSD file '%s' in persist directory", sanitizedPsdFilename)))
        return
    }

    if _, err := os.Stat(materialFilePath); os.IsNotExist(err) {
        c.JSON(http.StatusNotFound, NewNotFoundError(fmt.Sprintf("Material file '%s' in upload directory", sanitizedMaterialFilename)))
        return
    }

    // 准备脚本执行参数
    params := PhotoshopScriptParams{
        ScriptName:     "combine.tsx",
        OutputFilename: outputFilename,
        Replacements: map[string]string{
            "{{FILE_PSD}}":      psdFilePath,
            "{{FILE_OUTPUT}}":   outputFilePath,
            "{{FILE_MATERIAL}}": materialFilePath,
            "{{FILE_LOG}}":      logFilePath,
        },
    }

    // 执行脚本
    result, err := executePhotoshopScript(params)
    if err != nil {
        c.JSON(http.StatusInternalServerError, NewFileSystemError(err.Error()))
        return
    }

    // 返回响应
    response := kitcomfyui.CombineResponse{
        Success:  result.Success,
        Filename: result.OutputFilename,
        Log:      result.LogContent,
        Message:  result.Message,
    }

    c.JSON(http.StatusOK, response)
}

// PersistDir handlers

func handlePersistUpload(c *gin.Context) {
    handleGenericUpload(c, Options.Photoshop.PersistDir, "persist")
}

func handlePersistList(c *gin.Context) {
    handleGenericList(c, Options.Photoshop.PersistDir, "persist")
}

func handlePersistDownload(c *gin.Context) {
    handleGenericDownload(c, Options.Photoshop.PersistDir, "persist")
}

func handlePersistDelete(c *gin.Context) {
    handleGenericDelete(c, Options.Photoshop.PersistDir, "persist")
}

// UploadDir handlers

func handleUploadUpload(c *gin.Context) {
    handleGenericUpload(c, Options.Photoshop.UploadDir, "upload")
}

func handleUploadList(c *gin.Context) {
    handleGenericList(c, Options.Photoshop.UploadDir, "upload")
}

func handleUploadDownload(c *gin.Context) {
    handleGenericDownload(c, Options.Photoshop.UploadDir, "upload")
}

func handleUploadDelete(c *gin.Context) {
    handleGenericDelete(c, Options.Photoshop.UploadDir, "upload")
}

// OutputDir handlers

func handleOutputList(c *gin.Context) {
    handleGenericList(c, Options.Photoshop.OutputDir, "output")
}

func handleOutputDownload(c *gin.Context) {
    handleGenericDownload(c, Options.Photoshop.OutputDir, "output")
}

func handleOutputDelete(c *gin.Context) {
    handleGenericDelete(c, Options.Photoshop.OutputDir, "output")
}

// handlePersistCheck checks if a file exists in persist directory and validates its SHA1
func handlePersistCheck(c *gin.Context) {
    req := kitcomfyui.CheckFileRequest{}
    err := c.BindJSON(&req)
    if err != nil {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid JSON request body"))
        return
    }

    // 验证必需的参数
    if req.Filename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("Filename is required"))
        return
    }
    if req.Sha1 == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("SHA1 is required"))
        return
    }

    // 清理文件名防止路径遍历攻击
    sanitizedFilename := sanitizeFilename(req.Filename)
    if sanitizedFilename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid filename provided"))
        return
    }

    // 检查 persist 目录是否配置
    if Options.Photoshop.PersistDir == "" {
        c.JSON(http.StatusInternalServerError, NewConfigurationError("persist directory not configured"))
        return
    }

    // 构建完整文件路径
    filePath := filepath.Join(Options.Photoshop.PersistDir, sanitizedFilename)

    // 检查路径安全性
    if !isPathSafe(filePath, Options.Photoshop.PersistDir) {
        c.JSON(http.StatusBadRequest, NewSecurityError("Invalid file path detected"))
        return
    }

    // 检查文件是否存在
    fileExists := false
    sha1Match := false
    actualSha1 := ""
    message := ""

    if _, err := os.Stat(filePath); err == nil {
        fileExists = true

        // 计算文件的 SHA1
        calculatedSha1, err := calculateFileSHA1(filePath)
        if err != nil {
            c.JSON(http.StatusInternalServerError, NewFileSystemError("Failed to calculate file SHA1"))
            return
        }

        actualSha1 = calculatedSha1
        
        // 比较 SHA1（不区分大小写）
        if strings.EqualFold(strings.TrimSpace(req.Sha1), strings.TrimSpace(calculatedSha1)) {
            sha1Match = true
            message = "File exists and SHA1 matches"
        } else {
            message = "File exists but SHA1 does not match"
        }
    } else if os.IsNotExist(err) {
        message = "File does not exist"
    } else {
        c.JSON(http.StatusInternalServerError, NewFileSystemError("Failed to access file"))
        return
    }

    // 构建响应
    response := kitcomfyui.CheckFileResponse{
        Success:      true,
        Valid:        fileExists && sha1Match,
        FileExists:   fileExists,
        Sha1Match:    sha1Match,
        ActualSha1:   actualSha1,
        ExpectedSha1: req.Sha1,
        Message:      message,
    }

    c.JSON(http.StatusOK, response)
}

// handleCopyOutputToUpload copies a file from output directory to upload directory
func handleCopyOutputToUpload(c *gin.Context) {
    req := kitcomfyui.CopyOutputToUploadRequest{}
    err := c.BindJSON(&req)
    if err != nil {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid JSON request body"))
        return
    }

    // 验证必需的参数
    if req.Filename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("Filename is required"))
        return
    }

    // 清理文件名防止路径遍历攻击
    sanitizedFilename := sanitizeFilename(req.Filename)
    if sanitizedFilename == "" {
        c.JSON(http.StatusBadRequest, NewValidationError("Invalid filename provided"))
        return
    }

    // 构建源文件和目标文件路径
    sourceFilePath := filepath.Join(Options.Photoshop.OutputDir, sanitizedFilename)
    targetFilePath := filepath.Join(Options.Photoshop.UploadDir, sanitizedFilename)

    // 检查路径安全性
    if !isPathSafe(sourceFilePath, Options.Photoshop.OutputDir) ||
        !isPathSafe(targetFilePath, Options.Photoshop.UploadDir) {
        c.JSON(http.StatusBadRequest, NewSecurityError("Invalid file path detected"))
        return
    }

    // 检查源文件是否存在
    if _, err := os.Stat(sourceFilePath); os.IsNotExist(err) {
        c.JSON(http.StatusNotFound, NewNotFoundError(fmt.Sprintf("file '%s' in output directory", sanitizedFilename)))
        return
    }

    // 确保目标目录存在
    if err := ensureDirectoryExists(Options.Photoshop.UploadDir); err != nil {
        c.JSON(http.StatusInternalServerError, NewFileSystemError("Failed to create upload directory"))
        return
    }

    // 复制文件
    sourceFile, err := os.Open(sourceFilePath)
    if err != nil {
        c.JSON(http.StatusInternalServerError, NewFileSystemError("Failed to open source file"))
        return
    }
    defer sourceFile.Close()

    targetFile, err := os.Create(targetFilePath)
    if err != nil {
        c.JSON(http.StatusInternalServerError, NewFileSystemError("Failed to create target file"))
        return
    }
    defer targetFile.Close()

    // 执行文件复制
    _, err = targetFile.ReadFrom(sourceFile)
    if err != nil {
        c.JSON(http.StatusInternalServerError, NewFileSystemError("Failed to copy file"))
        return
    }

    // 构建响应
    response := kitcomfyui.CopyOutputToUploadResponse{
        Success:    true,
        Message:    "File copied successfully",
        Filename:   sanitizedFilename,
        SourcePath: "output/" + sanitizedFilename,
        TargetPath: "upload/" + sanitizedFilename,
    }

    c.JSON(http.StatusOK, response)
}
