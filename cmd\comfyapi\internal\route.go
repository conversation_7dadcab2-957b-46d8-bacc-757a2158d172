package internal

import (
    "github.com/gin-gonic/gin"
    "github.com/rs/zerolog/log"
)

// RegisterRoutes adds all ComfyUI file management routes to the Gin router
func RegisterRoutes(r *gin.Engine) {
    
    // Create API route group with common middleware
    api := r.Group("/api")
    {
        // Add request logging middleware for API routes
        //api.Use(requestLoggingMiddleware())
        
        // File upload endpoint - POST /api/upload
        api.POST("/upload", handleFileUpload)
        
        // Upload directory file management
        api.DELETE("/upload/:filename", handleDeleteUploadFile) // DELETE /api/upload/{filename} - Delete upload file
        
        // File management endpoints for output directory
        api.GET("/files", handleListFiles)                    // GET /api/files - List all files
        api.GET("/files/:filename", handleDownloadFile)       // GET /api/files/{filename} - Download file
        api.DELETE("/files/:filename", handleDeleteFile)      // DELETE /api/files/{filename} - Delete file
        
        // ComfyUI adapter proxy endpoints - captures all sub-paths
        api.Any("/adapter", handleAdapterProxy)               // ANY /api/adapter - Direct adapter requests
        api.Any("/adapter/*path", handleAdapterProxy)         // ANY /api/adapter/* - Wildcard adapter proxy
    }
    
    // Register Photoshop API routes with /ps prefix
    RegisterPhotoshopRoutes(r)
}


// requestLoggingMiddleware logs incoming requests and their responses
func requestLoggingMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Log incoming request
        log.Info().
            Str("method", c.Request.Method).
            Str("path", c.Request.URL.Path).
            Str("query", c.Request.URL.RawQuery).
            Str("clientIP", c.ClientIP()).
            Str("userAgent", c.Request.UserAgent()).
            Msg("Incoming API request")
        
        // Process request
        c.Next()
        
        // Log response
        statusCode := c.Writer.Status()
        logEvent := log.Info()
        
        // Use different log levels based on status code
        if statusCode >= 400 && statusCode < 500 {
            logEvent = log.Warn()
        } else if statusCode >= 500 {
            logEvent = log.Error()
        }
        
        logEvent.
            Str("method", c.Request.Method).
            Str("path", c.Request.URL.Path).
            Int("statusCode", statusCode).
            Str("clientIP", c.ClientIP()).
            Msg("API request completed")
    }
}