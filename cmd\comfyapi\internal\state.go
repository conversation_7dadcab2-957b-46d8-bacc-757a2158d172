package internal

import (
	"omnix/state"
	"omnix/toolkit/wrapper/kitviper"
	"os"
	"time"

	"github.com/qwenode/rr"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

type (
	Service struct {
		Port int `mapstructure:"port"`
	}
	Comfyui struct {
		Url       string `mapstructure:"url"`
		UploadDir string `mapstructure:"upload_dir"`
		OutputDir string `mapstructure:"output_dir"`
	}
	Photoshop struct {
		Exec       string `mapstructure:"exec"`        // 执行路径
		PersistDir string `mapstructure:"persist_dir"` // 持久化素材目录
		UploadDir  string `mapstructure:"upload_dir"`  // 临时素材目录
		OutputDir  string `mapstructure:"output_dir"`  // 合成结果目录
	}
	options struct {
		// PS桥接配置 20250909
		Photoshop Photoshop `mapstructure:"ps"`
		Service   Service   `mapstructure:"service"`
		// Comfyui桥接配置 20250909
		Comfyui Comfyui `mapstructure:"comfyui"`
	}
)

var (
	Options options
)

func init() {
	err := kitviper.ReadToml(rr.FileWithWorkDirectory("comfyapi.toml"), &Options)
	if err != nil {
		log.Err(err).Send()
		time.Sleep(time.Second * 30)
		panic(err)
	}
	log.Logger = log.
		Output(zerolog.ConsoleWriter{Out: os.Stdout, NoColor: true, TimeFormat: "2006-01-02T15:04:05Z"}).
		Level(zerolog.Level(state.LogLevel))
}
