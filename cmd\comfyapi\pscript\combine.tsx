var inputFile = new File( "{{FILE_PSD}}");
var outputFile = new File("{{FILE_OUTPUT}}");
var materialFile = new File("{{FILE_MATERIAL}}")
var logFile = new File( "{{FILE_LOG}}");


var log = logFile.open("w")
var pngSaveOptions = new PNGSaveOptions();
pngSaveOptions.compression = 3;
pngSaveOptions.interlaced = false;

var doc = app.open(inputFile);

var idslct = charIDToTypeID( "slct" );
    var desc553 = new ActionDescriptor();
    var idnull = charIDToTypeID( "null" );
        var ref24 = new ActionReference();
        var idLyr = charIDToTypeID( "Lyr " );
        ref24.putName( idLyr, "aaa" );
    desc553.putReference( idnull, ref24 );
    var idMkVs = charIDToTypeID( "MkVs" );
    desc553.putBoolean( idMkVs, false );
    var idLyrI = charIDToTypeID( "LyrI" );
        var list21 = new ActionList();
        list21.putInteger( 16 );
    desc553.putList( idLyrI, list21 );
executeAction( idslct, desc553, DialogModes.NO );

var idslct = stringIDToTypeID( "placedLayerEditContents" );
    var desc572 = new ActionDescriptor();
    var idDocI = charIDToTypeID( "DocI" );
    desc572.putInteger( idDocI, 136 );
    var idLyrI = charIDToTypeID( "LyrI" );
    desc572.putInteger( idLyrI, 16 );
executeAction( idslct, desc572, DialogModes.NO );

var idPlc = charIDToTypeID( "Plc " );
    var desc1717 = new ActionDescriptor();
    var idIdnt = charIDToTypeID( "Idnt" );
    desc1717.putInteger( idIdnt, 18 );
    var idnull = charIDToTypeID( "null" );
    desc1717.putPath( idnull, materialFile  );
    var idFTcs = charIDToTypeID( "FTcs" );
    var idQCSt = charIDToTypeID( "QCSt" );
    var idQcsa = charIDToTypeID( "Qcsa" );
    desc1717.putEnumerated( idFTcs, idQCSt, idQcsa );
    var idOfst = charIDToTypeID( "Ofst" );
        var desc1718 = new ActionDescriptor();
        var idHrzn = charIDToTypeID( "Hrzn" );
        var idRlt = charIDToTypeID( "#Rlt" );
        desc1718.putUnitDouble( idHrzn, idRlt, 0.000000 );
        var idVrtc = charIDToTypeID( "Vrtc" );
        var idRlt = charIDToTypeID( "#Rlt" );
        desc1718.putUnitDouble( idVrtc, idRlt, 0.000000 );
    var idOfst = charIDToTypeID( "Ofst" );
    desc1717.putObject( idOfst, idOfst, desc1718 );
    var idreplaceLayer = stringIDToTypeID( "replaceLayer" );
        var desc1719 = new ActionDescriptor();
        var idT = charIDToTypeID( "T   " );
            var ref105 = new ActionReference();
            var idLyr = charIDToTypeID( "Lyr " );
            ref105.putIdentifier( idLyr, 18 );
        desc1719.putReference( idT, ref105 );
    var idPlc = charIDToTypeID( "Plc " );
    desc1717.putObject( idreplaceLayer, idPlc, desc1719 );
executeAction( idPlc, desc1717, DialogModes.NO );

var idCls = charIDToTypeID( "Cls " );
    var desc1724 = new ActionDescriptor();
    var idAs = charIDToTypeID( "As  " );
        var desc1725 = new ActionDescriptor();
        var idmaximizeCompatibility = stringIDToTypeID( "maximizeCompatibility" );
        desc1725.putBoolean( idmaximizeCompatibility, true );
    var idPhteight = charIDToTypeID( "Pht8" );
    desc1724.putObject( idAs, idPhteight, desc1725 );
    var idIn = charIDToTypeID( "In  " );
    var idDocI = charIDToTypeID( "DocI" );
    desc1724.putInteger( idDocI, 649 );
    var idLwCs = charIDToTypeID( "LwCs" );
    desc1724.putBoolean( idLwCs, true );
    var idSvng = charIDToTypeID( "Svng" );
    var idYsN = charIDToTypeID( "YsN " );
    var idYs = charIDToTypeID( "Ys  " );
    desc1724.putEnumerated( idSvng, idYsN, idYs );
    var idforceNotify = stringIDToTypeID( "forceNotify" );
    desc1724.putBoolean( idforceNotify, true );
executeAction( idCls, desc1724, DialogModes.NO );

doc.saveAs(outputFile, pngSaveOptions, true);
doc.close(SaveOptions.DONOTSAVECHANGES);
logFile.writeln("success");
logFile.close();