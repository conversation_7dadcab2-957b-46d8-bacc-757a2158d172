var inputFile = new File( "{{FILE_INPUT}}");
var outputFile = new File("{{FILE_OUTPUT}}");
var logFile = new File( "{{FILE_LOG}}");


var log = logFile.open("w")
var pngSaveOptions = new PNGSaveOptions();
pngSaveOptions.compression = 3;
pngSaveOptions.interlaced = false;

var doc = app.open(inputFile);

var idsetd = charIDToTypeID( "setd" );
    var desc1639 = new ActionDescriptor();
    var idnull = charIDToTypeID( "null" );
        var ref99 = new ActionReference();
        var idLyr = charIDToTypeID( "Lyr " );
        var idBckg = charIDToTypeID( "Bckg" );
        ref99.putProperty( idLyr, idBckg );
    desc1639.putReference( idnull, ref99 );
    var idT = charIDToTypeID( "T   " );
        var desc1640 = new ActionDescriptor();
        var idOpct = charIDToTypeID( "Opct" );
        var idPrc = charIDToTypeID( "#Prc" );
        desc1640.putUnitDouble( idOpct, idPrc, 100.000000 );
        var idMd = charIDToTypeID( "Md  " );
        var idBlnM = charIDToTypeID( "BlnM" );
        var idNrml = charIDToTypeID( "Nrml" );
        desc1640.putEnumerated( idMd, idBlnM, idNrml );
    var idLyr = charIDToTypeID( "Lyr " );
    desc1639.putObject( idT, idLyr, desc1640 );
    var idLyrI = charIDToTypeID( "LyrI" );
    desc1639.putInteger( idLyrI, 2 );
executeAction( idsetd, desc1639, DialogModes.NO );
var idClrR = charIDToTypeID( "ClrR" );
    var desc1530 = new ActionDescriptor();
    var idFzns = charIDToTypeID( "Fzns" );
    desc1530.putInteger( idFzns, 40 );
    var idMnm = charIDToTypeID( "Mnm " );
        var desc1531 = new ActionDescriptor();
        var idLmnc = charIDToTypeID( "Lmnc" );
        desc1531.putDouble( idLmnc, 100.000000 );
        var idA = charIDToTypeID( "A   " );
        desc1531.putDouble( idA, 0.000000 );
        var idB = charIDToTypeID( "B   " );
        desc1531.putDouble( idB, 0.000000 );
    var idLbCl = charIDToTypeID( "LbCl" );
    desc1530.putObject( idMnm, idLbCl, desc1531 );
    var idMxm = charIDToTypeID( "Mxm " );
        var desc1532 = new ActionDescriptor();
        var idLmnc = charIDToTypeID( "Lmnc" );
        desc1532.putDouble( idLmnc, 100.000000 );
        var idA = charIDToTypeID( "A   " );
        desc1532.putDouble( idA, 0.000000 );
        var idB = charIDToTypeID( "B   " );
        desc1532.putDouble( idB, 0.000000 );
    var idLbCl = charIDToTypeID( "LbCl" );
    desc1530.putObject( idMxm, idLbCl, desc1532 );
    var idcolorModel = stringIDToTypeID( "colorModel" );
    desc1530.putInteger( idcolorModel, 0 );
executeAction( idClrR, desc1530, DialogModes.NO );

var idDlt = charIDToTypeID( "Dlt " );
executeAction( idDlt, undefined, DialogModes.NO );


var idsetd = charIDToTypeID( "setd" );
    var desc1660 = new ActionDescriptor();
    var idnull = charIDToTypeID( "null" );
        var ref100 = new ActionReference();
        var idChnl = charIDToTypeID( "Chnl" );
        var idfsel = charIDToTypeID( "fsel" );
        ref100.putProperty( idChnl, idfsel );
    desc1660.putReference( idnull, ref100 );
    var idT = charIDToTypeID( "T   " );
    var idOrdn = charIDToTypeID( "Ordn" );
    var idNone = charIDToTypeID( "None" );
    desc1660.putEnumerated( idT, idOrdn, idNone );
executeAction( idsetd, desc1660, DialogModes.NO );


doc.saveAs(outputFile, pngSaveOptions, true);
doc.close(SaveOptions.DONOTSAVECHANGES);
logFile.writeln("success");
logFile.close();