package main

import (
    "fmt"
    "io/fs"
    "net/http"
    adminservice "omnix/api/admin"
    "omnix/apps/dashboard"
    "omnix/state"
    "omnix/toolkit/ginzerolog"
    "strings"

    "github.com/gin-contrib/cors"
    "github.com/gin-gonic/gin"
    "github.com/rs/zerolog/log"
    "github.com/spf13/cobra"
)

var commandDashboard = &cobra.Command{
    Use:   "dashboard",
    Short: "管理后台",
    Run: func(cmd *cobra.Command, args []string) {
        if state.LogLevel > 0 {
            gin.SetMode(gin.ReleaseMode)
        }
        r := gin.New()
        r.SetTrustedProxies(nil)
        r.Use(gin.Recovery())
        r.Use(ginzerolog.DefaultZerologLogger())
        // 允许全部跨域 20250828
        if state.RemoteOptions.Admin.AllowAllCors {
            config := cors.DefaultConfig()
            config.AllowAllOrigins = true
            config.AllowHeaders = []string{"*"}
            config.AllowMethods = []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"}
            r.Use(cors.New(config))
        }
        // 前端发布 20250828
        sub, _ := fs.Sub(dashboard.Dist, "dist")
        staticServer := http.FileServer(http.FS(sub))
        r.GET("/assets/*filepath", func(c *gin.Context) {
            staticServer.ServeHTTP(c.Writer, c.Request)
        })
        r.GET("/", func(c *gin.Context) {
            file, _ := dashboard.Dist.ReadFile("dist/index.html")
            c.Writer.Header().Set("Content-Type", "text/html; charset=utf-8")
            c.Writer.Write(file)
            c.Writer.Flush()
        })
        r.NoRoute(func(c *gin.Context) {
            path := c.Request.URL.Path
            if strings.HasPrefix(path, "/.") || strings.HasPrefix(path, "/admin.") {
                c.Abort()
                return
            }
            file, _ := dashboard.Dist.ReadFile("dist/index.html")
            c.Writer.WriteHeader(http.StatusOK)
            c.Writer.Header().Set("Content-Type", "text/html; charset=utf-8")
            c.Writer.Write(file)
            c.Writer.Flush()
        })
        // Load admin API routes first
        adminservice.LoadRouter(r)
        
        port := 0
        if port <= 0 || port > 65535 {
            port = 28800
            log.Warn().Int("APP_API_PORT", port).Msg("未配置端口,使用系统默认")
        }
        r.Run(
            fmt.Sprintf(
                "0.0.0.0:%d",
                port,
            ),
        ) // listen and serve on 0.0.0.0:8080 (for windows "localhost:8080")
    },
    Args: cobra.NoArgs,
}

func init() {
    mainCommand.AddCommand(commandDashboard)
}
