package main

import (
    "fmt"
    "io/fs"
    "omnix/ddl/db1_migrations"
    "omnix/provider/db1c"

    _ "github.com/golang-migrate/migrate/v4/database/mongodb"
    "github.com/pressly/goose/v3"
    "github.com/rs/zerolog"
    "github.com/rs/zerolog/log"
    "github.com/spf13/cobra"
)

var commandDB = &cobra.Command{
    Use:   "db",
    Short: "数据库管理",
    Args:  cobra.NoArgs,
    PersistentPreRun: func(cmd *cobra.Command, args []string) {
        cmd.Root().PersistentPreRun(cmd, args)
        log.Logger = log.Logger.Level(zerolog.TraceLevel)
    },
}

func init() {
    commandDB.AddCommand(commandDBMigrateUp)
    commandDB.AddCommand(commandDBMigrateList)
    mainCommand.AddCommand(commandDB)
}

var commandDBMigrateUp = &cobra.Command{
    Use:   "migrate.up",
    Short: "升级数据库结构",
    Run: func(cmd *cobra.Command, args []string) {

        goose.SetBaseFS(db1_migrations.Migrations)
        err := goose.SetDialect(string(goose.DialectPostgres))
        if err != nil {
            log.Fatal().Err(err).Msg("failed to init dialect")
        }
        err = goose.Up(db1c.Get().GetDrv(), ".")

        if err != nil {
            log.Fatal().Err(err).Msg("failed to apply migrations")
        }
    },
    Args: cobra.NoArgs,
}
var commandDBMigrateList = &cobra.Command{
    Use:   "migrate.list",
    Short: "查看迁移文件列表",
    Run: func(cmd *cobra.Command, args []string) {
        matches, _ := fs.Glob(db1_migrations.Migrations, "*.sql")
        for _, match := range matches {
            fmt.Printf("MigrateFile: %s\n", match)
        }
    },
    Args: cobra.NoArgs,
}
