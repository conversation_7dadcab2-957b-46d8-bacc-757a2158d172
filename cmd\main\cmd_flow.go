package main

import (
    "context"
    flowservice "omnix/api/flow"
    "omnix/genpb/enumpb"
    "omnix/provider/flow"
    "omnix/state"
    "time"

    "github.com/rs/zerolog"
    "github.com/rs/zerolog/log"
    "github.com/spf13/cobra"
    "go.temporal.io/api/workflowservice/v1"
    "go.temporal.io/sdk/client"
    "go.temporal.io/sdk/worker"
    "google.golang.org/protobuf/types/known/durationpb"
)

var commandFlow = &cobra.Command{
    Use:   "flow",
    Short: "工作流管理",
    Args:  cobra.NoArgs,
    PersistentPreRun: func(cmd *cobra.Command, args []string) {
        cmd.Root().PersistentPreRun(cmd, args)
        log.Logger = log.Logger.Level(zerolog.TraceLevel)
    },
}

func init() {
    commandFlow.AddCommand(commandFlowWorker)
    commandFlow.AddCommand(commandFlowWorkerComfyui)
    commandFlow.AddCommand(commandFlowConsume)
    commandFlow.AddCommand(commandFlowSchedule)
    commandFlow.AddCommand(commandFlowCreateNamespace)
    mainCommand.AddCommand(commandFlow)
}

// 数据流向: 服务端>rabbitmq队列>mq消费者(限速,当前函数)>temporal(事务保障) 20250512
var commandFlowConsume = &cobra.Command{
    Use:   "consume",
    Short: "限速工作流队列消费者",
    Run: func(cmd *cobra.Command, args []string) {
        //var (
        //    // qos配置 20250512
        //    // 一般会在2台以上服务器启动consume,那么并发计算就是 启动的服务器*qos=总并发
        //    qosConfig = map[string]int{
        //        // 不涉及到shopify交互,要求尽快处理,允许短期任务阻塞 20250512
        //        enumpb.RABBIT_QUEUE_RATE_WORKFLOW.ToPascal(): 4,
        //        // 与shopify交互队列,要确保不触发限流的情况下尽快处理所有数据 20250512
        //        enumpb.RABBIT_QUEUE_RATE_SHOPIFY.ToPascal(): 2,
        //        // 图搜队列,图片转换等,要求worker必须是session共享的 20250512
        //        enumpb.RABBIT_QUEUE_RATE_IMAGE_SEARCH.ToPascal(): 1,
        //        // 采集队列,允许任务大批量阻塞 20250616
        //        enumpb.RABBIT_QUEUE_RATE_CRAWLER.ToPascal(): 4,
        //        // 1688产品处理队列,标注,审核,允许大量任务排队 20250620
        //        enumpb.RABBIT_QUEUE_RATE_ALIBABA.ToPascal(): 1,
        //        // 操作日志写入队列 20250626
        //        enumpb.RABBIT_QUEUE_RATE_LOGGING.ToPascal(): 1,
        //        // 此队列操作由cookie/csrf授权的shopify后台,类似任务都可以放入此间 20250626
        //        enumpb.RABBIT_QUEUE_RATE_SHOPIFY_COOKIE_ADMIN.ToPascal(): 1,
        //        // SOP批量操作,但不需要实时,但是需要尽快处理 20250724
        //        enumpb.RABBIT_QUEUE_RATE_SOP_BATCH.ToPascal():4, 
        //    }
        //)
        //
        //for queueName, qosRate := range qosConfig {
        //    go do.QueueConsumeWithQos(queueName, qosRate)
        //} 
        //select {}
    },
    Args: cobra.NoArgs,
}
var commandFlowWorkerComfyui = &cobra.Command{
    Use:   "worker.comfyui",
    Short: "执行Comfyui任务",
    Run: func(cmd *cobra.Command, args []string) {
        r := flow.Get().R()
        defer r.Close()
        w := worker.New(r, enumpb.FLOW_QUEUE_COMFYUI.ToState(), worker.Options{})
        flowservice.RegisterComfyui(w)
        err := w.Run(worker.InterruptCh())
        if err != nil {
            log.Err(err).Msg("worker.comfyui")
        }
    },
    Args: cobra.NoArgs,
}

var commandFlowWorker = &cobra.Command{
    Use:   "worker.default",
    Short: "工作流默认队列",
    Run: func(cmd *cobra.Command, args []string) {
        r := flow.Get().R()
        defer r.Close()
        w := worker.New(r, enumpb.FLOW_QUEUE_DEFAULT.ToState(), worker.Options{})
        flowservice.Register(w)
        err := w.Run(worker.InterruptCh())
        if err != nil {
            log.Err(err).Msg("worker.default")
        }
    },
    Args: cobra.NoArgs,
}
var commandFlowCreateNamespace = &cobra.Command{
    Use:   "create.namespace",
    Short: "创建命名空间",
    Run: func(cmd *cobra.Command, args []string) {
        if state.RemoteOptions.Temporal.Namespace == "default" || state.RemoteOptions.Temporal.Namespace == "" {
            log.Warn().Msg("默认命名空间为default或空,不需要创建")
            return
        }
        namespaceClient, err := client.NewNamespaceClient(client.Options{HostPort: state.RemoteOptions.Temporal.Host})
        if err != nil {
            log.Fatal().Err(err).Msg("create failed")
        }
        duration := time.Hour * 24 * 7
        err = namespaceClient.Register(context.Background(), &workflowservice.RegisterNamespaceRequest{
            Namespace:                        state.RemoteOptions.Temporal.Namespace,
            WorkflowExecutionRetentionPeriod: durationpb.New(duration),
        })
        if err != nil {
            log.Fatal().Err(err).Msg("create failed")
        } else {
            log.Info().Msg("created successfully")
        }
    },
    Args: cobra.NoArgs,
}

var commandFlowSchedule = &cobra.Command{
    Use:   "schedule",
    Short: "检查并创建定时任务",
    Run: func(cmd *cobra.Command, args []string) {
        r := flow.Get().R()
        defer r.Close()
        flowservice.RegisterSchedule(r)
        log.Info().Msg("执行完毕")
    },
    Args: cobra.NoArgs,
}
