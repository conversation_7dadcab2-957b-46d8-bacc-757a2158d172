package main

import (
    "fmt"
    partnerservice "omnix/api/partner"
    "omnix/state"
    "omnix/toolkit/ginzerolog"

    "github.com/gin-gonic/gin"
    "github.com/rs/zerolog/log"
    "github.com/spf13/cobra"
)

var commandPartner = &cobra.Command{
    Use:   "partner",
    Short: "对外合作接口",
    Run: func(cmd *cobra.Command, args []string) {
        if state.LogLevel > 0 {
            gin.SetMode(gin.ReleaseMode)
        }
        r := gin.New()
        r.SetTrustedProxies(nil)
        r.Use(gin.Recovery())
        r.Use(ginzerolog.DefaultZerologLogger())
        
        // Load admin API routes first
        partnerservice.LoadRouter(r)
        
        port := state.RemoteOptions.Partner.ListenPort
        if port <= 0 || port > 65535 {
            port = 28801
            log.Warn().Int("APP_API_PORT", port).Msg("未配置端口,使用系统默认")
        }
        r.Run(
            fmt.Sprintf(
                "0.0.0.0:%d",
                port,
            ),
        )
    },
    Args: cobra.NoArgs,
}

func init() {
    mainCommand.AddCommand(commandPartner)
}
