package main

import (
    "context"
    "crypto/ed25519"
    "crypto/rand"
    "encoding/hex"
    "omnix/ddl/db1/productitem"
    "omnix/genpb/enumpb"
    "omnix/provider/db1c"
    "omnix/toolkit/kitencrypt"

    "github.com/qwenode/rr"
    "github.com/qwenode/rr/random"
    "github.com/rs/zerolog/log"
    "github.com/spf13/cobra"
)

var commandTools = &cobra.Command{
    Use:   "tool",
    Short: "工具",
    Args:  cobra.NoArgs,
    PersistentPreRun: func(cmd *cobra.Command, args []string) {
        cmd.Root().PersistentPreRun(cmd, args)
        log.Logger = log.Logger.Level(-1)
    },
}

func init() {
    commandTools.AddCommand(commandJwtKey)
    commandTools.AddCommand(commandExportTitle)
    commandTools.AddCommand(commandTransfer)
    commandTools.AddCommand(commandCreateAdminUser)
    mainCommand.AddCommand(commandTools)
}

var commandCreateAdminUser = &cobra.Command{
    Use:   "create.admin",
    Short: "创建后台账号",
    Run: func(cmd *cobra.Command, args []string) {
        c := context.Background()
        exist, err := db1c.R().Administrator.Query().WhereUsername("adminx").Exist(c)
        if err != nil {
            log.Fatal().Err(err).Msg("<UNK>")
        }
        if exist {
            log.Fatal().Msg("账号adminx已经存在")
        }
        pwd := random.String(30)
        _, err = db1c.R().Administrator.Create().SetUsername("adminx").
            SetPassword(kitencrypt.GeneratePasswordHash(pwd)).SetState(enumpb.ADMINISTRATOR_STATE_ACTIVE.ToSnake()).
            Save(c)
        if err != nil {
            log.Fatal().Err(err).Msg("<UNK>")
        }
        log.Info().Str("账号", "adminx").Str("密码", pwd).Msg("<OK>")
    },
    Args: cobra.NoArgs,
}
var commandTransfer = &cobra.Command{
    Use:   "transfer",
    Short: "转移temu数据",
    Run: func(cmd *cobra.Command, args []string) {
        
    },
    Args: cobra.NoArgs,
}

var commandExportTitle = &cobra.Command{
    Use:   "title",
    Short: "批量导出产品标题,随机10000条",
    Run: func(cmd *cobra.Command, args []string) {
        var (
            c = context.Background()
        )

        item := db1c.R().ProductItem
        count, _ := item.Query().Count(c)
        log.Info().Int("总量", count).Send()
        maxi := 10000
        if count < maxi {
            maxi = count
        }
        int64s := make([]int64, 0, maxi)
        for i := 0; i < maxi; i++ {
            intRange := random.IntRange(1, count)
            int64s = append(int64s, int64(intRange))
        }
        all, err := db1c.R().ProductItem.Query().Select(productitem.FieldSpec).Where(productitem.IDIn(int64s...)).All(c)
        if err != nil {
            log.Err(err).Msg("<UNK>")
            return
        }
        txt := ""
        for _, item := range all {
            txt += item.Spec.Title + "\n"
        }
        rr.FilePutContents(rr.FileWithWorkDirectory("title.txt"), txt)
    },
    Args: cobra.NoArgs,
}

var commandJwtKey = &cobra.Command{
    Use:   "jwt.key",
    Short: "为jwt生成ed25519的秘钥,以加密token",
    Run: func(cmd *cobra.Command, args []string) {
        logger := log.With().Logger()
        logger.Info().Msg("开始生成")
        _, privateKey, err := ed25519.GenerateKey(rand.Reader)
        if err != nil {
            logger.Err(err).Send()
            return
        }
        logger.Info().Str("Key", hex.EncodeToString(privateKey)).Msg("请将key复制到配置文件中")
    },
    Args: cobra.NoArgs,
}
