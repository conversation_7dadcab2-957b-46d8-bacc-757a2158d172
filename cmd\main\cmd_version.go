package main

import (
    "log"
    "os"
    "omnix/state"

    "github.com/spf13/cobra"
)

var commandVersion = &cobra.Command{
    Use:     "version",
    Short:   "版本",
    Aliases: []string{"v"},
    Run: func(cmd *cobra.Command, args []string) {
        log.New(os.Stdout,"",0).Println("Version:",state.Version)
    },
    Args: cobra.NoArgs,
}

func init() {
    mainCommand.AddCommand(commandVersion)
}
