package main

import (
    "omnix/boot"
    "omnix/state"

    "github.com/rs/zerolog/log"
    "github.com/spf13/cobra"
)

var mainCommand = &cobra.Command{
    Use: "vime",
    PersistentPreRun: func(cmd *cobra.Command, args []string) {
        boot.LoadOptions()
    },
}

func main() {
    
    mainCommand.PersistentFlags().
        Int8VarP(&state.LogLevel, "verbose", "v", 3, "ZeroLog logging level,default only error")
    mainCommand.PersistentFlags().
        StringVarP(&state.EnvFile, "env", "e", "local.toml", "Environment variable file")
    if err := mainCommand.Execute(); err != nil {
        log.Err(err).Send()
    }
}
