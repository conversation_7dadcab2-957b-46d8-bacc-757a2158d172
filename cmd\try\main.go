package main

import (
    "context"
    "log"
    "omnix/boot"
    "omnix/genpb/flowpb"
    "omnix/provider/flow"
    "omnix/toolkit/wrapper/kitcomfyui"
)

var (
    c = context.Background()
)

func main() {
    boot.LoadOptions()
    fc := flow.Get().R()
    combine, err := flowpb.NewPhotoshopFlowClient(fc).PsCombine(c, &flowpb.PsCombineRequest{AdapterHost: "abc", Id: "abd2"})
    if err != nil {
        log.Fatal(err)
    }
    log.Println(combine)
    return
    client := kitcomfyui.New("http://127.0.0.1:28880")
    path, err := client.PSUploadToUploadFromPath("F:\\ps\\2.jpg")
    log.Println(path, err)
    background, err := client.PSCombine("1.psd", path.Filename)
    log.Println(background, err)

}
