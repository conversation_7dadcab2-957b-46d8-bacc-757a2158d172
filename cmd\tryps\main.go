package main

import (
    "log"
    "os/exec"
)

func main() {

    //output, err := exec.Command("C:\\Program Files\\Adobe\\Adobe Photoshop 2025\\Photoshop.exe", "-s", "F:\\ps\\a.jsx").CombinedOutput()
    // 移除白色背景 20250909
    output, err := exec.Command("C:\\Program Files\\Adobe\\Adobe Photoshop 2025\\Photoshop.exe", "-s", "F:\\ps\\rmwhitebg.jsx").CombinedOutput()
    //output, err := exec.Command("C:\\Program Files\\Adobe\\Adobe Photoshop 2025\\Photoshop.exe", "-s", "F:\\ps\\b.jsx").CombinedOutput()
    //output, err := exec.Command("cmd.exe", "/c", "C:\\Program Files\\Adobe\\Adobe Photoshop 2025\\Photoshop.exe", "-s", "F:\\ps\\ok.jsx").CombinedOutput()
    log.Println(err, string(output))
}
