psql:
  dsn: "***************************************************/vime?search_path=public&sslmode=disable"
  queries:
    - ddl/db1_rawquery
plugins_preset: "all" # Valid values are "default", "all" or "none".
plugins:
  dbinfo:
    disabled: true
    pkgname: "db1info"
    destination: "ddl/db1/info"
  enums:
    disabled: true
    pkgname: "db1"
    destination: "ddl/db1"
  models:
    disabled: true
    pkgname: "db1"
    destination: "ddl/db1"
  factory:
    disabled: true
    pkgname: "factory"
    destination: "ddl/db1/factory"
  dberrors:
    disabled: true
    pkgname: "dberrors"
    destination: "ddl/db1/dberrors"
  where:
    disabled: true
  loaders:
    disabled: true
  joins:
    disabled: true