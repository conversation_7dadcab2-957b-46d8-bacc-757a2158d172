// Code generated by ent, DO NOT EDIT.

package db1

import (
	"fmt"
	"omnix/ddl/db1/administrator"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Administrator is the model entity for the Administrator schema.
type Administrator struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedTime time.Time `json:"created_time,omitempty"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty"`
	// 用户名
	Username string `json:"username,omitempty"`
	// 密码
	Password string `json:"password,omitempty"`
	// 状态
	State        string `json:"state,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Administrator) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case administrator.FieldID:
			values[i] = new(sql.NullInt64)
		case administrator.FieldUsername, administrator.FieldPassword, administrator.FieldState:
			values[i] = new(sql.NullString)
		case administrator.FieldCreatedTime, administrator.FieldUpdatedTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Administrator fields.
func (_m *Administrator) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case administrator.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case administrator.FieldCreatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_time", values[i])
			} else if value.Valid {
				_m.CreatedTime = value.Time
			}
		case administrator.FieldUpdatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_time", values[i])
			} else if value.Valid {
				_m.UpdatedTime = value.Time
			}
		case administrator.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				_m.Username = value.String
			}
		case administrator.FieldPassword:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password", values[i])
			} else if value.Valid {
				_m.Password = value.String
			}
		case administrator.FieldState:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field state", values[i])
			} else if value.Valid {
				_m.State = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Administrator.
// This includes values selected through modifiers, order, etc.
func (_m *Administrator) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Administrator.
// Note that you need to call Administrator.Unwrap() before calling this method if this Administrator
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Administrator) Update() *AdministratorUpdateOne {
	return NewAdministratorClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Administrator entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Administrator) Unwrap() *Administrator {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("db1: Administrator is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Administrator) String() string {
	var builder strings.Builder
	builder.WriteString("Administrator(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("created_time=")
	builder.WriteString(_m.CreatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_time=")
	builder.WriteString(_m.UpdatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("username=")
	builder.WriteString(_m.Username)
	builder.WriteString(", ")
	builder.WriteString("password=")
	builder.WriteString(_m.Password)
	builder.WriteString(", ")
	builder.WriteString("state=")
	builder.WriteString(_m.State)
	builder.WriteByte(')')
	return builder.String()
}

// Administrators is a parsable slice of Administrator.
type Administrators []*Administrator
