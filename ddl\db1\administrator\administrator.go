// Code generated by ent, DO NOT EDIT.

package administrator

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the administrator type in the database.
	Label = "administrator"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedTime holds the string denoting the created_time field in the database.
	FieldCreatedTime = "created_time"
	// FieldUpdatedTime holds the string denoting the updated_time field in the database.
	FieldUpdatedTime = "updated_time"
	// FieldUsername holds the string denoting the username field in the database.
	FieldUsername = "username"
	// FieldPassword holds the string denoting the password field in the database.
	FieldPassword = "password"
	// FieldState holds the string denoting the state field in the database.
	FieldState = "state"
	// Table holds the table name of the administrator in the database.
	Table = "administrators"
)

// Columns holds all SQL columns for administrator fields.
var Columns = []string{
	FieldID,
	FieldCreatedTime,
	FieldUpdatedTime,
	FieldUsername,
	FieldPassword,
	FieldState,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedTime holds the default value on creation for the "created_time" field.
	DefaultCreatedTime func() time.Time
	// DefaultUpdatedTime holds the default value on creation for the "updated_time" field.
	DefaultUpdatedTime func() time.Time
	// UpdateDefaultUpdatedTime holds the default value on update for the "updated_time" field.
	UpdateDefaultUpdatedTime func() time.Time
	// UsernameValidator is a validator for the "username" field. It is called by the builders before save.
	UsernameValidator func(string) error
	// PasswordValidator is a validator for the "password" field. It is called by the builders before save.
	PasswordValidator func(string) error
	// StateValidator is a validator for the "state" field. It is called by the builders before save.
	StateValidator func(string) error
)

// OrderOption defines the ordering options for the Administrator queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedTime orders the results by the created_time field.
func ByCreatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedTime, opts...).ToFunc()
}

// ByUpdatedTime orders the results by the updated_time field.
func ByUpdatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedTime, opts...).ToFunc()
}

// ByUsername orders the results by the username field.
func ByUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsername, opts...).ToFunc()
}

// ByPassword orders the results by the password field.
func ByPassword(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPassword, opts...).ToFunc()
}

// ByState orders the results by the state field.
func ByState(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldState, opts...).ToFunc()
}
