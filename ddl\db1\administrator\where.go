// Code generated by ent, DO NOT EDIT.

package administrator

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldID, id))
}

// ID<PERSON><PERSON> applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.Administrator {
	return predicate.Administrator(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldUpdatedTime, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldUsername, v))
}

// Password applies equality check predicate on the "password" field. It's identical to PasswordEQ.
func Password(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldPassword, v))
}

// State applies equality check predicate on the "state" field. It's identical to StateEQ.
func State(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldState, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.Administrator {
	return predicate.Administrator(sql.FieldLTE(FieldUpdatedTime, v))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldUsername, v))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldNEQ(FieldUsername, v))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.Administrator {
	return predicate.Administrator(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.Administrator {
	return predicate.Administrator(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldContainsFold(FieldUsername, v))
}

// PasswordEQ applies the EQ predicate on the "password" field.
func PasswordEQ(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldPassword, v))
}

// PasswordNEQ applies the NEQ predicate on the "password" field.
func PasswordNEQ(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldNEQ(FieldPassword, v))
}

// PasswordIn applies the In predicate on the "password" field.
func PasswordIn(vs ...string) predicate.Administrator {
	return predicate.Administrator(sql.FieldIn(FieldPassword, vs...))
}

// PasswordNotIn applies the NotIn predicate on the "password" field.
func PasswordNotIn(vs ...string) predicate.Administrator {
	return predicate.Administrator(sql.FieldNotIn(FieldPassword, vs...))
}

// PasswordGT applies the GT predicate on the "password" field.
func PasswordGT(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldGT(FieldPassword, v))
}

// PasswordGTE applies the GTE predicate on the "password" field.
func PasswordGTE(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldGTE(FieldPassword, v))
}

// PasswordLT applies the LT predicate on the "password" field.
func PasswordLT(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldLT(FieldPassword, v))
}

// PasswordLTE applies the LTE predicate on the "password" field.
func PasswordLTE(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldLTE(FieldPassword, v))
}

// PasswordContains applies the Contains predicate on the "password" field.
func PasswordContains(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldContains(FieldPassword, v))
}

// PasswordHasPrefix applies the HasPrefix predicate on the "password" field.
func PasswordHasPrefix(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldHasPrefix(FieldPassword, v))
}

// PasswordHasSuffix applies the HasSuffix predicate on the "password" field.
func PasswordHasSuffix(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldHasSuffix(FieldPassword, v))
}

// PasswordEqualFold applies the EqualFold predicate on the "password" field.
func PasswordEqualFold(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEqualFold(FieldPassword, v))
}

// PasswordContainsFold applies the ContainsFold predicate on the "password" field.
func PasswordContainsFold(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldContainsFold(FieldPassword, v))
}

// StateEQ applies the EQ predicate on the "state" field.
func StateEQ(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEQ(FieldState, v))
}

// StateNEQ applies the NEQ predicate on the "state" field.
func StateNEQ(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldNEQ(FieldState, v))
}

// StateIn applies the In predicate on the "state" field.
func StateIn(vs ...string) predicate.Administrator {
	return predicate.Administrator(sql.FieldIn(FieldState, vs...))
}

// StateNotIn applies the NotIn predicate on the "state" field.
func StateNotIn(vs ...string) predicate.Administrator {
	return predicate.Administrator(sql.FieldNotIn(FieldState, vs...))
}

// StateGT applies the GT predicate on the "state" field.
func StateGT(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldGT(FieldState, v))
}

// StateGTE applies the GTE predicate on the "state" field.
func StateGTE(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldGTE(FieldState, v))
}

// StateLT applies the LT predicate on the "state" field.
func StateLT(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldLT(FieldState, v))
}

// StateLTE applies the LTE predicate on the "state" field.
func StateLTE(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldLTE(FieldState, v))
}

// StateContains applies the Contains predicate on the "state" field.
func StateContains(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldContains(FieldState, v))
}

// StateHasPrefix applies the HasPrefix predicate on the "state" field.
func StateHasPrefix(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldHasPrefix(FieldState, v))
}

// StateHasSuffix applies the HasSuffix predicate on the "state" field.
func StateHasSuffix(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldHasSuffix(FieldState, v))
}

// StateEqualFold applies the EqualFold predicate on the "state" field.
func StateEqualFold(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldEqualFold(FieldState, v))
}

// StateContainsFold applies the ContainsFold predicate on the "state" field.
func StateContainsFold(v string) predicate.Administrator {
	return predicate.Administrator(sql.FieldContainsFold(FieldState, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Administrator) predicate.Administrator {
	return predicate.Administrator(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Administrator) predicate.Administrator {
	return predicate.Administrator(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Administrator) predicate.Administrator {
	return predicate.Administrator(sql.NotPredicates(p))
}
