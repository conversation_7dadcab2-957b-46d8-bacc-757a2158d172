// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/administrator"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// Administrator<PERSON><PERSON> is the builder for creating a Administrator entity.
type AdministratorCreate struct {
	config
	mutation *AdministratorMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *AdministratorCreate) SetCreatedTime(v time.Time) *AdministratorCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *AdministratorCreate) SetNillableCreatedTime(v *time.Time) *AdministratorCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *AdministratorCreate) SetUpdatedTime(v time.Time) *AdministratorCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *AdministratorCreate) SetNillableUpdatedTime(v *time.Time) *AdministratorCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetUsername sets the "username" field.
func (_c *AdministratorCreate) SetUsername(v string) *AdministratorCreate {
	_c.mutation.SetUsername(v)
	return _c
}

// SetPassword sets the "password" field.
func (_c *AdministratorCreate) SetPassword(v string) *AdministratorCreate {
	_c.mutation.SetPassword(v)
	return _c
}

// SetState sets the "state" field.
func (_c *AdministratorCreate) SetState(v string) *AdministratorCreate {
	_c.mutation.SetState(v)
	return _c
}

// SetID sets the "id" field.
func (_c *AdministratorCreate) SetID(v int64) *AdministratorCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the AdministratorMutation object of the builder.
func (_c *AdministratorCreate) Mutation() *AdministratorMutation {
	return _c.mutation
}

// Save creates the Administrator in the database.
func (_c *AdministratorCreate) Save(ctx context.Context) (*Administrator, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *AdministratorCreate) SaveX(ctx context.Context) *Administrator {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AdministratorCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AdministratorCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *AdministratorCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := administrator.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := administrator.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *AdministratorCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "Administrator.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "Administrator.updated_time"`)}
	}
	if _, ok := _c.mutation.Username(); !ok {
		return &ValidationError{Name: "username", err: errors.New(`db1: missing required field "Administrator.username"`)}
	}
	if v, ok := _c.mutation.Username(); ok {
		if err := administrator.UsernameValidator(v); err != nil {
			return &ValidationError{Name: "username", err: fmt.Errorf(`db1: validator failed for field "Administrator.username": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Password(); !ok {
		return &ValidationError{Name: "password", err: errors.New(`db1: missing required field "Administrator.password"`)}
	}
	if v, ok := _c.mutation.Password(); ok {
		if err := administrator.PasswordValidator(v); err != nil {
			return &ValidationError{Name: "password", err: fmt.Errorf(`db1: validator failed for field "Administrator.password": %w`, err)}
		}
	}
	if _, ok := _c.mutation.State(); !ok {
		return &ValidationError{Name: "state", err: errors.New(`db1: missing required field "Administrator.state"`)}
	}
	if v, ok := _c.mutation.State(); ok {
		if err := administrator.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`db1: validator failed for field "Administrator.state": %w`, err)}
		}
	}
	return nil
}

func (_c *AdministratorCreate) sqlSave(ctx context.Context) (*Administrator, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *AdministratorCreate) createSpec() (*Administrator, *sqlgraph.CreateSpec) {
	var (
		_node = &Administrator{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(administrator.Table, sqlgraph.NewFieldSpec(administrator.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(administrator.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(administrator.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.Username(); ok {
		_spec.SetField(administrator.FieldUsername, field.TypeString, value)
		_node.Username = value
	}
	if value, ok := _c.mutation.Password(); ok {
		_spec.SetField(administrator.FieldPassword, field.TypeString, value)
		_node.Password = value
	}
	if value, ok := _c.mutation.State(); ok {
		_spec.SetField(administrator.FieldState, field.TypeString, value)
		_node.State = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Administrator.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AdministratorUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *AdministratorCreate) OnConflict(opts ...sql.ConflictOption) *AdministratorUpsertOne {
	_c.conflict = opts
	return &AdministratorUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Administrator.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *AdministratorCreate) OnConflictColumns(columns ...string) *AdministratorUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &AdministratorUpsertOne{
		create: _c,
	}
}

type (
	// AdministratorUpsertOne is the builder for "upsert"-ing
	//  one Administrator node.
	AdministratorUpsertOne struct {
		create *AdministratorCreate
	}

	// AdministratorUpsert is the "OnConflict" setter.
	AdministratorUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *AdministratorUpsert) SetUpdatedTime(v time.Time) *AdministratorUpsert {
	u.Set(administrator.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *AdministratorUpsert) UpdateUpdatedTime() *AdministratorUpsert {
	u.SetExcluded(administrator.FieldUpdatedTime)
	return u
}

// SetUsername sets the "username" field.
func (u *AdministratorUpsert) SetUsername(v string) *AdministratorUpsert {
	u.Set(administrator.FieldUsername, v)
	return u
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *AdministratorUpsert) UpdateUsername() *AdministratorUpsert {
	u.SetExcluded(administrator.FieldUsername)
	return u
}

// SetPassword sets the "password" field.
func (u *AdministratorUpsert) SetPassword(v string) *AdministratorUpsert {
	u.Set(administrator.FieldPassword, v)
	return u
}

// UpdatePassword sets the "password" field to the value that was provided on create.
func (u *AdministratorUpsert) UpdatePassword() *AdministratorUpsert {
	u.SetExcluded(administrator.FieldPassword)
	return u
}

// SetState sets the "state" field.
func (u *AdministratorUpsert) SetState(v string) *AdministratorUpsert {
	u.Set(administrator.FieldState, v)
	return u
}

// UpdateState sets the "state" field to the value that was provided on create.
func (u *AdministratorUpsert) UpdateState() *AdministratorUpsert {
	u.SetExcluded(administrator.FieldState)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Administrator.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(administrator.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AdministratorUpsertOne) UpdateNewValues() *AdministratorUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(administrator.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(administrator.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Administrator.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AdministratorUpsertOne) Ignore() *AdministratorUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AdministratorUpsertOne) DoNothing() *AdministratorUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AdministratorCreate.OnConflict
// documentation for more info.
func (u *AdministratorUpsertOne) Update(set func(*AdministratorUpsert)) *AdministratorUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AdministratorUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *AdministratorUpsertOne) SetUpdatedTime(v time.Time) *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *AdministratorUpsertOne) UpdateUpdatedTime() *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetUsername sets the "username" field.
func (u *AdministratorUpsertOne) SetUsername(v string) *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetUsername(v)
	})
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *AdministratorUpsertOne) UpdateUsername() *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdateUsername()
	})
}

// SetPassword sets the "password" field.
func (u *AdministratorUpsertOne) SetPassword(v string) *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetPassword(v)
	})
}

// UpdatePassword sets the "password" field to the value that was provided on create.
func (u *AdministratorUpsertOne) UpdatePassword() *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdatePassword()
	})
}

// SetState sets the "state" field.
func (u *AdministratorUpsertOne) SetState(v string) *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetState(v)
	})
}

// UpdateState sets the "state" field to the value that was provided on create.
func (u *AdministratorUpsertOne) UpdateState() *AdministratorUpsertOne {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdateState()
	})
}

// Exec executes the query.
func (u *AdministratorUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for AdministratorCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AdministratorUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AdministratorUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AdministratorUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AdministratorCreateBulk is the builder for creating many Administrator entities in bulk.
type AdministratorCreateBulk struct {
	config
	err      error
	builders []*AdministratorCreate
	conflict []sql.ConflictOption
}

// Save creates the Administrator entities in the database.
func (_c *AdministratorCreateBulk) Save(ctx context.Context) ([]*Administrator, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Administrator, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AdministratorMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *AdministratorCreateBulk) SaveX(ctx context.Context) []*Administrator {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AdministratorCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AdministratorCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Administrator.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AdministratorUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *AdministratorCreateBulk) OnConflict(opts ...sql.ConflictOption) *AdministratorUpsertBulk {
	_c.conflict = opts
	return &AdministratorUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Administrator.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *AdministratorCreateBulk) OnConflictColumns(columns ...string) *AdministratorUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &AdministratorUpsertBulk{
		create: _c,
	}
}

// AdministratorUpsertBulk is the builder for "upsert"-ing
// a bulk of Administrator nodes.
type AdministratorUpsertBulk struct {
	create *AdministratorCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Administrator.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(administrator.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AdministratorUpsertBulk) UpdateNewValues() *AdministratorUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(administrator.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(administrator.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Administrator.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AdministratorUpsertBulk) Ignore() *AdministratorUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AdministratorUpsertBulk) DoNothing() *AdministratorUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AdministratorCreateBulk.OnConflict
// documentation for more info.
func (u *AdministratorUpsertBulk) Update(set func(*AdministratorUpsert)) *AdministratorUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AdministratorUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *AdministratorUpsertBulk) SetUpdatedTime(v time.Time) *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *AdministratorUpsertBulk) UpdateUpdatedTime() *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetUsername sets the "username" field.
func (u *AdministratorUpsertBulk) SetUsername(v string) *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetUsername(v)
	})
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *AdministratorUpsertBulk) UpdateUsername() *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdateUsername()
	})
}

// SetPassword sets the "password" field.
func (u *AdministratorUpsertBulk) SetPassword(v string) *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetPassword(v)
	})
}

// UpdatePassword sets the "password" field to the value that was provided on create.
func (u *AdministratorUpsertBulk) UpdatePassword() *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdatePassword()
	})
}

// SetState sets the "state" field.
func (u *AdministratorUpsertBulk) SetState(v string) *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.SetState(v)
	})
}

// UpdateState sets the "state" field to the value that was provided on create.
func (u *AdministratorUpsertBulk) UpdateState() *AdministratorUpsertBulk {
	return u.Update(func(s *AdministratorUpsert) {
		s.UpdateState()
	})
}

// Exec executes the query.
func (u *AdministratorUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the AdministratorCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for AdministratorCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AdministratorUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
