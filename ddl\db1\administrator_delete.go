// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/administrator"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdministratorDelete is the builder for deleting a Administrator entity.
type AdministratorDelete struct {
	config
	hooks    []Hook
	mutation *AdministratorMutation
}

// Where appends a list predicates to the AdministratorDelete builder.
func (_d *AdministratorDelete) Where(ps ...predicate.Administrator) *AdministratorDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *AdministratorDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *AdministratorDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *AdministratorDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(administrator.Table, sqlgraph.NewFieldSpec(administrator.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the AdministratorDelete builder.
func (_d *AdministratorDelete) WhereCreatedTime(v time.Time) *AdministratorDelete {
	_d.Where(administrator.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the AdministratorDelete builder.
func (_d *AdministratorDelete) WhereUpdatedTime(v time.Time) *AdministratorDelete {
	_d.Where(administrator.UpdatedTime(v))
	return _d
}

// WhereUsername applies equality check predicate to the AdministratorDelete builder.
func (_d *AdministratorDelete) WhereUsername(v string) *AdministratorDelete {
	_d.Where(administrator.Username(v))
	return _d
}

// WherePassword applies equality check predicate to the AdministratorDelete builder.
func (_d *AdministratorDelete) WherePassword(v string) *AdministratorDelete {
	_d.Where(administrator.Password(v))
	return _d
}

// WhereState applies equality check predicate to the AdministratorDelete builder.
func (_d *AdministratorDelete) WhereState(v string) *AdministratorDelete {
	_d.Where(administrator.State(v))
	return _d
}

// AdministratorDeleteOne is the builder for deleting a single Administrator entity.
type AdministratorDeleteOne struct {
	_d *AdministratorDelete
}

// Where appends a list predicates to the AdministratorDelete builder.
func (_d *AdministratorDeleteOne) Where(ps ...predicate.Administrator) *AdministratorDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *AdministratorDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{administrator.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *AdministratorDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
