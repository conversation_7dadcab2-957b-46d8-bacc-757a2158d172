// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/administrator"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdministratorUpdate is the builder for updating Administrator entities.
type AdministratorUpdate struct {
	config
	hooks    []Hook
	mutation *AdministratorMutation
}

// Where appends a list predicates to the AdministratorUpdate builder.
func (_u *AdministratorUpdate) Where(ps ...predicate.Administrator) *AdministratorUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *AdministratorUpdate) SetUpdatedTime(v time.Time) *AdministratorUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetUsername sets the "username" field.
func (_u *AdministratorUpdate) SetUsername(v string) *AdministratorUpdate {
	_u.mutation.SetUsername(v)
	return _u
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (_u *AdministratorUpdate) SetNillableUsername(v *string) *AdministratorUpdate {
	if v != nil {
		_u.SetUsername(*v)
	}
	return _u
}

// SetPassword sets the "password" field.
func (_u *AdministratorUpdate) SetPassword(v string) *AdministratorUpdate {
	_u.mutation.SetPassword(v)
	return _u
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (_u *AdministratorUpdate) SetNillablePassword(v *string) *AdministratorUpdate {
	if v != nil {
		_u.SetPassword(*v)
	}
	return _u
}

// SetState sets the "state" field.
func (_u *AdministratorUpdate) SetState(v string) *AdministratorUpdate {
	_u.mutation.SetState(v)
	return _u
}

// SetNillableState sets the "state" field if the given value is not nil.
func (_u *AdministratorUpdate) SetNillableState(v *string) *AdministratorUpdate {
	if v != nil {
		_u.SetState(*v)
	}
	return _u
}

// Mutation returns the AdministratorMutation object of the builder.
func (_u *AdministratorUpdate) Mutation() *AdministratorMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *AdministratorUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AdministratorUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *AdministratorUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AdministratorUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AdministratorUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := administrator.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *AdministratorUpdate) check() error {
	if v, ok := _u.mutation.Username(); ok {
		if err := administrator.UsernameValidator(v); err != nil {
			return &ValidationError{Name: "username", err: fmt.Errorf(`db1: validator failed for field "Administrator.username": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Password(); ok {
		if err := administrator.PasswordValidator(v); err != nil {
			return &ValidationError{Name: "password", err: fmt.Errorf(`db1: validator failed for field "Administrator.password": %w`, err)}
		}
	}
	if v, ok := _u.mutation.State(); ok {
		if err := administrator.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`db1: validator failed for field "Administrator.state": %w`, err)}
		}
	}
	return nil
}

func (_u *AdministratorUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(administrator.Table, administrator.Columns, sqlgraph.NewFieldSpec(administrator.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(administrator.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Username(); ok {
		_spec.SetField(administrator.FieldUsername, field.TypeString, value)
	}
	if value, ok := _u.mutation.Password(); ok {
		_spec.SetField(administrator.FieldPassword, field.TypeString, value)
	}
	if value, ok := _u.mutation.State(); ok {
		_spec.SetField(administrator.FieldState, field.TypeString, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{administrator.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// AdministratorUpdateOne is the builder for updating a single Administrator entity.
type AdministratorUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AdministratorMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *AdministratorUpdateOne) SetUpdatedTime(v time.Time) *AdministratorUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetUsername sets the "username" field.
func (_u *AdministratorUpdateOne) SetUsername(v string) *AdministratorUpdateOne {
	_u.mutation.SetUsername(v)
	return _u
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (_u *AdministratorUpdateOne) SetNillableUsername(v *string) *AdministratorUpdateOne {
	if v != nil {
		_u.SetUsername(*v)
	}
	return _u
}

// SetPassword sets the "password" field.
func (_u *AdministratorUpdateOne) SetPassword(v string) *AdministratorUpdateOne {
	_u.mutation.SetPassword(v)
	return _u
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (_u *AdministratorUpdateOne) SetNillablePassword(v *string) *AdministratorUpdateOne {
	if v != nil {
		_u.SetPassword(*v)
	}
	return _u
}

// SetState sets the "state" field.
func (_u *AdministratorUpdateOne) SetState(v string) *AdministratorUpdateOne {
	_u.mutation.SetState(v)
	return _u
}

// SetNillableState sets the "state" field if the given value is not nil.
func (_u *AdministratorUpdateOne) SetNillableState(v *string) *AdministratorUpdateOne {
	if v != nil {
		_u.SetState(*v)
	}
	return _u
}

// Mutation returns the AdministratorMutation object of the builder.
func (_u *AdministratorUpdateOne) Mutation() *AdministratorMutation {
	return _u.mutation
}

// Where appends a list predicates to the AdministratorUpdate builder.
func (_u *AdministratorUpdateOne) Where(ps ...predicate.Administrator) *AdministratorUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *AdministratorUpdateOne) Select(field string, fields ...string) *AdministratorUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Administrator entity.
func (_u *AdministratorUpdateOne) Save(ctx context.Context) (*Administrator, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AdministratorUpdateOne) SaveX(ctx context.Context) *Administrator {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *AdministratorUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AdministratorUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AdministratorUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := administrator.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *AdministratorUpdateOne) check() error {
	if v, ok := _u.mutation.Username(); ok {
		if err := administrator.UsernameValidator(v); err != nil {
			return &ValidationError{Name: "username", err: fmt.Errorf(`db1: validator failed for field "Administrator.username": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Password(); ok {
		if err := administrator.PasswordValidator(v); err != nil {
			return &ValidationError{Name: "password", err: fmt.Errorf(`db1: validator failed for field "Administrator.password": %w`, err)}
		}
	}
	if v, ok := _u.mutation.State(); ok {
		if err := administrator.StateValidator(v); err != nil {
			return &ValidationError{Name: "state", err: fmt.Errorf(`db1: validator failed for field "Administrator.state": %w`, err)}
		}
	}
	return nil
}

func (_u *AdministratorUpdateOne) sqlSave(ctx context.Context) (_node *Administrator, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(administrator.Table, administrator.Columns, sqlgraph.NewFieldSpec(administrator.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "Administrator.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, administrator.FieldID)
		for _, f := range fields {
			if !administrator.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != administrator.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(administrator.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Username(); ok {
		_spec.SetField(administrator.FieldUsername, field.TypeString, value)
	}
	if value, ok := _u.mutation.Password(); ok {
		_spec.SetField(administrator.FieldPassword, field.TypeString, value)
	}
	if value, ok := _u.mutation.State(); ok {
		_spec.SetField(administrator.FieldState, field.TypeString, value)
	}
	_node = &Administrator{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{administrator.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the AdministratorUpdate builder.
func (_u *AdministratorUpdate) WhereCreatedTime(v time.Time) *AdministratorUpdate {
	_u.Where(administrator.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the AdministratorUpdate builder.
func (_u *AdministratorUpdate) WhereUpdatedTime(v time.Time) *AdministratorUpdate {
	_u.Where(administrator.UpdatedTime(v))
	return _u
}

// WhereUsername applies equality check predicate to the AdministratorUpdate builder.
func (_u *AdministratorUpdate) WhereUsername(v string) *AdministratorUpdate {
	_u.Where(administrator.Username(v))
	return _u
}

// WherePassword applies equality check predicate to the AdministratorUpdate builder.
func (_u *AdministratorUpdate) WherePassword(v string) *AdministratorUpdate {
	_u.Where(administrator.Password(v))
	return _u
}

// WhereState applies equality check predicate to the AdministratorUpdate builder.
func (_u *AdministratorUpdate) WhereState(v string) *AdministratorUpdate {
	_u.Where(administrator.State(v))
	return _u
}
