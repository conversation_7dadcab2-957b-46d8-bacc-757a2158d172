// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"omnix/ddl/db1/migrate"

	"omnix/ddl/db1/administrator"
	"omnix/ddl/db1/material"
	"omnix/ddl/db1/materialgroup"
	"omnix/ddl/db1/productitem"
	"omnix/ddl/db1/psd"
	"omnix/ddl/db1/psdcombineresult"
	"omnix/ddl/db1/psdcombinetask"
	"omnix/ddl/db1/psdgroup"
	"omnix/ddl/db1/registeredtrademark"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"

	stdsql "database/sql"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// Administrator is the client for interacting with the Administrator builders.
	Administrator *AdministratorClient
	// Material is the client for interacting with the Material builders.
	Material *MaterialClient
	// MaterialGroup is the client for interacting with the MaterialGroup builders.
	MaterialGroup *MaterialGroupClient
	// ProductItem is the client for interacting with the ProductItem builders.
	ProductItem *ProductItemClient
	// Psd is the client for interacting with the Psd builders.
	Psd *PsdClient
	// PsdCombineResult is the client for interacting with the PsdCombineResult builders.
	PsdCombineResult *PsdCombineResultClient
	// PsdCombineTask is the client for interacting with the PsdCombineTask builders.
	PsdCombineTask *PsdCombineTaskClient
	// PsdGroup is the client for interacting with the PsdGroup builders.
	PsdGroup *PsdGroupClient
	// RegisteredTrademark is the client for interacting with the RegisteredTrademark builders.
	RegisteredTrademark *RegisteredTrademarkClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.Administrator = NewAdministratorClient(c.config)
	c.Material = NewMaterialClient(c.config)
	c.MaterialGroup = NewMaterialGroupClient(c.config)
	c.ProductItem = NewProductItemClient(c.config)
	c.Psd = NewPsdClient(c.config)
	c.PsdCombineResult = NewPsdCombineResultClient(c.config)
	c.PsdCombineTask = NewPsdCombineTaskClient(c.config)
	c.PsdGroup = NewPsdGroupClient(c.config)
	c.RegisteredTrademark = NewRegisteredTrademarkClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("db1: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("db1: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		Administrator:       NewAdministratorClient(cfg),
		Material:            NewMaterialClient(cfg),
		MaterialGroup:       NewMaterialGroupClient(cfg),
		ProductItem:         NewProductItemClient(cfg),
		Psd:                 NewPsdClient(cfg),
		PsdCombineResult:    NewPsdCombineResultClient(cfg),
		PsdCombineTask:      NewPsdCombineTaskClient(cfg),
		PsdGroup:            NewPsdGroupClient(cfg),
		RegisteredTrademark: NewRegisteredTrademarkClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		Administrator:       NewAdministratorClient(cfg),
		Material:            NewMaterialClient(cfg),
		MaterialGroup:       NewMaterialGroupClient(cfg),
		ProductItem:         NewProductItemClient(cfg),
		Psd:                 NewPsdClient(cfg),
		PsdCombineResult:    NewPsdCombineResultClient(cfg),
		PsdCombineTask:      NewPsdCombineTaskClient(cfg),
		PsdGroup:            NewPsdGroupClient(cfg),
		RegisteredTrademark: NewRegisteredTrademarkClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		Administrator.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.Administrator, c.Material, c.MaterialGroup, c.ProductItem, c.Psd,
		c.PsdCombineResult, c.PsdCombineTask, c.PsdGroup, c.RegisteredTrademark,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.Administrator, c.Material, c.MaterialGroup, c.ProductItem, c.Psd,
		c.PsdCombineResult, c.PsdCombineTask, c.PsdGroup, c.RegisteredTrademark,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AdministratorMutation:
		return c.Administrator.mutate(ctx, m)
	case *MaterialMutation:
		return c.Material.mutate(ctx, m)
	case *MaterialGroupMutation:
		return c.MaterialGroup.mutate(ctx, m)
	case *ProductItemMutation:
		return c.ProductItem.mutate(ctx, m)
	case *PsdMutation:
		return c.Psd.mutate(ctx, m)
	case *PsdCombineResultMutation:
		return c.PsdCombineResult.mutate(ctx, m)
	case *PsdCombineTaskMutation:
		return c.PsdCombineTask.mutate(ctx, m)
	case *PsdGroupMutation:
		return c.PsdGroup.mutate(ctx, m)
	case *RegisteredTrademarkMutation:
		return c.RegisteredTrademark.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("db1: unknown mutation type %T", m)
	}
}

// AdministratorClient is a client for the Administrator schema.
type AdministratorClient struct {
	config
}

// NewAdministratorClient returns a client for the Administrator from the given config.
func NewAdministratorClient(c config) *AdministratorClient {
	return &AdministratorClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `administrator.Hooks(f(g(h())))`.
func (c *AdministratorClient) Use(hooks ...Hook) {
	c.hooks.Administrator = append(c.hooks.Administrator, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `administrator.Intercept(f(g(h())))`.
func (c *AdministratorClient) Intercept(interceptors ...Interceptor) {
	c.inters.Administrator = append(c.inters.Administrator, interceptors...)
}

// Create returns a builder for creating a Administrator entity.
func (c *AdministratorClient) Create() *AdministratorCreate {
	mutation := newAdministratorMutation(c.config, OpCreate)
	return &AdministratorCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Administrator entities.
func (c *AdministratorClient) CreateBulk(builders ...*AdministratorCreate) *AdministratorCreateBulk {
	return &AdministratorCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AdministratorClient) MapCreateBulk(slice any, setFunc func(*AdministratorCreate, int)) *AdministratorCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AdministratorCreateBulk{err: fmt.Errorf("calling to AdministratorClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AdministratorCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AdministratorCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Administrator.
func (c *AdministratorClient) Update() *AdministratorUpdate {
	mutation := newAdministratorMutation(c.config, OpUpdate)
	return &AdministratorUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AdministratorClient) UpdateOne(_m *Administrator) *AdministratorUpdateOne {
	mutation := newAdministratorMutation(c.config, OpUpdateOne, withAdministrator(_m))
	return &AdministratorUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AdministratorClient) UpdateOneID(id int64) *AdministratorUpdateOne {
	mutation := newAdministratorMutation(c.config, OpUpdateOne, withAdministratorID(id))
	return &AdministratorUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Administrator.
func (c *AdministratorClient) Delete() *AdministratorDelete {
	mutation := newAdministratorMutation(c.config, OpDelete)
	return &AdministratorDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AdministratorClient) DeleteOne(_m *Administrator) *AdministratorDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AdministratorClient) DeleteOneID(id int64) *AdministratorDeleteOne {
	builder := c.Delete().Where(administrator.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AdministratorDeleteOne{builder}
}

// Query returns a query builder for Administrator.
func (c *AdministratorClient) Query() *AdministratorQuery {
	return &AdministratorQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAdministrator},
		inters: c.Interceptors(),
	}
}

// Get returns a Administrator entity by its id.
func (c *AdministratorClient) Get(ctx context.Context, id int64) (*Administrator, error) {
	return c.Query().Where(administrator.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AdministratorClient) GetX(ctx context.Context, id int64) *Administrator {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AdministratorClient) Hooks() []Hook {
	return c.hooks.Administrator
}

// Interceptors returns the client interceptors.
func (c *AdministratorClient) Interceptors() []Interceptor {
	return c.inters.Administrator
}

func (c *AdministratorClient) mutate(ctx context.Context, m *AdministratorMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AdministratorCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AdministratorUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AdministratorUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AdministratorDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown Administrator mutation op: %q", m.Op())
	}
}

// MaterialClient is a client for the Material schema.
type MaterialClient struct {
	config
}

// NewMaterialClient returns a client for the Material from the given config.
func NewMaterialClient(c config) *MaterialClient {
	return &MaterialClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `material.Hooks(f(g(h())))`.
func (c *MaterialClient) Use(hooks ...Hook) {
	c.hooks.Material = append(c.hooks.Material, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `material.Intercept(f(g(h())))`.
func (c *MaterialClient) Intercept(interceptors ...Interceptor) {
	c.inters.Material = append(c.inters.Material, interceptors...)
}

// Create returns a builder for creating a Material entity.
func (c *MaterialClient) Create() *MaterialCreate {
	mutation := newMaterialMutation(c.config, OpCreate)
	return &MaterialCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Material entities.
func (c *MaterialClient) CreateBulk(builders ...*MaterialCreate) *MaterialCreateBulk {
	return &MaterialCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MaterialClient) MapCreateBulk(slice any, setFunc func(*MaterialCreate, int)) *MaterialCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MaterialCreateBulk{err: fmt.Errorf("calling to MaterialClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MaterialCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MaterialCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Material.
func (c *MaterialClient) Update() *MaterialUpdate {
	mutation := newMaterialMutation(c.config, OpUpdate)
	return &MaterialUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MaterialClient) UpdateOne(_m *Material) *MaterialUpdateOne {
	mutation := newMaterialMutation(c.config, OpUpdateOne, withMaterial(_m))
	return &MaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MaterialClient) UpdateOneID(id int64) *MaterialUpdateOne {
	mutation := newMaterialMutation(c.config, OpUpdateOne, withMaterialID(id))
	return &MaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Material.
func (c *MaterialClient) Delete() *MaterialDelete {
	mutation := newMaterialMutation(c.config, OpDelete)
	return &MaterialDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MaterialClient) DeleteOne(_m *Material) *MaterialDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MaterialClient) DeleteOneID(id int64) *MaterialDeleteOne {
	builder := c.Delete().Where(material.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MaterialDeleteOne{builder}
}

// Query returns a query builder for Material.
func (c *MaterialClient) Query() *MaterialQuery {
	return &MaterialQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMaterial},
		inters: c.Interceptors(),
	}
}

// Get returns a Material entity by its id.
func (c *MaterialClient) Get(ctx context.Context, id int64) (*Material, error) {
	return c.Query().Where(material.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MaterialClient) GetX(ctx context.Context, id int64) *Material {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *MaterialClient) Hooks() []Hook {
	return c.hooks.Material
}

// Interceptors returns the client interceptors.
func (c *MaterialClient) Interceptors() []Interceptor {
	return c.inters.Material
}

func (c *MaterialClient) mutate(ctx context.Context, m *MaterialMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MaterialCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MaterialUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MaterialUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MaterialDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown Material mutation op: %q", m.Op())
	}
}

// MaterialGroupClient is a client for the MaterialGroup schema.
type MaterialGroupClient struct {
	config
}

// NewMaterialGroupClient returns a client for the MaterialGroup from the given config.
func NewMaterialGroupClient(c config) *MaterialGroupClient {
	return &MaterialGroupClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `materialgroup.Hooks(f(g(h())))`.
func (c *MaterialGroupClient) Use(hooks ...Hook) {
	c.hooks.MaterialGroup = append(c.hooks.MaterialGroup, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `materialgroup.Intercept(f(g(h())))`.
func (c *MaterialGroupClient) Intercept(interceptors ...Interceptor) {
	c.inters.MaterialGroup = append(c.inters.MaterialGroup, interceptors...)
}

// Create returns a builder for creating a MaterialGroup entity.
func (c *MaterialGroupClient) Create() *MaterialGroupCreate {
	mutation := newMaterialGroupMutation(c.config, OpCreate)
	return &MaterialGroupCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of MaterialGroup entities.
func (c *MaterialGroupClient) CreateBulk(builders ...*MaterialGroupCreate) *MaterialGroupCreateBulk {
	return &MaterialGroupCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MaterialGroupClient) MapCreateBulk(slice any, setFunc func(*MaterialGroupCreate, int)) *MaterialGroupCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MaterialGroupCreateBulk{err: fmt.Errorf("calling to MaterialGroupClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MaterialGroupCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MaterialGroupCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for MaterialGroup.
func (c *MaterialGroupClient) Update() *MaterialGroupUpdate {
	mutation := newMaterialGroupMutation(c.config, OpUpdate)
	return &MaterialGroupUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MaterialGroupClient) UpdateOne(_m *MaterialGroup) *MaterialGroupUpdateOne {
	mutation := newMaterialGroupMutation(c.config, OpUpdateOne, withMaterialGroup(_m))
	return &MaterialGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MaterialGroupClient) UpdateOneID(id int64) *MaterialGroupUpdateOne {
	mutation := newMaterialGroupMutation(c.config, OpUpdateOne, withMaterialGroupID(id))
	return &MaterialGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for MaterialGroup.
func (c *MaterialGroupClient) Delete() *MaterialGroupDelete {
	mutation := newMaterialGroupMutation(c.config, OpDelete)
	return &MaterialGroupDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MaterialGroupClient) DeleteOne(_m *MaterialGroup) *MaterialGroupDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MaterialGroupClient) DeleteOneID(id int64) *MaterialGroupDeleteOne {
	builder := c.Delete().Where(materialgroup.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MaterialGroupDeleteOne{builder}
}

// Query returns a query builder for MaterialGroup.
func (c *MaterialGroupClient) Query() *MaterialGroupQuery {
	return &MaterialGroupQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMaterialGroup},
		inters: c.Interceptors(),
	}
}

// Get returns a MaterialGroup entity by its id.
func (c *MaterialGroupClient) Get(ctx context.Context, id int64) (*MaterialGroup, error) {
	return c.Query().Where(materialgroup.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MaterialGroupClient) GetX(ctx context.Context, id int64) *MaterialGroup {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *MaterialGroupClient) Hooks() []Hook {
	return c.hooks.MaterialGroup
}

// Interceptors returns the client interceptors.
func (c *MaterialGroupClient) Interceptors() []Interceptor {
	return c.inters.MaterialGroup
}

func (c *MaterialGroupClient) mutate(ctx context.Context, m *MaterialGroupMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MaterialGroupCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MaterialGroupUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MaterialGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MaterialGroupDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown MaterialGroup mutation op: %q", m.Op())
	}
}

// ProductItemClient is a client for the ProductItem schema.
type ProductItemClient struct {
	config
}

// NewProductItemClient returns a client for the ProductItem from the given config.
func NewProductItemClient(c config) *ProductItemClient {
	return &ProductItemClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `productitem.Hooks(f(g(h())))`.
func (c *ProductItemClient) Use(hooks ...Hook) {
	c.hooks.ProductItem = append(c.hooks.ProductItem, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `productitem.Intercept(f(g(h())))`.
func (c *ProductItemClient) Intercept(interceptors ...Interceptor) {
	c.inters.ProductItem = append(c.inters.ProductItem, interceptors...)
}

// Create returns a builder for creating a ProductItem entity.
func (c *ProductItemClient) Create() *ProductItemCreate {
	mutation := newProductItemMutation(c.config, OpCreate)
	return &ProductItemCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ProductItem entities.
func (c *ProductItemClient) CreateBulk(builders ...*ProductItemCreate) *ProductItemCreateBulk {
	return &ProductItemCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ProductItemClient) MapCreateBulk(slice any, setFunc func(*ProductItemCreate, int)) *ProductItemCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ProductItemCreateBulk{err: fmt.Errorf("calling to ProductItemClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ProductItemCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ProductItemCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ProductItem.
func (c *ProductItemClient) Update() *ProductItemUpdate {
	mutation := newProductItemMutation(c.config, OpUpdate)
	return &ProductItemUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ProductItemClient) UpdateOne(_m *ProductItem) *ProductItemUpdateOne {
	mutation := newProductItemMutation(c.config, OpUpdateOne, withProductItem(_m))
	return &ProductItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ProductItemClient) UpdateOneID(id int64) *ProductItemUpdateOne {
	mutation := newProductItemMutation(c.config, OpUpdateOne, withProductItemID(id))
	return &ProductItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ProductItem.
func (c *ProductItemClient) Delete() *ProductItemDelete {
	mutation := newProductItemMutation(c.config, OpDelete)
	return &ProductItemDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ProductItemClient) DeleteOne(_m *ProductItem) *ProductItemDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ProductItemClient) DeleteOneID(id int64) *ProductItemDeleteOne {
	builder := c.Delete().Where(productitem.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ProductItemDeleteOne{builder}
}

// Query returns a query builder for ProductItem.
func (c *ProductItemClient) Query() *ProductItemQuery {
	return &ProductItemQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeProductItem},
		inters: c.Interceptors(),
	}
}

// Get returns a ProductItem entity by its id.
func (c *ProductItemClient) Get(ctx context.Context, id int64) (*ProductItem, error) {
	return c.Query().Where(productitem.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ProductItemClient) GetX(ctx context.Context, id int64) *ProductItem {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ProductItemClient) Hooks() []Hook {
	return c.hooks.ProductItem
}

// Interceptors returns the client interceptors.
func (c *ProductItemClient) Interceptors() []Interceptor {
	return c.inters.ProductItem
}

func (c *ProductItemClient) mutate(ctx context.Context, m *ProductItemMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ProductItemCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ProductItemUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ProductItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ProductItemDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown ProductItem mutation op: %q", m.Op())
	}
}

// PsdClient is a client for the Psd schema.
type PsdClient struct {
	config
}

// NewPsdClient returns a client for the Psd from the given config.
func NewPsdClient(c config) *PsdClient {
	return &PsdClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `psd.Hooks(f(g(h())))`.
func (c *PsdClient) Use(hooks ...Hook) {
	c.hooks.Psd = append(c.hooks.Psd, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `psd.Intercept(f(g(h())))`.
func (c *PsdClient) Intercept(interceptors ...Interceptor) {
	c.inters.Psd = append(c.inters.Psd, interceptors...)
}

// Create returns a builder for creating a Psd entity.
func (c *PsdClient) Create() *PsdCreate {
	mutation := newPsdMutation(c.config, OpCreate)
	return &PsdCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Psd entities.
func (c *PsdClient) CreateBulk(builders ...*PsdCreate) *PsdCreateBulk {
	return &PsdCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PsdClient) MapCreateBulk(slice any, setFunc func(*PsdCreate, int)) *PsdCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PsdCreateBulk{err: fmt.Errorf("calling to PsdClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PsdCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PsdCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Psd.
func (c *PsdClient) Update() *PsdUpdate {
	mutation := newPsdMutation(c.config, OpUpdate)
	return &PsdUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PsdClient) UpdateOne(_m *Psd) *PsdUpdateOne {
	mutation := newPsdMutation(c.config, OpUpdateOne, withPsd(_m))
	return &PsdUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PsdClient) UpdateOneID(id int64) *PsdUpdateOne {
	mutation := newPsdMutation(c.config, OpUpdateOne, withPsdID(id))
	return &PsdUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Psd.
func (c *PsdClient) Delete() *PsdDelete {
	mutation := newPsdMutation(c.config, OpDelete)
	return &PsdDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PsdClient) DeleteOne(_m *Psd) *PsdDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PsdClient) DeleteOneID(id int64) *PsdDeleteOne {
	builder := c.Delete().Where(psd.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PsdDeleteOne{builder}
}

// Query returns a query builder for Psd.
func (c *PsdClient) Query() *PsdQuery {
	return &PsdQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePsd},
		inters: c.Interceptors(),
	}
}

// Get returns a Psd entity by its id.
func (c *PsdClient) Get(ctx context.Context, id int64) (*Psd, error) {
	return c.Query().Where(psd.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PsdClient) GetX(ctx context.Context, id int64) *Psd {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PsdClient) Hooks() []Hook {
	return c.hooks.Psd
}

// Interceptors returns the client interceptors.
func (c *PsdClient) Interceptors() []Interceptor {
	return c.inters.Psd
}

func (c *PsdClient) mutate(ctx context.Context, m *PsdMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PsdCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PsdUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PsdUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PsdDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown Psd mutation op: %q", m.Op())
	}
}

// PsdCombineResultClient is a client for the PsdCombineResult schema.
type PsdCombineResultClient struct {
	config
}

// NewPsdCombineResultClient returns a client for the PsdCombineResult from the given config.
func NewPsdCombineResultClient(c config) *PsdCombineResultClient {
	return &PsdCombineResultClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `psdcombineresult.Hooks(f(g(h())))`.
func (c *PsdCombineResultClient) Use(hooks ...Hook) {
	c.hooks.PsdCombineResult = append(c.hooks.PsdCombineResult, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `psdcombineresult.Intercept(f(g(h())))`.
func (c *PsdCombineResultClient) Intercept(interceptors ...Interceptor) {
	c.inters.PsdCombineResult = append(c.inters.PsdCombineResult, interceptors...)
}

// Create returns a builder for creating a PsdCombineResult entity.
func (c *PsdCombineResultClient) Create() *PsdCombineResultCreate {
	mutation := newPsdCombineResultMutation(c.config, OpCreate)
	return &PsdCombineResultCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PsdCombineResult entities.
func (c *PsdCombineResultClient) CreateBulk(builders ...*PsdCombineResultCreate) *PsdCombineResultCreateBulk {
	return &PsdCombineResultCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PsdCombineResultClient) MapCreateBulk(slice any, setFunc func(*PsdCombineResultCreate, int)) *PsdCombineResultCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PsdCombineResultCreateBulk{err: fmt.Errorf("calling to PsdCombineResultClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PsdCombineResultCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PsdCombineResultCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PsdCombineResult.
func (c *PsdCombineResultClient) Update() *PsdCombineResultUpdate {
	mutation := newPsdCombineResultMutation(c.config, OpUpdate)
	return &PsdCombineResultUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PsdCombineResultClient) UpdateOne(_m *PsdCombineResult) *PsdCombineResultUpdateOne {
	mutation := newPsdCombineResultMutation(c.config, OpUpdateOne, withPsdCombineResult(_m))
	return &PsdCombineResultUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PsdCombineResultClient) UpdateOneID(id int64) *PsdCombineResultUpdateOne {
	mutation := newPsdCombineResultMutation(c.config, OpUpdateOne, withPsdCombineResultID(id))
	return &PsdCombineResultUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PsdCombineResult.
func (c *PsdCombineResultClient) Delete() *PsdCombineResultDelete {
	mutation := newPsdCombineResultMutation(c.config, OpDelete)
	return &PsdCombineResultDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PsdCombineResultClient) DeleteOne(_m *PsdCombineResult) *PsdCombineResultDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PsdCombineResultClient) DeleteOneID(id int64) *PsdCombineResultDeleteOne {
	builder := c.Delete().Where(psdcombineresult.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PsdCombineResultDeleteOne{builder}
}

// Query returns a query builder for PsdCombineResult.
func (c *PsdCombineResultClient) Query() *PsdCombineResultQuery {
	return &PsdCombineResultQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePsdCombineResult},
		inters: c.Interceptors(),
	}
}

// Get returns a PsdCombineResult entity by its id.
func (c *PsdCombineResultClient) Get(ctx context.Context, id int64) (*PsdCombineResult, error) {
	return c.Query().Where(psdcombineresult.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PsdCombineResultClient) GetX(ctx context.Context, id int64) *PsdCombineResult {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PsdCombineResultClient) Hooks() []Hook {
	return c.hooks.PsdCombineResult
}

// Interceptors returns the client interceptors.
func (c *PsdCombineResultClient) Interceptors() []Interceptor {
	return c.inters.PsdCombineResult
}

func (c *PsdCombineResultClient) mutate(ctx context.Context, m *PsdCombineResultMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PsdCombineResultCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PsdCombineResultUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PsdCombineResultUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PsdCombineResultDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown PsdCombineResult mutation op: %q", m.Op())
	}
}

// PsdCombineTaskClient is a client for the PsdCombineTask schema.
type PsdCombineTaskClient struct {
	config
}

// NewPsdCombineTaskClient returns a client for the PsdCombineTask from the given config.
func NewPsdCombineTaskClient(c config) *PsdCombineTaskClient {
	return &PsdCombineTaskClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `psdcombinetask.Hooks(f(g(h())))`.
func (c *PsdCombineTaskClient) Use(hooks ...Hook) {
	c.hooks.PsdCombineTask = append(c.hooks.PsdCombineTask, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `psdcombinetask.Intercept(f(g(h())))`.
func (c *PsdCombineTaskClient) Intercept(interceptors ...Interceptor) {
	c.inters.PsdCombineTask = append(c.inters.PsdCombineTask, interceptors...)
}

// Create returns a builder for creating a PsdCombineTask entity.
func (c *PsdCombineTaskClient) Create() *PsdCombineTaskCreate {
	mutation := newPsdCombineTaskMutation(c.config, OpCreate)
	return &PsdCombineTaskCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PsdCombineTask entities.
func (c *PsdCombineTaskClient) CreateBulk(builders ...*PsdCombineTaskCreate) *PsdCombineTaskCreateBulk {
	return &PsdCombineTaskCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PsdCombineTaskClient) MapCreateBulk(slice any, setFunc func(*PsdCombineTaskCreate, int)) *PsdCombineTaskCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PsdCombineTaskCreateBulk{err: fmt.Errorf("calling to PsdCombineTaskClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PsdCombineTaskCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PsdCombineTaskCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PsdCombineTask.
func (c *PsdCombineTaskClient) Update() *PsdCombineTaskUpdate {
	mutation := newPsdCombineTaskMutation(c.config, OpUpdate)
	return &PsdCombineTaskUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PsdCombineTaskClient) UpdateOne(_m *PsdCombineTask) *PsdCombineTaskUpdateOne {
	mutation := newPsdCombineTaskMutation(c.config, OpUpdateOne, withPsdCombineTask(_m))
	return &PsdCombineTaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PsdCombineTaskClient) UpdateOneID(id int64) *PsdCombineTaskUpdateOne {
	mutation := newPsdCombineTaskMutation(c.config, OpUpdateOne, withPsdCombineTaskID(id))
	return &PsdCombineTaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PsdCombineTask.
func (c *PsdCombineTaskClient) Delete() *PsdCombineTaskDelete {
	mutation := newPsdCombineTaskMutation(c.config, OpDelete)
	return &PsdCombineTaskDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PsdCombineTaskClient) DeleteOne(_m *PsdCombineTask) *PsdCombineTaskDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PsdCombineTaskClient) DeleteOneID(id int64) *PsdCombineTaskDeleteOne {
	builder := c.Delete().Where(psdcombinetask.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PsdCombineTaskDeleteOne{builder}
}

// Query returns a query builder for PsdCombineTask.
func (c *PsdCombineTaskClient) Query() *PsdCombineTaskQuery {
	return &PsdCombineTaskQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePsdCombineTask},
		inters: c.Interceptors(),
	}
}

// Get returns a PsdCombineTask entity by its id.
func (c *PsdCombineTaskClient) Get(ctx context.Context, id int64) (*PsdCombineTask, error) {
	return c.Query().Where(psdcombinetask.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PsdCombineTaskClient) GetX(ctx context.Context, id int64) *PsdCombineTask {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PsdCombineTaskClient) Hooks() []Hook {
	return c.hooks.PsdCombineTask
}

// Interceptors returns the client interceptors.
func (c *PsdCombineTaskClient) Interceptors() []Interceptor {
	return c.inters.PsdCombineTask
}

func (c *PsdCombineTaskClient) mutate(ctx context.Context, m *PsdCombineTaskMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PsdCombineTaskCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PsdCombineTaskUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PsdCombineTaskUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PsdCombineTaskDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown PsdCombineTask mutation op: %q", m.Op())
	}
}

// PsdGroupClient is a client for the PsdGroup schema.
type PsdGroupClient struct {
	config
}

// NewPsdGroupClient returns a client for the PsdGroup from the given config.
func NewPsdGroupClient(c config) *PsdGroupClient {
	return &PsdGroupClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `psdgroup.Hooks(f(g(h())))`.
func (c *PsdGroupClient) Use(hooks ...Hook) {
	c.hooks.PsdGroup = append(c.hooks.PsdGroup, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `psdgroup.Intercept(f(g(h())))`.
func (c *PsdGroupClient) Intercept(interceptors ...Interceptor) {
	c.inters.PsdGroup = append(c.inters.PsdGroup, interceptors...)
}

// Create returns a builder for creating a PsdGroup entity.
func (c *PsdGroupClient) Create() *PsdGroupCreate {
	mutation := newPsdGroupMutation(c.config, OpCreate)
	return &PsdGroupCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of PsdGroup entities.
func (c *PsdGroupClient) CreateBulk(builders ...*PsdGroupCreate) *PsdGroupCreateBulk {
	return &PsdGroupCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PsdGroupClient) MapCreateBulk(slice any, setFunc func(*PsdGroupCreate, int)) *PsdGroupCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PsdGroupCreateBulk{err: fmt.Errorf("calling to PsdGroupClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PsdGroupCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PsdGroupCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for PsdGroup.
func (c *PsdGroupClient) Update() *PsdGroupUpdate {
	mutation := newPsdGroupMutation(c.config, OpUpdate)
	return &PsdGroupUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PsdGroupClient) UpdateOne(_m *PsdGroup) *PsdGroupUpdateOne {
	mutation := newPsdGroupMutation(c.config, OpUpdateOne, withPsdGroup(_m))
	return &PsdGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PsdGroupClient) UpdateOneID(id int64) *PsdGroupUpdateOne {
	mutation := newPsdGroupMutation(c.config, OpUpdateOne, withPsdGroupID(id))
	return &PsdGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for PsdGroup.
func (c *PsdGroupClient) Delete() *PsdGroupDelete {
	mutation := newPsdGroupMutation(c.config, OpDelete)
	return &PsdGroupDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PsdGroupClient) DeleteOne(_m *PsdGroup) *PsdGroupDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PsdGroupClient) DeleteOneID(id int64) *PsdGroupDeleteOne {
	builder := c.Delete().Where(psdgroup.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PsdGroupDeleteOne{builder}
}

// Query returns a query builder for PsdGroup.
func (c *PsdGroupClient) Query() *PsdGroupQuery {
	return &PsdGroupQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePsdGroup},
		inters: c.Interceptors(),
	}
}

// Get returns a PsdGroup entity by its id.
func (c *PsdGroupClient) Get(ctx context.Context, id int64) (*PsdGroup, error) {
	return c.Query().Where(psdgroup.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PsdGroupClient) GetX(ctx context.Context, id int64) *PsdGroup {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *PsdGroupClient) Hooks() []Hook {
	return c.hooks.PsdGroup
}

// Interceptors returns the client interceptors.
func (c *PsdGroupClient) Interceptors() []Interceptor {
	return c.inters.PsdGroup
}

func (c *PsdGroupClient) mutate(ctx context.Context, m *PsdGroupMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PsdGroupCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PsdGroupUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PsdGroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PsdGroupDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown PsdGroup mutation op: %q", m.Op())
	}
}

// RegisteredTrademarkClient is a client for the RegisteredTrademark schema.
type RegisteredTrademarkClient struct {
	config
}

// NewRegisteredTrademarkClient returns a client for the RegisteredTrademark from the given config.
func NewRegisteredTrademarkClient(c config) *RegisteredTrademarkClient {
	return &RegisteredTrademarkClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `registeredtrademark.Hooks(f(g(h())))`.
func (c *RegisteredTrademarkClient) Use(hooks ...Hook) {
	c.hooks.RegisteredTrademark = append(c.hooks.RegisteredTrademark, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `registeredtrademark.Intercept(f(g(h())))`.
func (c *RegisteredTrademarkClient) Intercept(interceptors ...Interceptor) {
	c.inters.RegisteredTrademark = append(c.inters.RegisteredTrademark, interceptors...)
}

// Create returns a builder for creating a RegisteredTrademark entity.
func (c *RegisteredTrademarkClient) Create() *RegisteredTrademarkCreate {
	mutation := newRegisteredTrademarkMutation(c.config, OpCreate)
	return &RegisteredTrademarkCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of RegisteredTrademark entities.
func (c *RegisteredTrademarkClient) CreateBulk(builders ...*RegisteredTrademarkCreate) *RegisteredTrademarkCreateBulk {
	return &RegisteredTrademarkCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *RegisteredTrademarkClient) MapCreateBulk(slice any, setFunc func(*RegisteredTrademarkCreate, int)) *RegisteredTrademarkCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &RegisteredTrademarkCreateBulk{err: fmt.Errorf("calling to RegisteredTrademarkClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*RegisteredTrademarkCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &RegisteredTrademarkCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for RegisteredTrademark.
func (c *RegisteredTrademarkClient) Update() *RegisteredTrademarkUpdate {
	mutation := newRegisteredTrademarkMutation(c.config, OpUpdate)
	return &RegisteredTrademarkUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *RegisteredTrademarkClient) UpdateOne(_m *RegisteredTrademark) *RegisteredTrademarkUpdateOne {
	mutation := newRegisteredTrademarkMutation(c.config, OpUpdateOne, withRegisteredTrademark(_m))
	return &RegisteredTrademarkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *RegisteredTrademarkClient) UpdateOneID(id int64) *RegisteredTrademarkUpdateOne {
	mutation := newRegisteredTrademarkMutation(c.config, OpUpdateOne, withRegisteredTrademarkID(id))
	return &RegisteredTrademarkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for RegisteredTrademark.
func (c *RegisteredTrademarkClient) Delete() *RegisteredTrademarkDelete {
	mutation := newRegisteredTrademarkMutation(c.config, OpDelete)
	return &RegisteredTrademarkDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *RegisteredTrademarkClient) DeleteOne(_m *RegisteredTrademark) *RegisteredTrademarkDeleteOne {
	return c.DeleteOneID(_m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *RegisteredTrademarkClient) DeleteOneID(id int64) *RegisteredTrademarkDeleteOne {
	builder := c.Delete().Where(registeredtrademark.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &RegisteredTrademarkDeleteOne{builder}
}

// Query returns a query builder for RegisteredTrademark.
func (c *RegisteredTrademarkClient) Query() *RegisteredTrademarkQuery {
	return &RegisteredTrademarkQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeRegisteredTrademark},
		inters: c.Interceptors(),
	}
}

// Get returns a RegisteredTrademark entity by its id.
func (c *RegisteredTrademarkClient) Get(ctx context.Context, id int64) (*RegisteredTrademark, error) {
	return c.Query().Where(registeredtrademark.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *RegisteredTrademarkClient) GetX(ctx context.Context, id int64) *RegisteredTrademark {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *RegisteredTrademarkClient) Hooks() []Hook {
	return c.hooks.RegisteredTrademark
}

// Interceptors returns the client interceptors.
func (c *RegisteredTrademarkClient) Interceptors() []Interceptor {
	return c.inters.RegisteredTrademark
}

func (c *RegisteredTrademarkClient) mutate(ctx context.Context, m *RegisteredTrademarkMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&RegisteredTrademarkCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&RegisteredTrademarkUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&RegisteredTrademarkUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&RegisteredTrademarkDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("db1: unknown RegisteredTrademark mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		Administrator, Material, MaterialGroup, ProductItem, Psd, PsdCombineResult,
		PsdCombineTask, PsdGroup, RegisteredTrademark []ent.Hook
	}
	inters struct {
		Administrator, Material, MaterialGroup, ProductItem, Psd, PsdCombineResult,
		PsdCombineTask, PsdGroup, RegisteredTrademark []ent.Interceptor
	}
)

// ExecContext allows calling the underlying ExecContext method of the driver if it is supported by it.
// See, database/sql#DB.ExecContext for more information.
func (c *config) ExecContext(ctx context.Context, query string, args ...any) (stdsql.Result, error) {
	ex, ok := c.driver.(interface {
		ExecContext(context.Context, string, ...any) (stdsql.Result, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.ExecContext is not supported")
	}
	return ex.ExecContext(ctx, query, args...)
}

// QueryContext allows calling the underlying QueryContext method of the driver if it is supported by it.
// See, database/sql#DB.QueryContext for more information.
func (c *config) QueryContext(ctx context.Context, query string, args ...any) (*stdsql.Rows, error) {
	q, ok := c.driver.(interface {
		QueryContext(context.Context, string, ...any) (*stdsql.Rows, error)
	})
	if !ok {
		return nil, fmt.Errorf("Driver.QueryContext is not supported")
	}
	return q.QueryContext(ctx, query, args...)
}
