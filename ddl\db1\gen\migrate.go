//go:build ignore
package main

import (
    "context"
    "omnix/ddl/db1"
    "omnix/ddl/db1/migrate"
    "os"

    _ "github.com/lib/pq"
    "github.com/rs/zerolog/log"
)

//go:generate go run migrate.go
func main() {
    if len(os.Args) < 2 {
        log.Fatal().Msg("please provide a pgsql dsn")
    }
    dsn := os.Args[1]
    client, err := db1.Open("postgres", dsn)
    if err != nil {
        log.Fatal().Err(err).Msg("could not connect to postgres")
    }
    defer client.Close()
    ctx := context.Background()
    // Run migration.
    err = client.Schema.Create(
        ctx,
        //migrate.WithDropIndex(true),
        migrate.WithDropColumn(true),
    )
    if err != nil {
        log.Fatal().Err(err).Msg("could not create schema")
    }else{
        log.Info().Msg("migrate schema created")
    }
}
