{{/* The line below tells Intellij/GoLand to enable the autocompletion based *gen.Type type. */}}
{{/* gotype: entgo.io/ent/entc/gen.Type */}}

{{ define "update/additional/query_where" }}

{{/* A template that adds the "GoString" method to all generated models on the same file they are defined. */}}

{{ range $f := $.Fields }}
    {{ $func := $f.StructField }}
	{{/* JSON cannot be compared using "=" and <PERSON><PERSON> has a type defined with the field name */}}
	{{ $hasP := not (or $f.IsJSON $f.IsEnum) }}
	{{ $comparable := or $f.ConvertedToBasic $f.Type.Valuer }}
	{{ $undeclared := (and (ne $func "Label") (ne $func "OrderOption") (ne $func "Hooks") (ne $func "Policy") (ne $func "Table") ) }}
	{{- if and $hasP $comparable $undeclared }}
// Where{{$func}} applies equality check predicate to the {{ $.UpdateName }} builder.
func ({{ $.UpdateReceiver  }} *{{ $.UpdateName }}) Where{{ $func }}(v {{ $f.Type }}) *{{ $.UpdateName }} {
	{{ $.UpdateReceiver }}.Where({{ $.Package }}.{{ $func }}(v))
	return {{ $.UpdateReceiver }}
}
    {{ end }}

{{ end }}

{{ end }}
