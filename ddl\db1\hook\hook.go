// Code generated by ent, DO NOT EDIT.

package hook

import (
	"context"
	"fmt"
	"omnix/ddl/db1"
)

// The AdministratorFunc type is an adapter to allow the use of ordinary
// function as Administrator mutator.
type AdministratorFunc func(context.Context, *db1.AdministratorMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f AdministratorFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.AdministratorMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.AdministratorMutation", m)
}

// The MaterialFunc type is an adapter to allow the use of ordinary
// function as Material mutator.
type MaterialFunc func(context.Context, *db1.MaterialMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f MaterialFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.MaterialMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.MaterialMutation", m)
}

// The MaterialGroupFunc type is an adapter to allow the use of ordinary
// function as MaterialGroup mutator.
type MaterialGroupFunc func(context.Context, *db1.MaterialGroupMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f MaterialGroupFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.MaterialGroupMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.MaterialGroupMutation", m)
}

// The ProductItemFunc type is an adapter to allow the use of ordinary
// function as ProductItem mutator.
type ProductItemFunc func(context.Context, *db1.ProductItemMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f ProductItemFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.ProductItemMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.ProductItemMutation", m)
}

// The PsdFunc type is an adapter to allow the use of ordinary
// function as Psd mutator.
type PsdFunc func(context.Context, *db1.PsdMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f PsdFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.PsdMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.PsdMutation", m)
}

// The PsdCombineResultFunc type is an adapter to allow the use of ordinary
// function as PsdCombineResult mutator.
type PsdCombineResultFunc func(context.Context, *db1.PsdCombineResultMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f PsdCombineResultFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.PsdCombineResultMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.PsdCombineResultMutation", m)
}

// The PsdCombineTaskFunc type is an adapter to allow the use of ordinary
// function as PsdCombineTask mutator.
type PsdCombineTaskFunc func(context.Context, *db1.PsdCombineTaskMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f PsdCombineTaskFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.PsdCombineTaskMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.PsdCombineTaskMutation", m)
}

// The PsdGroupFunc type is an adapter to allow the use of ordinary
// function as PsdGroup mutator.
type PsdGroupFunc func(context.Context, *db1.PsdGroupMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f PsdGroupFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.PsdGroupMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.PsdGroupMutation", m)
}

// The RegisteredTrademarkFunc type is an adapter to allow the use of ordinary
// function as RegisteredTrademark mutator.
type RegisteredTrademarkFunc func(context.Context, *db1.RegisteredTrademarkMutation) (db1.Value, error)

// Mutate calls f(ctx, m).
func (f RegisteredTrademarkFunc) Mutate(ctx context.Context, m db1.Mutation) (db1.Value, error) {
	if mv, ok := m.(*db1.RegisteredTrademarkMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *db1.RegisteredTrademarkMutation", m)
}

// Condition is a hook condition function.
type Condition func(context.Context, db1.Mutation) bool

// And groups conditions with the AND operator.
func And(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m db1.Mutation) bool {
		if !first(ctx, m) || !second(ctx, m) {
			return false
		}
		for _, cond := range rest {
			if !cond(ctx, m) {
				return false
			}
		}
		return true
	}
}

// Or groups conditions with the OR operator.
func Or(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m db1.Mutation) bool {
		if first(ctx, m) || second(ctx, m) {
			return true
		}
		for _, cond := range rest {
			if cond(ctx, m) {
				return true
			}
		}
		return false
	}
}

// Not negates a given condition.
func Not(cond Condition) Condition {
	return func(ctx context.Context, m db1.Mutation) bool {
		return !cond(ctx, m)
	}
}

// HasOp is a condition testing mutation operation.
func HasOp(op db1.Op) Condition {
	return func(_ context.Context, m db1.Mutation) bool {
		return m.Op().Is(op)
	}
}

// HasAddedFields is a condition validating `.AddedField` on fields.
func HasAddedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m db1.Mutation) bool {
		if _, exists := m.AddedField(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.AddedField(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasClearedFields is a condition validating `.FieldCleared` on fields.
func HasClearedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m db1.Mutation) bool {
		if exists := m.FieldCleared(field); !exists {
			return false
		}
		for _, field := range fields {
			if exists := m.FieldCleared(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasFields is a condition validating `.Field` on fields.
func HasFields(field string, fields ...string) Condition {
	return func(_ context.Context, m db1.Mutation) bool {
		if _, exists := m.Field(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.Field(field); !exists {
				return false
			}
		}
		return true
	}
}

// If executes the given hook under condition.
//
//	hook.If(ComputeAverage, And(HasFields(...), HasAddedFields(...)))
func If(hk db1.Hook, cond Condition) db1.Hook {
	return func(next db1.Mutator) db1.Mutator {
		return db1.MutateFunc(func(ctx context.Context, m db1.Mutation) (db1.Value, error) {
			if cond(ctx, m) {
				return hk(next).Mutate(ctx, m)
			}
			return next.Mutate(ctx, m)
		})
	}
}

// On executes the given hook only for the given operation.
//
//	hook.On(Log, db1.Delete|db1.Create)
func On(hk db1.Hook, op db1.Op) db1.Hook {
	return If(hk, HasOp(op))
}

// Unless skips the given hook only for the given operation.
//
//	hook.Unless(Log, db1.Update|db1.UpdateOne)
func Unless(hk db1.Hook, op db1.Op) db1.Hook {
	return If(hk, Not(HasOp(op)))
}

// FixedError is a hook returning a fixed error.
func FixedError(err error) db1.Hook {
	return func(db1.Mutator) db1.Mutator {
		return db1.MutateFunc(func(context.Context, db1.Mutation) (db1.Value, error) {
			return nil, err
		})
	}
}

// Reject returns a hook that rejects all operations that match op.
//
//	func (T) Hooks() []db1.Hook {
//		return []db1.Hook{
//			Reject(db1.Delete|db1.Update),
//		}
//	}
func Reject(op db1.Op) db1.Hook {
	hk := FixedError(fmt.Errorf("%s operation is not allowed", op))
	return On(hk, op)
}

// Chain acts as a list of hooks and is effectively immutable.
// Once created, it will always hold the same set of hooks in the same order.
type Chain struct {
	hooks []db1.Hook
}

// NewChain creates a new chain of hooks.
func NewChain(hooks ...db1.Hook) Chain {
	return Chain{append([]db1.Hook(nil), hooks...)}
}

// Hook chains the list of hooks and returns the final hook.
func (c Chain) Hook() db1.Hook {
	return func(mutator db1.Mutator) db1.Mutator {
		for i := len(c.hooks) - 1; i >= 0; i-- {
			mutator = c.hooks[i](mutator)
		}
		return mutator
	}
}

// Append extends a chain, adding the specified hook
// as the last ones in the mutation flow.
func (c Chain) Append(hooks ...db1.Hook) Chain {
	newHooks := make([]db1.Hook, 0, len(c.hooks)+len(hooks))
	newHooks = append(newHooks, c.hooks...)
	newHooks = append(newHooks, hooks...)
	return Chain{newHooks}
}

// Extend extends a chain, adding the specified chain
// as the last ones in the mutation flow.
func (c Chain) Extend(chain Chain) Chain {
	return c.Append(chain.hooks...)
}
