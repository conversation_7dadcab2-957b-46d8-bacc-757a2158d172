// Code generated by ent, DO NOT EDIT.

package intercept

import (
	"context"
	"fmt"

	"omnix/ddl/db1"
	"omnix/ddl/db1/administrator"
	"omnix/ddl/db1/material"
	"omnix/ddl/db1/materialgroup"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/productitem"
	"omnix/ddl/db1/psd"
	"omnix/ddl/db1/psdcombineresult"
	"omnix/ddl/db1/psdcombinetask"
	"omnix/ddl/db1/psdgroup"
	"omnix/ddl/db1/registeredtrademark"

	"entgo.io/ent/dialect/sql"
)

// The Query interface represents an operation that queries a graph.
// By using this interface, users can write generic code that manipulates
// query builders of different types.
type Query interface {
	// Type returns the string representation of the query type.
	Type() string
	// Limit the number of records to be returned by this query.
	Limit(int)
	// Offset to start from.
	Offset(int)
	// Unique configures the query builder to filter duplicate records.
	Unique(bool)
	// Order specifies how the records should be ordered.
	Order(...func(*sql.Selector))
	// WhereP appends storage-level predicates to the query builder. Using this method, users
	// can use type-assertion to append predicates that do not depend on any generated package.
	WhereP(...func(*sql.Selector))
}

// The Func type is an adapter that allows ordinary functions to be used as interceptors.
// Unlike traversal functions, interceptors are skipped during graph traversals. Note that the
// implementation of Func is different from the one defined in entgo.io/ent.InterceptFunc.
type Func func(context.Context, Query) error

// Intercept calls f(ctx, q) and then applied the next Querier.
func (f Func) Intercept(next db1.Querier) db1.Querier {
	return db1.QuerierFunc(func(ctx context.Context, q db1.Query) (db1.Value, error) {
		query, err := NewQuery(q)
		if err != nil {
			return nil, err
		}
		if err := f(ctx, query); err != nil {
			return nil, err
		}
		return next.Query(ctx, q)
	})
}

// The TraverseFunc type is an adapter to allow the use of ordinary function as Traverser.
// If f is a function with the appropriate signature, TraverseFunc(f) is a Traverser that calls f.
type TraverseFunc func(context.Context, Query) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFunc) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFunc) Traverse(ctx context.Context, q db1.Query) error {
	query, err := NewQuery(q)
	if err != nil {
		return err
	}
	return f(ctx, query)
}

// The AdministratorFunc type is an adapter to allow the use of ordinary function as a Querier.
type AdministratorFunc func(context.Context, *db1.AdministratorQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f AdministratorFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.AdministratorQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.AdministratorQuery", q)
}

// The TraverseAdministrator type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAdministrator func(context.Context, *db1.AdministratorQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAdministrator) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAdministrator) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.AdministratorQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.AdministratorQuery", q)
}

// The MaterialFunc type is an adapter to allow the use of ordinary function as a Querier.
type MaterialFunc func(context.Context, *db1.MaterialQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f MaterialFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.MaterialQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.MaterialQuery", q)
}

// The TraverseMaterial type is an adapter to allow the use of ordinary function as Traverser.
type TraverseMaterial func(context.Context, *db1.MaterialQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseMaterial) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseMaterial) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.MaterialQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.MaterialQuery", q)
}

// The MaterialGroupFunc type is an adapter to allow the use of ordinary function as a Querier.
type MaterialGroupFunc func(context.Context, *db1.MaterialGroupQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f MaterialGroupFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.MaterialGroupQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.MaterialGroupQuery", q)
}

// The TraverseMaterialGroup type is an adapter to allow the use of ordinary function as Traverser.
type TraverseMaterialGroup func(context.Context, *db1.MaterialGroupQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseMaterialGroup) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseMaterialGroup) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.MaterialGroupQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.MaterialGroupQuery", q)
}

// The ProductItemFunc type is an adapter to allow the use of ordinary function as a Querier.
type ProductItemFunc func(context.Context, *db1.ProductItemQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f ProductItemFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.ProductItemQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.ProductItemQuery", q)
}

// The TraverseProductItem type is an adapter to allow the use of ordinary function as Traverser.
type TraverseProductItem func(context.Context, *db1.ProductItemQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseProductItem) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseProductItem) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.ProductItemQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.ProductItemQuery", q)
}

// The PsdFunc type is an adapter to allow the use of ordinary function as a Querier.
type PsdFunc func(context.Context, *db1.PsdQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f PsdFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.PsdQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.PsdQuery", q)
}

// The TraversePsd type is an adapter to allow the use of ordinary function as Traverser.
type TraversePsd func(context.Context, *db1.PsdQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePsd) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePsd) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.PsdQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.PsdQuery", q)
}

// The PsdCombineResultFunc type is an adapter to allow the use of ordinary function as a Querier.
type PsdCombineResultFunc func(context.Context, *db1.PsdCombineResultQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f PsdCombineResultFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.PsdCombineResultQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.PsdCombineResultQuery", q)
}

// The TraversePsdCombineResult type is an adapter to allow the use of ordinary function as Traverser.
type TraversePsdCombineResult func(context.Context, *db1.PsdCombineResultQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePsdCombineResult) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePsdCombineResult) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.PsdCombineResultQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.PsdCombineResultQuery", q)
}

// The PsdCombineTaskFunc type is an adapter to allow the use of ordinary function as a Querier.
type PsdCombineTaskFunc func(context.Context, *db1.PsdCombineTaskQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f PsdCombineTaskFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.PsdCombineTaskQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.PsdCombineTaskQuery", q)
}

// The TraversePsdCombineTask type is an adapter to allow the use of ordinary function as Traverser.
type TraversePsdCombineTask func(context.Context, *db1.PsdCombineTaskQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePsdCombineTask) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePsdCombineTask) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.PsdCombineTaskQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.PsdCombineTaskQuery", q)
}

// The PsdGroupFunc type is an adapter to allow the use of ordinary function as a Querier.
type PsdGroupFunc func(context.Context, *db1.PsdGroupQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f PsdGroupFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.PsdGroupQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.PsdGroupQuery", q)
}

// The TraversePsdGroup type is an adapter to allow the use of ordinary function as Traverser.
type TraversePsdGroup func(context.Context, *db1.PsdGroupQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePsdGroup) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePsdGroup) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.PsdGroupQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.PsdGroupQuery", q)
}

// The RegisteredTrademarkFunc type is an adapter to allow the use of ordinary function as a Querier.
type RegisteredTrademarkFunc func(context.Context, *db1.RegisteredTrademarkQuery) (db1.Value, error)

// Query calls f(ctx, q).
func (f RegisteredTrademarkFunc) Query(ctx context.Context, q db1.Query) (db1.Value, error) {
	if q, ok := q.(*db1.RegisteredTrademarkQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *db1.RegisteredTrademarkQuery", q)
}

// The TraverseRegisteredTrademark type is an adapter to allow the use of ordinary function as Traverser.
type TraverseRegisteredTrademark func(context.Context, *db1.RegisteredTrademarkQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseRegisteredTrademark) Intercept(next db1.Querier) db1.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseRegisteredTrademark) Traverse(ctx context.Context, q db1.Query) error {
	if q, ok := q.(*db1.RegisteredTrademarkQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *db1.RegisteredTrademarkQuery", q)
}

// NewQuery returns the generic Query interface for the given typed query.
func NewQuery(q db1.Query) (Query, error) {
	switch q := q.(type) {
	case *db1.AdministratorQuery:
		return &query[*db1.AdministratorQuery, predicate.Administrator, administrator.OrderOption]{typ: db1.TypeAdministrator, tq: q}, nil
	case *db1.MaterialQuery:
		return &query[*db1.MaterialQuery, predicate.Material, material.OrderOption]{typ: db1.TypeMaterial, tq: q}, nil
	case *db1.MaterialGroupQuery:
		return &query[*db1.MaterialGroupQuery, predicate.MaterialGroup, materialgroup.OrderOption]{typ: db1.TypeMaterialGroup, tq: q}, nil
	case *db1.ProductItemQuery:
		return &query[*db1.ProductItemQuery, predicate.ProductItem, productitem.OrderOption]{typ: db1.TypeProductItem, tq: q}, nil
	case *db1.PsdQuery:
		return &query[*db1.PsdQuery, predicate.Psd, psd.OrderOption]{typ: db1.TypePsd, tq: q}, nil
	case *db1.PsdCombineResultQuery:
		return &query[*db1.PsdCombineResultQuery, predicate.PsdCombineResult, psdcombineresult.OrderOption]{typ: db1.TypePsdCombineResult, tq: q}, nil
	case *db1.PsdCombineTaskQuery:
		return &query[*db1.PsdCombineTaskQuery, predicate.PsdCombineTask, psdcombinetask.OrderOption]{typ: db1.TypePsdCombineTask, tq: q}, nil
	case *db1.PsdGroupQuery:
		return &query[*db1.PsdGroupQuery, predicate.PsdGroup, psdgroup.OrderOption]{typ: db1.TypePsdGroup, tq: q}, nil
	case *db1.RegisteredTrademarkQuery:
		return &query[*db1.RegisteredTrademarkQuery, predicate.RegisteredTrademark, registeredtrademark.OrderOption]{typ: db1.TypeRegisteredTrademark, tq: q}, nil
	default:
		return nil, fmt.Errorf("unknown query type %T", q)
	}
}

type query[T any, P ~func(*sql.Selector), R ~func(*sql.Selector)] struct {
	typ string
	tq  interface {
		Limit(int) T
		Offset(int) T
		Unique(bool) T
		Order(...R) T
		Where(...P) T
	}
}

func (q query[T, P, R]) Type() string {
	return q.typ
}

func (q query[T, P, R]) Limit(limit int) {
	q.tq.Limit(limit)
}

func (q query[T, P, R]) Offset(offset int) {
	q.tq.Offset(offset)
}

func (q query[T, P, R]) Unique(unique bool) {
	q.tq.Unique(unique)
}

func (q query[T, P, R]) Order(orders ...func(*sql.Selector)) {
	rs := make([]R, len(orders))
	for i := range orders {
		rs[i] = orders[i]
	}
	q.tq.Order(rs...)
}

func (q query[T, P, R]) WhereP(ps ...func(*sql.Selector)) {
	p := make([]P, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	q.tq.Where(p...)
}
