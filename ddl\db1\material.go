// Code generated by ent, DO NOT EDIT.

package db1

import (
	"fmt"
	"omnix/ddl/db1/material"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

// Material is the model entity for the Material schema.
type Material struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedTime time.Time `json:"created_time,omitempty"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty"`
	// 素材标题
	Title string `json:"title,omitempty"`
	// 原始素材地址
	SourceURL string `json:"source_url,omitempty"`
	// 素材组
	MaterialGroup pq.Int64Array `json:"material_group,omitempty"`
	// 素材标记
	Flag pq.StringArray `json:"flag,omitempty"`
	// 关联Temu采集
	SourceTemuID int64 `json:"source_temu_id,omitempty"`
	// 文件校验码
	Hash string `json:"hash,omitempty"`
	// 素材S3存储路径
	Path         string `json:"path,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Material) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case material.FieldMaterialGroup:
			values[i] = new(pq.Int64Array)
		case material.FieldFlag:
			values[i] = new(pq.StringArray)
		case material.FieldID, material.FieldSourceTemuID:
			values[i] = new(sql.NullInt64)
		case material.FieldTitle, material.FieldSourceURL, material.FieldHash, material.FieldPath:
			values[i] = new(sql.NullString)
		case material.FieldCreatedTime, material.FieldUpdatedTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Material fields.
func (_m *Material) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case material.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case material.FieldCreatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_time", values[i])
			} else if value.Valid {
				_m.CreatedTime = value.Time
			}
		case material.FieldUpdatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_time", values[i])
			} else if value.Valid {
				_m.UpdatedTime = value.Time
			}
		case material.FieldTitle:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field title", values[i])
			} else if value.Valid {
				_m.Title = value.String
			}
		case material.FieldSourceURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field source_url", values[i])
			} else if value.Valid {
				_m.SourceURL = value.String
			}
		case material.FieldMaterialGroup:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field material_group", values[i])
			} else if value != nil {
				_m.MaterialGroup = *value
			}
		case material.FieldFlag:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field flag", values[i])
			} else if value != nil {
				_m.Flag = *value
			}
		case material.FieldSourceTemuID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field source_temu_id", values[i])
			} else if value.Valid {
				_m.SourceTemuID = value.Int64
			}
		case material.FieldHash:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field hash", values[i])
			} else if value.Valid {
				_m.Hash = value.String
			}
		case material.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				_m.Path = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Material.
// This includes values selected through modifiers, order, etc.
func (_m *Material) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Material.
// Note that you need to call Material.Unwrap() before calling this method if this Material
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Material) Update() *MaterialUpdateOne {
	return NewMaterialClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Material entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Material) Unwrap() *Material {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("db1: Material is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Material) String() string {
	var builder strings.Builder
	builder.WriteString("Material(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("created_time=")
	builder.WriteString(_m.CreatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_time=")
	builder.WriteString(_m.UpdatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("title=")
	builder.WriteString(_m.Title)
	builder.WriteString(", ")
	builder.WriteString("source_url=")
	builder.WriteString(_m.SourceURL)
	builder.WriteString(", ")
	builder.WriteString("material_group=")
	builder.WriteString(fmt.Sprintf("%v", _m.MaterialGroup))
	builder.WriteString(", ")
	builder.WriteString("flag=")
	builder.WriteString(fmt.Sprintf("%v", _m.Flag))
	builder.WriteString(", ")
	builder.WriteString("source_temu_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.SourceTemuID))
	builder.WriteString(", ")
	builder.WriteString("hash=")
	builder.WriteString(_m.Hash)
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(_m.Path)
	builder.WriteByte(')')
	return builder.String()
}

// Materials is a parsable slice of Material.
type Materials []*Material
