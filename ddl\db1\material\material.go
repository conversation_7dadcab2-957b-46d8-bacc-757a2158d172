// Code generated by ent, DO NOT EDIT.

package material

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the material type in the database.
	Label = "material"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedTime holds the string denoting the created_time field in the database.
	FieldCreatedTime = "created_time"
	// FieldUpdatedTime holds the string denoting the updated_time field in the database.
	FieldUpdatedTime = "updated_time"
	// FieldTitle holds the string denoting the title field in the database.
	FieldTitle = "title"
	// FieldSourceURL holds the string denoting the source_url field in the database.
	FieldSourceURL = "source_url"
	// FieldMaterialGroup holds the string denoting the material_group field in the database.
	FieldMaterialGroup = "material_group"
	// FieldFlag holds the string denoting the flag field in the database.
	FieldFlag = "flag"
	// FieldSourceTemuID holds the string denoting the source_temu_id field in the database.
	FieldSourceTemuID = "source_temu_id"
	// FieldHash holds the string denoting the hash field in the database.
	FieldHash = "hash"
	// FieldPath holds the string denoting the path field in the database.
	FieldPath = "path"
	// Table holds the table name of the material in the database.
	Table = "material"
)

// Columns holds all SQL columns for material fields.
var Columns = []string{
	FieldID,
	FieldCreatedTime,
	FieldUpdatedTime,
	FieldTitle,
	FieldSourceURL,
	FieldMaterialGroup,
	FieldFlag,
	FieldSourceTemuID,
	FieldHash,
	FieldPath,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedTime holds the default value on creation for the "created_time" field.
	DefaultCreatedTime func() time.Time
	// DefaultUpdatedTime holds the default value on creation for the "updated_time" field.
	DefaultUpdatedTime func() time.Time
	// UpdateDefaultUpdatedTime holds the default value on update for the "updated_time" field.
	UpdateDefaultUpdatedTime func() time.Time
)

// OrderOption defines the ordering options for the Material queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedTime orders the results by the created_time field.
func ByCreatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedTime, opts...).ToFunc()
}

// ByUpdatedTime orders the results by the updated_time field.
func ByUpdatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedTime, opts...).ToFunc()
}

// ByTitle orders the results by the title field.
func ByTitle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTitle, opts...).ToFunc()
}

// BySourceURL orders the results by the source_url field.
func BySourceURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSourceURL, opts...).ToFunc()
}

// ByMaterialGroup orders the results by the material_group field.
func ByMaterialGroup(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaterialGroup, opts...).ToFunc()
}

// ByFlag orders the results by the flag field.
func ByFlag(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFlag, opts...).ToFunc()
}

// BySourceTemuID orders the results by the source_temu_id field.
func BySourceTemuID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSourceTemuID, opts...).ToFunc()
}

// ByHash orders the results by the hash field.
func ByHash(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHash, opts...).ToFunc()
}

// ByPath orders the results by the path field.
func ByPath(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPath, opts...).ToFunc()
}
