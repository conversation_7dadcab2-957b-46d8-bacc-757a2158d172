// Code generated by ent, DO NOT EDIT.

package material

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldUpdatedTime, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldTitle, v))
}

// SourceURL applies equality check predicate on the "source_url" field. It's identical to SourceURLEQ.
func SourceURL(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldSourceURL, v))
}

// MaterialGroup applies equality check predicate on the "material_group" field. It's identical to MaterialGroupEQ.
func MaterialGroup(v pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldMaterialGroup, v))
}

// Flag applies equality check predicate on the "flag" field. It's identical to FlagEQ.
func Flag(v pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldFlag, v))
}

// SourceTemuID applies equality check predicate on the "source_temu_id" field. It's identical to SourceTemuIDEQ.
func SourceTemuID(v int64) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldSourceTemuID, v))
}

// Hash applies equality check predicate on the "hash" field. It's identical to HashEQ.
func Hash(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldHash, v))
}

// Path applies equality check predicate on the "path" field. It's identical to PathEQ.
func Path(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldPath, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldUpdatedTime, v))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.Material {
	return predicate.Material(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.Material {
	return predicate.Material(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.Material {
	return predicate.Material(sql.FieldContainsFold(FieldTitle, v))
}

// SourceURLEQ applies the EQ predicate on the "source_url" field.
func SourceURLEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldSourceURL, v))
}

// SourceURLNEQ applies the NEQ predicate on the "source_url" field.
func SourceURLNEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldSourceURL, v))
}

// SourceURLIn applies the In predicate on the "source_url" field.
func SourceURLIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldSourceURL, vs...))
}

// SourceURLNotIn applies the NotIn predicate on the "source_url" field.
func SourceURLNotIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldSourceURL, vs...))
}

// SourceURLGT applies the GT predicate on the "source_url" field.
func SourceURLGT(v string) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldSourceURL, v))
}

// SourceURLGTE applies the GTE predicate on the "source_url" field.
func SourceURLGTE(v string) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldSourceURL, v))
}

// SourceURLLT applies the LT predicate on the "source_url" field.
func SourceURLLT(v string) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldSourceURL, v))
}

// SourceURLLTE applies the LTE predicate on the "source_url" field.
func SourceURLLTE(v string) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldSourceURL, v))
}

// SourceURLContains applies the Contains predicate on the "source_url" field.
func SourceURLContains(v string) predicate.Material {
	return predicate.Material(sql.FieldContains(FieldSourceURL, v))
}

// SourceURLHasPrefix applies the HasPrefix predicate on the "source_url" field.
func SourceURLHasPrefix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasPrefix(FieldSourceURL, v))
}

// SourceURLHasSuffix applies the HasSuffix predicate on the "source_url" field.
func SourceURLHasSuffix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasSuffix(FieldSourceURL, v))
}

// SourceURLEqualFold applies the EqualFold predicate on the "source_url" field.
func SourceURLEqualFold(v string) predicate.Material {
	return predicate.Material(sql.FieldEqualFold(FieldSourceURL, v))
}

// SourceURLContainsFold applies the ContainsFold predicate on the "source_url" field.
func SourceURLContainsFold(v string) predicate.Material {
	return predicate.Material(sql.FieldContainsFold(FieldSourceURL, v))
}

// MaterialGroupEQ applies the EQ predicate on the "material_group" field.
func MaterialGroupEQ(v pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldMaterialGroup, v))
}

// MaterialGroupNEQ applies the NEQ predicate on the "material_group" field.
func MaterialGroupNEQ(v pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldMaterialGroup, v))
}

// MaterialGroupIn applies the In predicate on the "material_group" field.
func MaterialGroupIn(vs ...pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldMaterialGroup, vs...))
}

// MaterialGroupNotIn applies the NotIn predicate on the "material_group" field.
func MaterialGroupNotIn(vs ...pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldMaterialGroup, vs...))
}

// MaterialGroupGT applies the GT predicate on the "material_group" field.
func MaterialGroupGT(v pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldMaterialGroup, v))
}

// MaterialGroupGTE applies the GTE predicate on the "material_group" field.
func MaterialGroupGTE(v pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldMaterialGroup, v))
}

// MaterialGroupLT applies the LT predicate on the "material_group" field.
func MaterialGroupLT(v pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldMaterialGroup, v))
}

// MaterialGroupLTE applies the LTE predicate on the "material_group" field.
func MaterialGroupLTE(v pq.Int64Array) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldMaterialGroup, v))
}

// MaterialGroupIsNil applies the IsNil predicate on the "material_group" field.
func MaterialGroupIsNil() predicate.Material {
	return predicate.Material(sql.FieldIsNull(FieldMaterialGroup))
}

// MaterialGroupNotNil applies the NotNil predicate on the "material_group" field.
func MaterialGroupNotNil() predicate.Material {
	return predicate.Material(sql.FieldNotNull(FieldMaterialGroup))
}

// FlagEQ applies the EQ predicate on the "flag" field.
func FlagEQ(v pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldFlag, v))
}

// FlagNEQ applies the NEQ predicate on the "flag" field.
func FlagNEQ(v pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldFlag, v))
}

// FlagIn applies the In predicate on the "flag" field.
func FlagIn(vs ...pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldFlag, vs...))
}

// FlagNotIn applies the NotIn predicate on the "flag" field.
func FlagNotIn(vs ...pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldFlag, vs...))
}

// FlagGT applies the GT predicate on the "flag" field.
func FlagGT(v pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldFlag, v))
}

// FlagGTE applies the GTE predicate on the "flag" field.
func FlagGTE(v pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldFlag, v))
}

// FlagLT applies the LT predicate on the "flag" field.
func FlagLT(v pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldFlag, v))
}

// FlagLTE applies the LTE predicate on the "flag" field.
func FlagLTE(v pq.StringArray) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldFlag, v))
}

// FlagIsNil applies the IsNil predicate on the "flag" field.
func FlagIsNil() predicate.Material {
	return predicate.Material(sql.FieldIsNull(FieldFlag))
}

// FlagNotNil applies the NotNil predicate on the "flag" field.
func FlagNotNil() predicate.Material {
	return predicate.Material(sql.FieldNotNull(FieldFlag))
}

// SourceTemuIDEQ applies the EQ predicate on the "source_temu_id" field.
func SourceTemuIDEQ(v int64) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldSourceTemuID, v))
}

// SourceTemuIDNEQ applies the NEQ predicate on the "source_temu_id" field.
func SourceTemuIDNEQ(v int64) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldSourceTemuID, v))
}

// SourceTemuIDIn applies the In predicate on the "source_temu_id" field.
func SourceTemuIDIn(vs ...int64) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldSourceTemuID, vs...))
}

// SourceTemuIDNotIn applies the NotIn predicate on the "source_temu_id" field.
func SourceTemuIDNotIn(vs ...int64) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldSourceTemuID, vs...))
}

// SourceTemuIDGT applies the GT predicate on the "source_temu_id" field.
func SourceTemuIDGT(v int64) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldSourceTemuID, v))
}

// SourceTemuIDGTE applies the GTE predicate on the "source_temu_id" field.
func SourceTemuIDGTE(v int64) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldSourceTemuID, v))
}

// SourceTemuIDLT applies the LT predicate on the "source_temu_id" field.
func SourceTemuIDLT(v int64) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldSourceTemuID, v))
}

// SourceTemuIDLTE applies the LTE predicate on the "source_temu_id" field.
func SourceTemuIDLTE(v int64) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldSourceTemuID, v))
}

// HashEQ applies the EQ predicate on the "hash" field.
func HashEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldHash, v))
}

// HashNEQ applies the NEQ predicate on the "hash" field.
func HashNEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldHash, v))
}

// HashIn applies the In predicate on the "hash" field.
func HashIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldHash, vs...))
}

// HashNotIn applies the NotIn predicate on the "hash" field.
func HashNotIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldHash, vs...))
}

// HashGT applies the GT predicate on the "hash" field.
func HashGT(v string) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldHash, v))
}

// HashGTE applies the GTE predicate on the "hash" field.
func HashGTE(v string) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldHash, v))
}

// HashLT applies the LT predicate on the "hash" field.
func HashLT(v string) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldHash, v))
}

// HashLTE applies the LTE predicate on the "hash" field.
func HashLTE(v string) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldHash, v))
}

// HashContains applies the Contains predicate on the "hash" field.
func HashContains(v string) predicate.Material {
	return predicate.Material(sql.FieldContains(FieldHash, v))
}

// HashHasPrefix applies the HasPrefix predicate on the "hash" field.
func HashHasPrefix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasPrefix(FieldHash, v))
}

// HashHasSuffix applies the HasSuffix predicate on the "hash" field.
func HashHasSuffix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasSuffix(FieldHash, v))
}

// HashEqualFold applies the EqualFold predicate on the "hash" field.
func HashEqualFold(v string) predicate.Material {
	return predicate.Material(sql.FieldEqualFold(FieldHash, v))
}

// HashContainsFold applies the ContainsFold predicate on the "hash" field.
func HashContainsFold(v string) predicate.Material {
	return predicate.Material(sql.FieldContainsFold(FieldHash, v))
}

// PathEQ applies the EQ predicate on the "path" field.
func PathEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldEQ(FieldPath, v))
}

// PathNEQ applies the NEQ predicate on the "path" field.
func PathNEQ(v string) predicate.Material {
	return predicate.Material(sql.FieldNEQ(FieldPath, v))
}

// PathIn applies the In predicate on the "path" field.
func PathIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldIn(FieldPath, vs...))
}

// PathNotIn applies the NotIn predicate on the "path" field.
func PathNotIn(vs ...string) predicate.Material {
	return predicate.Material(sql.FieldNotIn(FieldPath, vs...))
}

// PathGT applies the GT predicate on the "path" field.
func PathGT(v string) predicate.Material {
	return predicate.Material(sql.FieldGT(FieldPath, v))
}

// PathGTE applies the GTE predicate on the "path" field.
func PathGTE(v string) predicate.Material {
	return predicate.Material(sql.FieldGTE(FieldPath, v))
}

// PathLT applies the LT predicate on the "path" field.
func PathLT(v string) predicate.Material {
	return predicate.Material(sql.FieldLT(FieldPath, v))
}

// PathLTE applies the LTE predicate on the "path" field.
func PathLTE(v string) predicate.Material {
	return predicate.Material(sql.FieldLTE(FieldPath, v))
}

// PathContains applies the Contains predicate on the "path" field.
func PathContains(v string) predicate.Material {
	return predicate.Material(sql.FieldContains(FieldPath, v))
}

// PathHasPrefix applies the HasPrefix predicate on the "path" field.
func PathHasPrefix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasPrefix(FieldPath, v))
}

// PathHasSuffix applies the HasSuffix predicate on the "path" field.
func PathHasSuffix(v string) predicate.Material {
	return predicate.Material(sql.FieldHasSuffix(FieldPath, v))
}

// PathEqualFold applies the EqualFold predicate on the "path" field.
func PathEqualFold(v string) predicate.Material {
	return predicate.Material(sql.FieldEqualFold(FieldPath, v))
}

// PathContainsFold applies the ContainsFold predicate on the "path" field.
func PathContainsFold(v string) predicate.Material {
	return predicate.Material(sql.FieldContainsFold(FieldPath, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Material) predicate.Material {
	return predicate.Material(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Material) predicate.Material {
	return predicate.Material(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Material) predicate.Material {
	return predicate.Material(sql.NotPredicates(p))
}
