// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/material"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// MaterialCreate is the builder for creating a Material entity.
type MaterialCreate struct {
	config
	mutation *MaterialMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *MaterialCreate) SetCreatedTime(v time.Time) *MaterialCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *MaterialCreate) SetNillableCreatedTime(v *time.Time) *MaterialCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *MaterialCreate) SetUpdatedTime(v time.Time) *MaterialCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *MaterialCreate) SetNillableUpdatedTime(v *time.Time) *MaterialCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetTitle sets the "title" field.
func (_c *MaterialCreate) SetTitle(v string) *MaterialCreate {
	_c.mutation.SetTitle(v)
	return _c
}

// SetSourceURL sets the "source_url" field.
func (_c *MaterialCreate) SetSourceURL(v string) *MaterialCreate {
	_c.mutation.SetSourceURL(v)
	return _c
}

// SetMaterialGroup sets the "material_group" field.
func (_c *MaterialCreate) SetMaterialGroup(v pq.Int64Array) *MaterialCreate {
	_c.mutation.SetMaterialGroup(v)
	return _c
}

// SetFlag sets the "flag" field.
func (_c *MaterialCreate) SetFlag(v pq.StringArray) *MaterialCreate {
	_c.mutation.SetFlag(v)
	return _c
}

// SetSourceTemuID sets the "source_temu_id" field.
func (_c *MaterialCreate) SetSourceTemuID(v int64) *MaterialCreate {
	_c.mutation.SetSourceTemuID(v)
	return _c
}

// SetHash sets the "hash" field.
func (_c *MaterialCreate) SetHash(v string) *MaterialCreate {
	_c.mutation.SetHash(v)
	return _c
}

// SetPath sets the "path" field.
func (_c *MaterialCreate) SetPath(v string) *MaterialCreate {
	_c.mutation.SetPath(v)
	return _c
}

// SetID sets the "id" field.
func (_c *MaterialCreate) SetID(v int64) *MaterialCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the MaterialMutation object of the builder.
func (_c *MaterialCreate) Mutation() *MaterialMutation {
	return _c.mutation
}

// Save creates the Material in the database.
func (_c *MaterialCreate) Save(ctx context.Context) (*Material, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *MaterialCreate) SaveX(ctx context.Context) *Material {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *MaterialCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *MaterialCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *MaterialCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := material.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := material.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *MaterialCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "Material.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "Material.updated_time"`)}
	}
	if _, ok := _c.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`db1: missing required field "Material.title"`)}
	}
	if _, ok := _c.mutation.SourceURL(); !ok {
		return &ValidationError{Name: "source_url", err: errors.New(`db1: missing required field "Material.source_url"`)}
	}
	if _, ok := _c.mutation.SourceTemuID(); !ok {
		return &ValidationError{Name: "source_temu_id", err: errors.New(`db1: missing required field "Material.source_temu_id"`)}
	}
	if _, ok := _c.mutation.Hash(); !ok {
		return &ValidationError{Name: "hash", err: errors.New(`db1: missing required field "Material.hash"`)}
	}
	if _, ok := _c.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`db1: missing required field "Material.path"`)}
	}
	return nil
}

func (_c *MaterialCreate) sqlSave(ctx context.Context) (*Material, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *MaterialCreate) createSpec() (*Material, *sqlgraph.CreateSpec) {
	var (
		_node = &Material{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(material.Table, sqlgraph.NewFieldSpec(material.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(material.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(material.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.Title(); ok {
		_spec.SetField(material.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := _c.mutation.SourceURL(); ok {
		_spec.SetField(material.FieldSourceURL, field.TypeString, value)
		_node.SourceURL = value
	}
	if value, ok := _c.mutation.MaterialGroup(); ok {
		_spec.SetField(material.FieldMaterialGroup, field.TypeOther, value)
		_node.MaterialGroup = value
	}
	if value, ok := _c.mutation.Flag(); ok {
		_spec.SetField(material.FieldFlag, field.TypeOther, value)
		_node.Flag = value
	}
	if value, ok := _c.mutation.SourceTemuID(); ok {
		_spec.SetField(material.FieldSourceTemuID, field.TypeInt64, value)
		_node.SourceTemuID = value
	}
	if value, ok := _c.mutation.Hash(); ok {
		_spec.SetField(material.FieldHash, field.TypeString, value)
		_node.Hash = value
	}
	if value, ok := _c.mutation.Path(); ok {
		_spec.SetField(material.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Material.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MaterialUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *MaterialCreate) OnConflict(opts ...sql.ConflictOption) *MaterialUpsertOne {
	_c.conflict = opts
	return &MaterialUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Material.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *MaterialCreate) OnConflictColumns(columns ...string) *MaterialUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &MaterialUpsertOne{
		create: _c,
	}
}

type (
	// MaterialUpsertOne is the builder for "upsert"-ing
	//  one Material node.
	MaterialUpsertOne struct {
		create *MaterialCreate
	}

	// MaterialUpsert is the "OnConflict" setter.
	MaterialUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *MaterialUpsert) SetUpdatedTime(v time.Time) *MaterialUpsert {
	u.Set(material.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *MaterialUpsert) UpdateUpdatedTime() *MaterialUpsert {
	u.SetExcluded(material.FieldUpdatedTime)
	return u
}

// SetTitle sets the "title" field.
func (u *MaterialUpsert) SetTitle(v string) *MaterialUpsert {
	u.Set(material.FieldTitle, v)
	return u
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *MaterialUpsert) UpdateTitle() *MaterialUpsert {
	u.SetExcluded(material.FieldTitle)
	return u
}

// SetSourceURL sets the "source_url" field.
func (u *MaterialUpsert) SetSourceURL(v string) *MaterialUpsert {
	u.Set(material.FieldSourceURL, v)
	return u
}

// UpdateSourceURL sets the "source_url" field to the value that was provided on create.
func (u *MaterialUpsert) UpdateSourceURL() *MaterialUpsert {
	u.SetExcluded(material.FieldSourceURL)
	return u
}

// SetMaterialGroup sets the "material_group" field.
func (u *MaterialUpsert) SetMaterialGroup(v pq.Int64Array) *MaterialUpsert {
	u.Set(material.FieldMaterialGroup, v)
	return u
}

// UpdateMaterialGroup sets the "material_group" field to the value that was provided on create.
func (u *MaterialUpsert) UpdateMaterialGroup() *MaterialUpsert {
	u.SetExcluded(material.FieldMaterialGroup)
	return u
}

// ClearMaterialGroup clears the value of the "material_group" field.
func (u *MaterialUpsert) ClearMaterialGroup() *MaterialUpsert {
	u.SetNull(material.FieldMaterialGroup)
	return u
}

// SetFlag sets the "flag" field.
func (u *MaterialUpsert) SetFlag(v pq.StringArray) *MaterialUpsert {
	u.Set(material.FieldFlag, v)
	return u
}

// UpdateFlag sets the "flag" field to the value that was provided on create.
func (u *MaterialUpsert) UpdateFlag() *MaterialUpsert {
	u.SetExcluded(material.FieldFlag)
	return u
}

// ClearFlag clears the value of the "flag" field.
func (u *MaterialUpsert) ClearFlag() *MaterialUpsert {
	u.SetNull(material.FieldFlag)
	return u
}

// SetSourceTemuID sets the "source_temu_id" field.
func (u *MaterialUpsert) SetSourceTemuID(v int64) *MaterialUpsert {
	u.Set(material.FieldSourceTemuID, v)
	return u
}

// UpdateSourceTemuID sets the "source_temu_id" field to the value that was provided on create.
func (u *MaterialUpsert) UpdateSourceTemuID() *MaterialUpsert {
	u.SetExcluded(material.FieldSourceTemuID)
	return u
}

// AddSourceTemuID adds v to the "source_temu_id" field.
func (u *MaterialUpsert) AddSourceTemuID(v int64) *MaterialUpsert {
	u.Add(material.FieldSourceTemuID, v)
	return u
}

// SetHash sets the "hash" field.
func (u *MaterialUpsert) SetHash(v string) *MaterialUpsert {
	u.Set(material.FieldHash, v)
	return u
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *MaterialUpsert) UpdateHash() *MaterialUpsert {
	u.SetExcluded(material.FieldHash)
	return u
}

// SetPath sets the "path" field.
func (u *MaterialUpsert) SetPath(v string) *MaterialUpsert {
	u.Set(material.FieldPath, v)
	return u
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *MaterialUpsert) UpdatePath() *MaterialUpsert {
	u.SetExcluded(material.FieldPath)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Material.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(material.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MaterialUpsertOne) UpdateNewValues() *MaterialUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(material.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(material.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Material.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *MaterialUpsertOne) Ignore() *MaterialUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MaterialUpsertOne) DoNothing() *MaterialUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MaterialCreate.OnConflict
// documentation for more info.
func (u *MaterialUpsertOne) Update(set func(*MaterialUpsert)) *MaterialUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MaterialUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *MaterialUpsertOne) SetUpdatedTime(v time.Time) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdateUpdatedTime() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetTitle sets the "title" field.
func (u *MaterialUpsertOne) SetTitle(v string) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdateTitle() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateTitle()
	})
}

// SetSourceURL sets the "source_url" field.
func (u *MaterialUpsertOne) SetSourceURL(v string) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetSourceURL(v)
	})
}

// UpdateSourceURL sets the "source_url" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdateSourceURL() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateSourceURL()
	})
}

// SetMaterialGroup sets the "material_group" field.
func (u *MaterialUpsertOne) SetMaterialGroup(v pq.Int64Array) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetMaterialGroup(v)
	})
}

// UpdateMaterialGroup sets the "material_group" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdateMaterialGroup() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateMaterialGroup()
	})
}

// ClearMaterialGroup clears the value of the "material_group" field.
func (u *MaterialUpsertOne) ClearMaterialGroup() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.ClearMaterialGroup()
	})
}

// SetFlag sets the "flag" field.
func (u *MaterialUpsertOne) SetFlag(v pq.StringArray) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetFlag(v)
	})
}

// UpdateFlag sets the "flag" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdateFlag() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateFlag()
	})
}

// ClearFlag clears the value of the "flag" field.
func (u *MaterialUpsertOne) ClearFlag() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.ClearFlag()
	})
}

// SetSourceTemuID sets the "source_temu_id" field.
func (u *MaterialUpsertOne) SetSourceTemuID(v int64) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetSourceTemuID(v)
	})
}

// AddSourceTemuID adds v to the "source_temu_id" field.
func (u *MaterialUpsertOne) AddSourceTemuID(v int64) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.AddSourceTemuID(v)
	})
}

// UpdateSourceTemuID sets the "source_temu_id" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdateSourceTemuID() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateSourceTemuID()
	})
}

// SetHash sets the "hash" field.
func (u *MaterialUpsertOne) SetHash(v string) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetHash(v)
	})
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdateHash() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateHash()
	})
}

// SetPath sets the "path" field.
func (u *MaterialUpsertOne) SetPath(v string) *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *MaterialUpsertOne) UpdatePath() *MaterialUpsertOne {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdatePath()
	})
}

// Exec executes the query.
func (u *MaterialUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for MaterialCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MaterialUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *MaterialUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *MaterialUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// MaterialCreateBulk is the builder for creating many Material entities in bulk.
type MaterialCreateBulk struct {
	config
	err      error
	builders []*MaterialCreate
	conflict []sql.ConflictOption
}

// Save creates the Material entities in the database.
func (_c *MaterialCreateBulk) Save(ctx context.Context) ([]*Material, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Material, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MaterialMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *MaterialCreateBulk) SaveX(ctx context.Context) []*Material {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *MaterialCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *MaterialCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Material.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MaterialUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *MaterialCreateBulk) OnConflict(opts ...sql.ConflictOption) *MaterialUpsertBulk {
	_c.conflict = opts
	return &MaterialUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Material.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *MaterialCreateBulk) OnConflictColumns(columns ...string) *MaterialUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &MaterialUpsertBulk{
		create: _c,
	}
}

// MaterialUpsertBulk is the builder for "upsert"-ing
// a bulk of Material nodes.
type MaterialUpsertBulk struct {
	create *MaterialCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Material.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(material.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MaterialUpsertBulk) UpdateNewValues() *MaterialUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(material.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(material.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Material.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *MaterialUpsertBulk) Ignore() *MaterialUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MaterialUpsertBulk) DoNothing() *MaterialUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MaterialCreateBulk.OnConflict
// documentation for more info.
func (u *MaterialUpsertBulk) Update(set func(*MaterialUpsert)) *MaterialUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MaterialUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *MaterialUpsertBulk) SetUpdatedTime(v time.Time) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdateUpdatedTime() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetTitle sets the "title" field.
func (u *MaterialUpsertBulk) SetTitle(v string) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdateTitle() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateTitle()
	})
}

// SetSourceURL sets the "source_url" field.
func (u *MaterialUpsertBulk) SetSourceURL(v string) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetSourceURL(v)
	})
}

// UpdateSourceURL sets the "source_url" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdateSourceURL() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateSourceURL()
	})
}

// SetMaterialGroup sets the "material_group" field.
func (u *MaterialUpsertBulk) SetMaterialGroup(v pq.Int64Array) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetMaterialGroup(v)
	})
}

// UpdateMaterialGroup sets the "material_group" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdateMaterialGroup() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateMaterialGroup()
	})
}

// ClearMaterialGroup clears the value of the "material_group" field.
func (u *MaterialUpsertBulk) ClearMaterialGroup() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.ClearMaterialGroup()
	})
}

// SetFlag sets the "flag" field.
func (u *MaterialUpsertBulk) SetFlag(v pq.StringArray) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetFlag(v)
	})
}

// UpdateFlag sets the "flag" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdateFlag() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateFlag()
	})
}

// ClearFlag clears the value of the "flag" field.
func (u *MaterialUpsertBulk) ClearFlag() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.ClearFlag()
	})
}

// SetSourceTemuID sets the "source_temu_id" field.
func (u *MaterialUpsertBulk) SetSourceTemuID(v int64) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetSourceTemuID(v)
	})
}

// AddSourceTemuID adds v to the "source_temu_id" field.
func (u *MaterialUpsertBulk) AddSourceTemuID(v int64) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.AddSourceTemuID(v)
	})
}

// UpdateSourceTemuID sets the "source_temu_id" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdateSourceTemuID() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateSourceTemuID()
	})
}

// SetHash sets the "hash" field.
func (u *MaterialUpsertBulk) SetHash(v string) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetHash(v)
	})
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdateHash() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdateHash()
	})
}

// SetPath sets the "path" field.
func (u *MaterialUpsertBulk) SetPath(v string) *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *MaterialUpsertBulk) UpdatePath() *MaterialUpsertBulk {
	return u.Update(func(s *MaterialUpsert) {
		s.UpdatePath()
	})
}

// Exec executes the query.
func (u *MaterialUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the MaterialCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for MaterialCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MaterialUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
