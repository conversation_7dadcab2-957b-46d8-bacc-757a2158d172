// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/material"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// MaterialDelete is the builder for deleting a Material entity.
type MaterialDelete struct {
	config
	hooks    []Hook
	mutation *MaterialMutation
}

// Where appends a list predicates to the MaterialDelete builder.
func (_d *MaterialDelete) Where(ps ...predicate.Material) *MaterialDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *MaterialDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *MaterialDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *MaterialDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(material.Table, sqlgraph.NewFieldSpec(material.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereCreatedTime(v time.Time) *MaterialDelete {
	_d.Where(material.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereUpdatedTime(v time.Time) *MaterialDelete {
	_d.Where(material.UpdatedTime(v))
	return _d
}

// WhereTitle applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereTitle(v string) *MaterialDelete {
	_d.Where(material.Title(v))
	return _d
}

// WhereSourceURL applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereSourceURL(v string) *MaterialDelete {
	_d.Where(material.SourceURL(v))
	return _d
}

// WhereMaterialGroup applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereMaterialGroup(v pq.Int64Array) *MaterialDelete {
	_d.Where(material.MaterialGroup(v))
	return _d
}

// WhereFlag applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereFlag(v pq.StringArray) *MaterialDelete {
	_d.Where(material.Flag(v))
	return _d
}

// WhereSourceTemuID applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereSourceTemuID(v int64) *MaterialDelete {
	_d.Where(material.SourceTemuID(v))
	return _d
}

// WhereHash applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WhereHash(v string) *MaterialDelete {
	_d.Where(material.Hash(v))
	return _d
}

// WherePath applies equality check predicate to the MaterialDelete builder.
func (_d *MaterialDelete) WherePath(v string) *MaterialDelete {
	_d.Where(material.Path(v))
	return _d
}

// MaterialDeleteOne is the builder for deleting a single Material entity.
type MaterialDeleteOne struct {
	_d *MaterialDelete
}

// Where appends a list predicates to the MaterialDelete builder.
func (_d *MaterialDeleteOne) Where(ps ...predicate.Material) *MaterialDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *MaterialDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{material.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *MaterialDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
