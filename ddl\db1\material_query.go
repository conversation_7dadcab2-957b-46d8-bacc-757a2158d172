// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"fmt"
	"math"
	"omnix/ddl/db1/material"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// MaterialQuery is the builder for querying Material entities.
type MaterialQuery struct {
	config
	ctx        *QueryContext
	order      []material.OrderOption
	inters     []Interceptor
	predicates []predicate.Material
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the MaterialQuery builder.
func (_q *MaterialQuery) Where(ps ...predicate.Material) *MaterialQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *MaterialQuery) Limit(limit int) *MaterialQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *MaterialQuery) Offset(offset int) *MaterialQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *MaterialQuery) Unique(unique bool) *MaterialQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *MaterialQuery) Order(o ...material.OrderOption) *MaterialQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// First returns the first Material entity from the query.
// Returns a *NotFoundError when no Material was found.
func (_q *MaterialQuery) First(ctx context.Context) (*Material, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{material.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *MaterialQuery) FirstX(ctx context.Context) *Material {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Material ID from the query.
// Returns a *NotFoundError when no Material ID was found.
func (_q *MaterialQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{material.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *MaterialQuery) FirstIDX(ctx context.Context) int64 {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Material entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Material entity is found.
// Returns a *NotFoundError when no Material entities are found.
func (_q *MaterialQuery) Only(ctx context.Context) (*Material, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{material.Label}
	default:
		return nil, &NotSingularError{material.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *MaterialQuery) OnlyX(ctx context.Context) *Material {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Material ID in the query.
// Returns a *NotSingularError when more than one Material ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *MaterialQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{material.Label}
	default:
		err = &NotSingularError{material.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *MaterialQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Materials.
func (_q *MaterialQuery) All(ctx context.Context) ([]*Material, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Material, *MaterialQuery]()
	return withInterceptors[[]*Material](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *MaterialQuery) AllX(ctx context.Context) []*Material {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Material IDs.
func (_q *MaterialQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(material.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *MaterialQuery) IDsX(ctx context.Context) []int64 {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *MaterialQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*MaterialQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *MaterialQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *MaterialQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("db1: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *MaterialQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the MaterialQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *MaterialQuery) Clone() *MaterialQuery {
	if _q == nil {
		return nil
	}
	return &MaterialQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]material.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.Material{}, _q.predicates...),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Material.Query().
//		GroupBy(material.FieldCreatedTime).
//		Aggregate(db1.Count()).
//		Scan(ctx, &v)
func (_q *MaterialQuery) GroupBy(field string, fields ...string) *MaterialGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &MaterialGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = material.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//	}
//
//	client.Material.Query().
//		Select(material.FieldCreatedTime).
//		Scan(ctx, &v)
func (_q *MaterialQuery) Select(fields ...string) *MaterialSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &MaterialSelect{MaterialQuery: _q}
	sbuild.label = material.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a MaterialSelect configured with the given aggregations.
func (_q *MaterialQuery) Aggregate(fns ...AggregateFunc) *MaterialSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *MaterialQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("db1: uninitialized interceptor (forgotten import db1/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !material.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *MaterialQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Material, error) {
	var (
		nodes = []*Material{}
		_spec = _q.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Material).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Material{config: _q.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (_q *MaterialQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *MaterialQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(material.Table, material.Columns, sqlgraph.NewFieldSpec(material.FieldID, field.TypeInt64))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, material.FieldID)
		for i := range fields {
			if fields[i] != material.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *MaterialQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(material.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = material.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WhereCreatedTime applies equality check predicate on the "created_time" field
func (_m *MaterialQuery) WhereCreatedTime(v time.Time) *MaterialQuery {
	_m.Where(material.CreatedTime(v))
	return _m
}

// WhereUpdatedTime applies equality check predicate on the "updated_time" field
func (_m *MaterialQuery) WhereUpdatedTime(v time.Time) *MaterialQuery {
	_m.Where(material.UpdatedTime(v))
	return _m
}

// WhereTitle applies equality check predicate on the "title" field
func (_m *MaterialQuery) WhereTitle(v string) *MaterialQuery {
	_m.Where(material.Title(v))
	return _m
}

// WhereSourceURL applies equality check predicate on the "source_url" field
func (_m *MaterialQuery) WhereSourceURL(v string) *MaterialQuery {
	_m.Where(material.SourceURL(v))
	return _m
}

// WhereMaterialGroup applies equality check predicate on the "material_group" field
func (_m *MaterialQuery) WhereMaterialGroup(v pq.Int64Array) *MaterialQuery {
	_m.Where(material.MaterialGroup(v))
	return _m
}

// WhereFlag applies equality check predicate on the "flag" field
func (_m *MaterialQuery) WhereFlag(v pq.StringArray) *MaterialQuery {
	_m.Where(material.Flag(v))
	return _m
}

// WhereSourceTemuID applies equality check predicate on the "source_temu_id" field
func (_m *MaterialQuery) WhereSourceTemuID(v int64) *MaterialQuery {
	_m.Where(material.SourceTemuID(v))
	return _m
}

// WhereHash applies equality check predicate on the "hash" field
func (_m *MaterialQuery) WhereHash(v string) *MaterialQuery {
	_m.Where(material.Hash(v))
	return _m
}

// WherePath applies equality check predicate on the "path" field
func (_m *MaterialQuery) WherePath(v string) *MaterialQuery {
	_m.Where(material.Path(v))
	return _m
}

// MaterialGroupBy is the group-by builder for Material entities.
type MaterialGroupBy struct {
	selector
	build *MaterialQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *MaterialGroupBy) Aggregate(fns ...AggregateFunc) *MaterialGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *MaterialGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MaterialQuery, *MaterialGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *MaterialGroupBy) sqlScan(ctx context.Context, root *MaterialQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// MaterialSelect is the builder for selecting fields of Material entities.
type MaterialSelect struct {
	*MaterialQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *MaterialSelect) Aggregate(fns ...AggregateFunc) *MaterialSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *MaterialSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MaterialQuery, *MaterialSelect](ctx, _s.MaterialQuery, _s, _s.inters, v)
}

func (_s *MaterialSelect) sqlScan(ctx context.Context, root *MaterialQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
