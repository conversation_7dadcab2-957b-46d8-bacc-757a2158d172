// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/material"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// MaterialUpdate is the builder for updating Material entities.
type MaterialUpdate struct {
	config
	hooks    []Hook
	mutation *MaterialMutation
}

// Where appends a list predicates to the MaterialUpdate builder.
func (_u *MaterialUpdate) Where(ps ...predicate.Material) *MaterialUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *MaterialUpdate) SetUpdatedTime(v time.Time) *MaterialUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetTitle sets the "title" field.
func (_u *MaterialUpdate) SetTitle(v string) *MaterialUpdate {
	_u.mutation.SetTitle(v)
	return _u
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (_u *MaterialUpdate) SetNillableTitle(v *string) *MaterialUpdate {
	if v != nil {
		_u.SetTitle(*v)
	}
	return _u
}

// SetSourceURL sets the "source_url" field.
func (_u *MaterialUpdate) SetSourceURL(v string) *MaterialUpdate {
	_u.mutation.SetSourceURL(v)
	return _u
}

// SetNillableSourceURL sets the "source_url" field if the given value is not nil.
func (_u *MaterialUpdate) SetNillableSourceURL(v *string) *MaterialUpdate {
	if v != nil {
		_u.SetSourceURL(*v)
	}
	return _u
}

// SetMaterialGroup sets the "material_group" field.
func (_u *MaterialUpdate) SetMaterialGroup(v pq.Int64Array) *MaterialUpdate {
	_u.mutation.SetMaterialGroup(v)
	return _u
}

// ClearMaterialGroup clears the value of the "material_group" field.
func (_u *MaterialUpdate) ClearMaterialGroup() *MaterialUpdate {
	_u.mutation.ClearMaterialGroup()
	return _u
}

// SetFlag sets the "flag" field.
func (_u *MaterialUpdate) SetFlag(v pq.StringArray) *MaterialUpdate {
	_u.mutation.SetFlag(v)
	return _u
}

// ClearFlag clears the value of the "flag" field.
func (_u *MaterialUpdate) ClearFlag() *MaterialUpdate {
	_u.mutation.ClearFlag()
	return _u
}

// SetSourceTemuID sets the "source_temu_id" field.
func (_u *MaterialUpdate) SetSourceTemuID(v int64) *MaterialUpdate {
	_u.mutation.ResetSourceTemuID()
	_u.mutation.SetSourceTemuID(v)
	return _u
}

// SetNillableSourceTemuID sets the "source_temu_id" field if the given value is not nil.
func (_u *MaterialUpdate) SetNillableSourceTemuID(v *int64) *MaterialUpdate {
	if v != nil {
		_u.SetSourceTemuID(*v)
	}
	return _u
}

// AddSourceTemuID adds value to the "source_temu_id" field.
func (_u *MaterialUpdate) AddSourceTemuID(v int64) *MaterialUpdate {
	_u.mutation.AddSourceTemuID(v)
	return _u
}

// SetHash sets the "hash" field.
func (_u *MaterialUpdate) SetHash(v string) *MaterialUpdate {
	_u.mutation.SetHash(v)
	return _u
}

// SetNillableHash sets the "hash" field if the given value is not nil.
func (_u *MaterialUpdate) SetNillableHash(v *string) *MaterialUpdate {
	if v != nil {
		_u.SetHash(*v)
	}
	return _u
}

// SetPath sets the "path" field.
func (_u *MaterialUpdate) SetPath(v string) *MaterialUpdate {
	_u.mutation.SetPath(v)
	return _u
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (_u *MaterialUpdate) SetNillablePath(v *string) *MaterialUpdate {
	if v != nil {
		_u.SetPath(*v)
	}
	return _u
}

// Mutation returns the MaterialMutation object of the builder.
func (_u *MaterialUpdate) Mutation() *MaterialMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *MaterialUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *MaterialUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *MaterialUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *MaterialUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *MaterialUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := material.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

func (_u *MaterialUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(material.Table, material.Columns, sqlgraph.NewFieldSpec(material.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(material.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Title(); ok {
		_spec.SetField(material.FieldTitle, field.TypeString, value)
	}
	if value, ok := _u.mutation.SourceURL(); ok {
		_spec.SetField(material.FieldSourceURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.MaterialGroup(); ok {
		_spec.SetField(material.FieldMaterialGroup, field.TypeOther, value)
	}
	if _u.mutation.MaterialGroupCleared() {
		_spec.ClearField(material.FieldMaterialGroup, field.TypeOther)
	}
	if value, ok := _u.mutation.Flag(); ok {
		_spec.SetField(material.FieldFlag, field.TypeOther, value)
	}
	if _u.mutation.FlagCleared() {
		_spec.ClearField(material.FieldFlag, field.TypeOther)
	}
	if value, ok := _u.mutation.SourceTemuID(); ok {
		_spec.SetField(material.FieldSourceTemuID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedSourceTemuID(); ok {
		_spec.AddField(material.FieldSourceTemuID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.Hash(); ok {
		_spec.SetField(material.FieldHash, field.TypeString, value)
	}
	if value, ok := _u.mutation.Path(); ok {
		_spec.SetField(material.FieldPath, field.TypeString, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{material.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// MaterialUpdateOne is the builder for updating a single Material entity.
type MaterialUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *MaterialMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *MaterialUpdateOne) SetUpdatedTime(v time.Time) *MaterialUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetTitle sets the "title" field.
func (_u *MaterialUpdateOne) SetTitle(v string) *MaterialUpdateOne {
	_u.mutation.SetTitle(v)
	return _u
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (_u *MaterialUpdateOne) SetNillableTitle(v *string) *MaterialUpdateOne {
	if v != nil {
		_u.SetTitle(*v)
	}
	return _u
}

// SetSourceURL sets the "source_url" field.
func (_u *MaterialUpdateOne) SetSourceURL(v string) *MaterialUpdateOne {
	_u.mutation.SetSourceURL(v)
	return _u
}

// SetNillableSourceURL sets the "source_url" field if the given value is not nil.
func (_u *MaterialUpdateOne) SetNillableSourceURL(v *string) *MaterialUpdateOne {
	if v != nil {
		_u.SetSourceURL(*v)
	}
	return _u
}

// SetMaterialGroup sets the "material_group" field.
func (_u *MaterialUpdateOne) SetMaterialGroup(v pq.Int64Array) *MaterialUpdateOne {
	_u.mutation.SetMaterialGroup(v)
	return _u
}

// ClearMaterialGroup clears the value of the "material_group" field.
func (_u *MaterialUpdateOne) ClearMaterialGroup() *MaterialUpdateOne {
	_u.mutation.ClearMaterialGroup()
	return _u
}

// SetFlag sets the "flag" field.
func (_u *MaterialUpdateOne) SetFlag(v pq.StringArray) *MaterialUpdateOne {
	_u.mutation.SetFlag(v)
	return _u
}

// ClearFlag clears the value of the "flag" field.
func (_u *MaterialUpdateOne) ClearFlag() *MaterialUpdateOne {
	_u.mutation.ClearFlag()
	return _u
}

// SetSourceTemuID sets the "source_temu_id" field.
func (_u *MaterialUpdateOne) SetSourceTemuID(v int64) *MaterialUpdateOne {
	_u.mutation.ResetSourceTemuID()
	_u.mutation.SetSourceTemuID(v)
	return _u
}

// SetNillableSourceTemuID sets the "source_temu_id" field if the given value is not nil.
func (_u *MaterialUpdateOne) SetNillableSourceTemuID(v *int64) *MaterialUpdateOne {
	if v != nil {
		_u.SetSourceTemuID(*v)
	}
	return _u
}

// AddSourceTemuID adds value to the "source_temu_id" field.
func (_u *MaterialUpdateOne) AddSourceTemuID(v int64) *MaterialUpdateOne {
	_u.mutation.AddSourceTemuID(v)
	return _u
}

// SetHash sets the "hash" field.
func (_u *MaterialUpdateOne) SetHash(v string) *MaterialUpdateOne {
	_u.mutation.SetHash(v)
	return _u
}

// SetNillableHash sets the "hash" field if the given value is not nil.
func (_u *MaterialUpdateOne) SetNillableHash(v *string) *MaterialUpdateOne {
	if v != nil {
		_u.SetHash(*v)
	}
	return _u
}

// SetPath sets the "path" field.
func (_u *MaterialUpdateOne) SetPath(v string) *MaterialUpdateOne {
	_u.mutation.SetPath(v)
	return _u
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (_u *MaterialUpdateOne) SetNillablePath(v *string) *MaterialUpdateOne {
	if v != nil {
		_u.SetPath(*v)
	}
	return _u
}

// Mutation returns the MaterialMutation object of the builder.
func (_u *MaterialUpdateOne) Mutation() *MaterialMutation {
	return _u.mutation
}

// Where appends a list predicates to the MaterialUpdate builder.
func (_u *MaterialUpdateOne) Where(ps ...predicate.Material) *MaterialUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *MaterialUpdateOne) Select(field string, fields ...string) *MaterialUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Material entity.
func (_u *MaterialUpdateOne) Save(ctx context.Context) (*Material, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *MaterialUpdateOne) SaveX(ctx context.Context) *Material {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *MaterialUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *MaterialUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *MaterialUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := material.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

func (_u *MaterialUpdateOne) sqlSave(ctx context.Context) (_node *Material, err error) {
	_spec := sqlgraph.NewUpdateSpec(material.Table, material.Columns, sqlgraph.NewFieldSpec(material.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "Material.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, material.FieldID)
		for _, f := range fields {
			if !material.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != material.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(material.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Title(); ok {
		_spec.SetField(material.FieldTitle, field.TypeString, value)
	}
	if value, ok := _u.mutation.SourceURL(); ok {
		_spec.SetField(material.FieldSourceURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.MaterialGroup(); ok {
		_spec.SetField(material.FieldMaterialGroup, field.TypeOther, value)
	}
	if _u.mutation.MaterialGroupCleared() {
		_spec.ClearField(material.FieldMaterialGroup, field.TypeOther)
	}
	if value, ok := _u.mutation.Flag(); ok {
		_spec.SetField(material.FieldFlag, field.TypeOther, value)
	}
	if _u.mutation.FlagCleared() {
		_spec.ClearField(material.FieldFlag, field.TypeOther)
	}
	if value, ok := _u.mutation.SourceTemuID(); ok {
		_spec.SetField(material.FieldSourceTemuID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedSourceTemuID(); ok {
		_spec.AddField(material.FieldSourceTemuID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.Hash(); ok {
		_spec.SetField(material.FieldHash, field.TypeString, value)
	}
	if value, ok := _u.mutation.Path(); ok {
		_spec.SetField(material.FieldPath, field.TypeString, value)
	}
	_node = &Material{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{material.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereCreatedTime(v time.Time) *MaterialUpdate {
	_u.Where(material.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereUpdatedTime(v time.Time) *MaterialUpdate {
	_u.Where(material.UpdatedTime(v))
	return _u
}

// WhereTitle applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereTitle(v string) *MaterialUpdate {
	_u.Where(material.Title(v))
	return _u
}

// WhereSourceURL applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereSourceURL(v string) *MaterialUpdate {
	_u.Where(material.SourceURL(v))
	return _u
}

// WhereMaterialGroup applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereMaterialGroup(v pq.Int64Array) *MaterialUpdate {
	_u.Where(material.MaterialGroup(v))
	return _u
}

// WhereFlag applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereFlag(v pq.StringArray) *MaterialUpdate {
	_u.Where(material.Flag(v))
	return _u
}

// WhereSourceTemuID applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereSourceTemuID(v int64) *MaterialUpdate {
	_u.Where(material.SourceTemuID(v))
	return _u
}

// WhereHash applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WhereHash(v string) *MaterialUpdate {
	_u.Where(material.Hash(v))
	return _u
}

// WherePath applies equality check predicate to the MaterialUpdate builder.
func (_u *MaterialUpdate) WherePath(v string) *MaterialUpdate {
	_u.Where(material.Path(v))
	return _u
}
