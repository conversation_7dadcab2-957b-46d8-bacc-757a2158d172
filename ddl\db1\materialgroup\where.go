// Code generated by ent, DO NOT EDIT.

package materialgroup

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldUpdatedTime, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldName, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLTE(FieldUpdatedTime, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.FieldContainsFold(FieldName, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.MaterialGroup) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.MaterialGroup) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.MaterialGroup) predicate.MaterialGroup {
	return predicate.MaterialGroup(sql.NotPredicates(p))
}
