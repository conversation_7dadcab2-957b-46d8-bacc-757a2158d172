// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/materialgroup"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaterialGroupCreate is the builder for creating a MaterialGroup entity.
type MaterialGroupCreate struct {
	config
	mutation *MaterialGroupMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *MaterialGroupCreate) SetCreatedTime(v time.Time) *MaterialGroupCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *MaterialGroupCreate) SetNillableCreatedTime(v *time.Time) *MaterialGroupCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *MaterialGroupCreate) SetUpdatedTime(v time.Time) *MaterialGroupCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *MaterialGroupCreate) SetNillableUpdatedTime(v *time.Time) *MaterialGroupCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetName sets the "name" field.
func (_c *MaterialGroupCreate) SetName(v string) *MaterialGroupCreate {
	_c.mutation.SetName(v)
	return _c
}

// SetID sets the "id" field.
func (_c *MaterialGroupCreate) SetID(v int64) *MaterialGroupCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the MaterialGroupMutation object of the builder.
func (_c *MaterialGroupCreate) Mutation() *MaterialGroupMutation {
	return _c.mutation
}

// Save creates the MaterialGroup in the database.
func (_c *MaterialGroupCreate) Save(ctx context.Context) (*MaterialGroup, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *MaterialGroupCreate) SaveX(ctx context.Context) *MaterialGroup {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *MaterialGroupCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *MaterialGroupCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *MaterialGroupCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := materialgroup.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := materialgroup.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *MaterialGroupCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "MaterialGroup.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "MaterialGroup.updated_time"`)}
	}
	if _, ok := _c.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`db1: missing required field "MaterialGroup.name"`)}
	}
	return nil
}

func (_c *MaterialGroupCreate) sqlSave(ctx context.Context) (*MaterialGroup, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *MaterialGroupCreate) createSpec() (*MaterialGroup, *sqlgraph.CreateSpec) {
	var (
		_node = &MaterialGroup{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(materialgroup.Table, sqlgraph.NewFieldSpec(materialgroup.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(materialgroup.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(materialgroup.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.Name(); ok {
		_spec.SetField(materialgroup.FieldName, field.TypeString, value)
		_node.Name = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.MaterialGroup.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MaterialGroupUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *MaterialGroupCreate) OnConflict(opts ...sql.ConflictOption) *MaterialGroupUpsertOne {
	_c.conflict = opts
	return &MaterialGroupUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.MaterialGroup.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *MaterialGroupCreate) OnConflictColumns(columns ...string) *MaterialGroupUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &MaterialGroupUpsertOne{
		create: _c,
	}
}

type (
	// MaterialGroupUpsertOne is the builder for "upsert"-ing
	//  one MaterialGroup node.
	MaterialGroupUpsertOne struct {
		create *MaterialGroupCreate
	}

	// MaterialGroupUpsert is the "OnConflict" setter.
	MaterialGroupUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *MaterialGroupUpsert) SetUpdatedTime(v time.Time) *MaterialGroupUpsert {
	u.Set(materialgroup.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *MaterialGroupUpsert) UpdateUpdatedTime() *MaterialGroupUpsert {
	u.SetExcluded(materialgroup.FieldUpdatedTime)
	return u
}

// SetName sets the "name" field.
func (u *MaterialGroupUpsert) SetName(v string) *MaterialGroupUpsert {
	u.Set(materialgroup.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MaterialGroupUpsert) UpdateName() *MaterialGroupUpsert {
	u.SetExcluded(materialgroup.FieldName)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.MaterialGroup.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(materialgroup.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MaterialGroupUpsertOne) UpdateNewValues() *MaterialGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(materialgroup.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(materialgroup.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.MaterialGroup.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *MaterialGroupUpsertOne) Ignore() *MaterialGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MaterialGroupUpsertOne) DoNothing() *MaterialGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MaterialGroupCreate.OnConflict
// documentation for more info.
func (u *MaterialGroupUpsertOne) Update(set func(*MaterialGroupUpsert)) *MaterialGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MaterialGroupUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *MaterialGroupUpsertOne) SetUpdatedTime(v time.Time) *MaterialGroupUpsertOne {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *MaterialGroupUpsertOne) UpdateUpdatedTime() *MaterialGroupUpsertOne {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetName sets the "name" field.
func (u *MaterialGroupUpsertOne) SetName(v string) *MaterialGroupUpsertOne {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MaterialGroupUpsertOne) UpdateName() *MaterialGroupUpsertOne {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.UpdateName()
	})
}

// Exec executes the query.
func (u *MaterialGroupUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for MaterialGroupCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MaterialGroupUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *MaterialGroupUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *MaterialGroupUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// MaterialGroupCreateBulk is the builder for creating many MaterialGroup entities in bulk.
type MaterialGroupCreateBulk struct {
	config
	err      error
	builders []*MaterialGroupCreate
	conflict []sql.ConflictOption
}

// Save creates the MaterialGroup entities in the database.
func (_c *MaterialGroupCreateBulk) Save(ctx context.Context) ([]*MaterialGroup, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*MaterialGroup, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MaterialGroupMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *MaterialGroupCreateBulk) SaveX(ctx context.Context) []*MaterialGroup {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *MaterialGroupCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *MaterialGroupCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.MaterialGroup.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MaterialGroupUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *MaterialGroupCreateBulk) OnConflict(opts ...sql.ConflictOption) *MaterialGroupUpsertBulk {
	_c.conflict = opts
	return &MaterialGroupUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.MaterialGroup.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *MaterialGroupCreateBulk) OnConflictColumns(columns ...string) *MaterialGroupUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &MaterialGroupUpsertBulk{
		create: _c,
	}
}

// MaterialGroupUpsertBulk is the builder for "upsert"-ing
// a bulk of MaterialGroup nodes.
type MaterialGroupUpsertBulk struct {
	create *MaterialGroupCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.MaterialGroup.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(materialgroup.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MaterialGroupUpsertBulk) UpdateNewValues() *MaterialGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(materialgroup.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(materialgroup.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.MaterialGroup.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *MaterialGroupUpsertBulk) Ignore() *MaterialGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MaterialGroupUpsertBulk) DoNothing() *MaterialGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MaterialGroupCreateBulk.OnConflict
// documentation for more info.
func (u *MaterialGroupUpsertBulk) Update(set func(*MaterialGroupUpsert)) *MaterialGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MaterialGroupUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *MaterialGroupUpsertBulk) SetUpdatedTime(v time.Time) *MaterialGroupUpsertBulk {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *MaterialGroupUpsertBulk) UpdateUpdatedTime() *MaterialGroupUpsertBulk {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetName sets the "name" field.
func (u *MaterialGroupUpsertBulk) SetName(v string) *MaterialGroupUpsertBulk {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MaterialGroupUpsertBulk) UpdateName() *MaterialGroupUpsertBulk {
	return u.Update(func(s *MaterialGroupUpsert) {
		s.UpdateName()
	})
}

// Exec executes the query.
func (u *MaterialGroupUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the MaterialGroupCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for MaterialGroupCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MaterialGroupUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
