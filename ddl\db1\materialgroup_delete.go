// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/materialgroup"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaterialGroupDelete is the builder for deleting a MaterialGroup entity.
type MaterialGroupDelete struct {
	config
	hooks    []Hook
	mutation *MaterialGroupMutation
}

// Where appends a list predicates to the MaterialGroupDelete builder.
func (_d *MaterialGroupDelete) Where(ps ...predicate.MaterialGroup) *MaterialGroupDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *MaterialGroupDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *MaterialGroupDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *MaterialGroupDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(materialgroup.Table, sqlgraph.NewFieldSpec(materialgroup.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the MaterialGroupDelete builder.
func (_d *MaterialGroupDelete) WhereCreatedTime(v time.Time) *MaterialGroupDelete {
	_d.Where(materialgroup.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the MaterialGroupDelete builder.
func (_d *MaterialGroupDelete) WhereUpdatedTime(v time.Time) *MaterialGroupDelete {
	_d.Where(materialgroup.UpdatedTime(v))
	return _d
}

// WhereName applies equality check predicate to the MaterialGroupDelete builder.
func (_d *MaterialGroupDelete) WhereName(v string) *MaterialGroupDelete {
	_d.Where(materialgroup.Name(v))
	return _d
}

// MaterialGroupDeleteOne is the builder for deleting a single MaterialGroup entity.
type MaterialGroupDeleteOne struct {
	_d *MaterialGroupDelete
}

// Where appends a list predicates to the MaterialGroupDelete builder.
func (_d *MaterialGroupDeleteOne) Where(ps ...predicate.MaterialGroup) *MaterialGroupDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *MaterialGroupDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{materialgroup.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *MaterialGroupDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
