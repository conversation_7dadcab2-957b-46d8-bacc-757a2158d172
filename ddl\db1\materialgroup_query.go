// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"fmt"
	"math"
	"omnix/ddl/db1/materialgroup"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaterialGroupQuery is the builder for querying MaterialGroup entities.
type MaterialGroupQuery struct {
	config
	ctx        *QueryContext
	order      []materialgroup.OrderOption
	inters     []Interceptor
	predicates []predicate.MaterialGroup
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the MaterialGroupQuery builder.
func (_q *MaterialGroupQuery) Where(ps ...predicate.MaterialGroup) *MaterialGroupQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *MaterialGroupQuery) Limit(limit int) *MaterialGroupQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *MaterialGroupQuery) Offset(offset int) *MaterialGroupQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *MaterialGroupQuery) Unique(unique bool) *MaterialGroupQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *MaterialGroupQuery) Order(o ...materialgroup.OrderOption) *MaterialGroupQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// First returns the first MaterialGroup entity from the query.
// Returns a *NotFoundError when no MaterialGroup was found.
func (_q *MaterialGroupQuery) First(ctx context.Context) (*MaterialGroup, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{materialgroup.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *MaterialGroupQuery) FirstX(ctx context.Context) *MaterialGroup {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first MaterialGroup ID from the query.
// Returns a *NotFoundError when no MaterialGroup ID was found.
func (_q *MaterialGroupQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{materialgroup.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *MaterialGroupQuery) FirstIDX(ctx context.Context) int64 {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single MaterialGroup entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one MaterialGroup entity is found.
// Returns a *NotFoundError when no MaterialGroup entities are found.
func (_q *MaterialGroupQuery) Only(ctx context.Context) (*MaterialGroup, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{materialgroup.Label}
	default:
		return nil, &NotSingularError{materialgroup.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *MaterialGroupQuery) OnlyX(ctx context.Context) *MaterialGroup {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only MaterialGroup ID in the query.
// Returns a *NotSingularError when more than one MaterialGroup ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *MaterialGroupQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{materialgroup.Label}
	default:
		err = &NotSingularError{materialgroup.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *MaterialGroupQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of MaterialGroups.
func (_q *MaterialGroupQuery) All(ctx context.Context) ([]*MaterialGroup, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*MaterialGroup, *MaterialGroupQuery]()
	return withInterceptors[[]*MaterialGroup](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *MaterialGroupQuery) AllX(ctx context.Context) []*MaterialGroup {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of MaterialGroup IDs.
func (_q *MaterialGroupQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(materialgroup.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *MaterialGroupQuery) IDsX(ctx context.Context) []int64 {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *MaterialGroupQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*MaterialGroupQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *MaterialGroupQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *MaterialGroupQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("db1: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *MaterialGroupQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the MaterialGroupQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *MaterialGroupQuery) Clone() *MaterialGroupQuery {
	if _q == nil {
		return nil
	}
	return &MaterialGroupQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]materialgroup.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.MaterialGroup{}, _q.predicates...),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.MaterialGroup.Query().
//		GroupBy(materialgroup.FieldCreatedTime).
//		Aggregate(db1.Count()).
//		Scan(ctx, &v)
func (_q *MaterialGroupQuery) GroupBy(field string, fields ...string) *MaterialGroupGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &MaterialGroupGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = materialgroup.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//	}
//
//	client.MaterialGroup.Query().
//		Select(materialgroup.FieldCreatedTime).
//		Scan(ctx, &v)
func (_q *MaterialGroupQuery) Select(fields ...string) *MaterialGroupSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &MaterialGroupSelect{MaterialGroupQuery: _q}
	sbuild.label = materialgroup.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a MaterialGroupSelect configured with the given aggregations.
func (_q *MaterialGroupQuery) Aggregate(fns ...AggregateFunc) *MaterialGroupSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *MaterialGroupQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("db1: uninitialized interceptor (forgotten import db1/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !materialgroup.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *MaterialGroupQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*MaterialGroup, error) {
	var (
		nodes = []*MaterialGroup{}
		_spec = _q.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*MaterialGroup).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &MaterialGroup{config: _q.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (_q *MaterialGroupQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *MaterialGroupQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(materialgroup.Table, materialgroup.Columns, sqlgraph.NewFieldSpec(materialgroup.FieldID, field.TypeInt64))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, materialgroup.FieldID)
		for i := range fields {
			if fields[i] != materialgroup.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *MaterialGroupQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(materialgroup.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = materialgroup.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WhereCreatedTime applies equality check predicate on the "created_time" field
func (_m *MaterialGroupQuery) WhereCreatedTime(v time.Time) *MaterialGroupQuery {
	_m.Where(materialgroup.CreatedTime(v))
	return _m
}

// WhereUpdatedTime applies equality check predicate on the "updated_time" field
func (_m *MaterialGroupQuery) WhereUpdatedTime(v time.Time) *MaterialGroupQuery {
	_m.Where(materialgroup.UpdatedTime(v))
	return _m
}

// WhereName applies equality check predicate on the "name" field
func (_m *MaterialGroupQuery) WhereName(v string) *MaterialGroupQuery {
	_m.Where(materialgroup.Name(v))
	return _m
}

// MaterialGroupGroupBy is the group-by builder for MaterialGroup entities.
type MaterialGroupGroupBy struct {
	selector
	build *MaterialGroupQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *MaterialGroupGroupBy) Aggregate(fns ...AggregateFunc) *MaterialGroupGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *MaterialGroupGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MaterialGroupQuery, *MaterialGroupGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *MaterialGroupGroupBy) sqlScan(ctx context.Context, root *MaterialGroupQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// MaterialGroupSelect is the builder for selecting fields of MaterialGroup entities.
type MaterialGroupSelect struct {
	*MaterialGroupQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *MaterialGroupSelect) Aggregate(fns ...AggregateFunc) *MaterialGroupSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *MaterialGroupSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*MaterialGroupQuery, *MaterialGroupSelect](ctx, _s.MaterialGroupQuery, _s, _s.inters, v)
}

func (_s *MaterialGroupSelect) sqlScan(ctx context.Context, root *MaterialGroupQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
