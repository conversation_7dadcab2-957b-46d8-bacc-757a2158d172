// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/materialgroup"
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MaterialGroupUpdate is the builder for updating MaterialGroup entities.
type MaterialGroupUpdate struct {
	config
	hooks    []Hook
	mutation *MaterialGroupMutation
}

// Where appends a list predicates to the MaterialGroupUpdate builder.
func (_u *MaterialGroupUpdate) Where(ps ...predicate.MaterialGroup) *MaterialGroupUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *MaterialGroupUpdate) SetUpdatedTime(v time.Time) *MaterialGroupUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetName sets the "name" field.
func (_u *MaterialGroupUpdate) SetName(v string) *MaterialGroupUpdate {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *MaterialGroupUpdate) SetNillableName(v *string) *MaterialGroupUpdate {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// Mutation returns the MaterialGroupMutation object of the builder.
func (_u *MaterialGroupUpdate) Mutation() *MaterialGroupMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *MaterialGroupUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *MaterialGroupUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *MaterialGroupUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *MaterialGroupUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *MaterialGroupUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := materialgroup.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

func (_u *MaterialGroupUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(materialgroup.Table, materialgroup.Columns, sqlgraph.NewFieldSpec(materialgroup.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(materialgroup.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(materialgroup.FieldName, field.TypeString, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{materialgroup.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// MaterialGroupUpdateOne is the builder for updating a single MaterialGroup entity.
type MaterialGroupUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *MaterialGroupMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *MaterialGroupUpdateOne) SetUpdatedTime(v time.Time) *MaterialGroupUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetName sets the "name" field.
func (_u *MaterialGroupUpdateOne) SetName(v string) *MaterialGroupUpdateOne {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *MaterialGroupUpdateOne) SetNillableName(v *string) *MaterialGroupUpdateOne {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// Mutation returns the MaterialGroupMutation object of the builder.
func (_u *MaterialGroupUpdateOne) Mutation() *MaterialGroupMutation {
	return _u.mutation
}

// Where appends a list predicates to the MaterialGroupUpdate builder.
func (_u *MaterialGroupUpdateOne) Where(ps ...predicate.MaterialGroup) *MaterialGroupUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *MaterialGroupUpdateOne) Select(field string, fields ...string) *MaterialGroupUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated MaterialGroup entity.
func (_u *MaterialGroupUpdateOne) Save(ctx context.Context) (*MaterialGroup, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *MaterialGroupUpdateOne) SaveX(ctx context.Context) *MaterialGroup {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *MaterialGroupUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *MaterialGroupUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *MaterialGroupUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := materialgroup.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

func (_u *MaterialGroupUpdateOne) sqlSave(ctx context.Context) (_node *MaterialGroup, err error) {
	_spec := sqlgraph.NewUpdateSpec(materialgroup.Table, materialgroup.Columns, sqlgraph.NewFieldSpec(materialgroup.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "MaterialGroup.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, materialgroup.FieldID)
		for _, f := range fields {
			if !materialgroup.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != materialgroup.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(materialgroup.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(materialgroup.FieldName, field.TypeString, value)
	}
	_node = &MaterialGroup{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{materialgroup.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the MaterialGroupUpdate builder.
func (_u *MaterialGroupUpdate) WhereCreatedTime(v time.Time) *MaterialGroupUpdate {
	_u.Where(materialgroup.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the MaterialGroupUpdate builder.
func (_u *MaterialGroupUpdate) WhereUpdatedTime(v time.Time) *MaterialGroupUpdate {
	_u.Where(materialgroup.UpdatedTime(v))
	return _u
}

// WhereName applies equality check predicate to the MaterialGroupUpdate builder.
func (_u *MaterialGroupUpdate) WhereName(v string) *MaterialGroupUpdate {
	_u.Where(materialgroup.Name(v))
	return _u
}
