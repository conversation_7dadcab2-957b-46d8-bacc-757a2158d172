// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AdministratorsColumns holds the columns for the "administrators" table.
	AdministratorsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "username", Type: field.TypeString, Unique: true},
		{Name: "password", Type: field.TypeString},
		{Name: "state", Type: field.TypeString},
	}
	// AdministratorsTable holds the schema information for the "administrators" table.
	AdministratorsTable = &schema.Table{
		Name:       "administrators",
		Columns:    AdministratorsColumns,
		PrimaryKey: []*schema.Column{AdministratorsColumns[0]},
	}
	// MaterialColumns holds the columns for the "material" table.
	MaterialColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "title", Type: field.TypeString},
		{Name: "source_url", Type: field.TypeString},
		{Name: "material_group", Type: field.TypeOther, Nullable: true, SchemaType: map[string]string{"postgres": "int8[]"}},
		{Name: "flag", Type: field.TypeOther, Nullable: true, SchemaType: map[string]string{"postgres": "text[]"}},
		{Name: "source_temu_id", Type: field.TypeInt64},
		{Name: "hash", Type: field.TypeString},
		{Name: "path", Type: field.TypeString},
	}
	// MaterialTable holds the schema information for the "material" table.
	MaterialTable = &schema.Table{
		Name:       "material",
		Columns:    MaterialColumns,
		PrimaryKey: []*schema.Column{MaterialColumns[0]},
	}
	// MaterialGroupColumns holds the columns for the "material_group" table.
	MaterialGroupColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "name", Type: field.TypeString, Unique: true},
	}
	// MaterialGroupTable holds the schema information for the "material_group" table.
	MaterialGroupTable = &schema.Table{
		Name:       "material_group",
		Columns:    MaterialGroupColumns,
		PrimaryKey: []*schema.Column{MaterialGroupColumns[0]},
	}
	// ProductItemsColumns holds the columns for the "product_items" table.
	ProductItemsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "hash", Type: field.TypeString, Unique: true, Size: 40},
		{Name: "item_id", Type: field.TypeString, Size: 40},
		{Name: "platform", Type: field.TypeString, Size: 10},
		{Name: "spec", Type: field.TypeJSON},
		{Name: "filter", Type: field.TypeJSON},
		{Name: "mark", Type: field.TypeJSON, Nullable: true},
		{Name: "data", Type: field.TypeString, Size: 2147483647},
	}
	// ProductItemsTable holds the schema information for the "product_items" table.
	ProductItemsTable = &schema.Table{
		Name:       "product_items",
		Columns:    ProductItemsColumns,
		PrimaryKey: []*schema.Column{ProductItemsColumns[0]},
	}
	// PsdsColumns holds the columns for the "psds" table.
	PsdsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "psd_group_id", Type: field.TypeOther, Nullable: true, SchemaType: map[string]string{"postgres": "int8[]"}},
		{Name: "desc", Type: field.TypeString, Nullable: true},
		{Name: "file_path", Type: field.TypeString},
		{Name: "file_sha1", Type: field.TypeString, Unique: true},
		{Name: "file_valid", Type: field.TypeInt32, Default: 0},
		{Name: "weight", Type: field.TypeInt32, Default: 0},
		{Name: "is_cover", Type: field.TypeInt32, Default: 0},
	}
	// PsdsTable holds the schema information for the "psds" table.
	PsdsTable = &schema.Table{
		Name:       "psds",
		Columns:    PsdsColumns,
		PrimaryKey: []*schema.Column{PsdsColumns[0]},
	}
	// PsdCombineResultsColumns holds the columns for the "psd_combine_results" table.
	PsdCombineResultsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "task_id", Type: field.TypeInt64},
		{Name: "result_url", Type: field.TypeString},
		{Name: "weight", Type: field.TypeInt32, Default: 0},
		{Name: "is_cover", Type: field.TypeInt32, Default: 0},
	}
	// PsdCombineResultsTable holds the schema information for the "psd_combine_results" table.
	PsdCombineResultsTable = &schema.Table{
		Name:       "psd_combine_results",
		Columns:    PsdCombineResultsColumns,
		PrimaryKey: []*schema.Column{PsdCombineResultsColumns[0]},
	}
	// PsdCombineTasksColumns holds the columns for the "psd_combine_tasks" table.
	PsdCombineTasksColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "psd_group_id", Type: field.TypeInt64},
		{Name: "material_url", Type: field.TypeString},
		{Name: "reference_id", Type: field.TypeInt64, Nullable: true},
		{Name: "extra_params", Type: field.TypeString, Nullable: true, Size: 2147483647},
		{Name: "status", Type: field.TypeString, Default: "0"},
		{Name: "error_msg", Type: field.TypeString, Nullable: true},
	}
	// PsdCombineTasksTable holds the schema information for the "psd_combine_tasks" table.
	PsdCombineTasksTable = &schema.Table{
		Name:       "psd_combine_tasks",
		Columns:    PsdCombineTasksColumns,
		PrimaryKey: []*schema.Column{PsdCombineTasksColumns[0]},
	}
	// PsdGroupsColumns holds the columns for the "psd_groups" table.
	PsdGroupsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "name", Type: field.TypeString, Unique: true},
		{Name: "desc", Type: field.TypeString, Nullable: true},
	}
	// PsdGroupsTable holds the schema information for the "psd_groups" table.
	PsdGroupsTable = &schema.Table{
		Name:       "psd_groups",
		Columns:    PsdGroupsColumns,
		PrimaryKey: []*schema.Column{PsdGroupsColumns[0]},
	}
	// RegisteredTrademarkColumns holds the columns for the "registered_trademark" table.
	RegisteredTrademarkColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "updated_time", Type: field.TypeTime, SchemaType: map[string]string{"postgres": "timestamp without time zone"}},
		{Name: "tid", Type: field.TypeInt32, Unique: true},
		{Name: "data", Type: field.TypeJSON},
	}
	// RegisteredTrademarkTable holds the schema information for the "registered_trademark" table.
	RegisteredTrademarkTable = &schema.Table{
		Name:       "registered_trademark",
		Columns:    RegisteredTrademarkColumns,
		PrimaryKey: []*schema.Column{RegisteredTrademarkColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AdministratorsTable,
		MaterialTable,
		MaterialGroupTable,
		ProductItemsTable,
		PsdsTable,
		PsdCombineResultsTable,
		PsdCombineTasksTable,
		PsdGroupsTable,
		RegisteredTrademarkTable,
	}
)

func init() {
	MaterialTable.Annotation = &entsql.Annotation{
		Table: "material",
	}
	MaterialGroupTable.Annotation = &entsql.Annotation{
		Table: "material_group",
	}
	RegisteredTrademarkTable.Annotation = &entsql.Annotation{
		Table: "registered_trademark",
	}
}
