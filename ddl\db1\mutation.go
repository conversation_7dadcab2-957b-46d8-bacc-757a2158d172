// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"omnix/ddl/db1/administrator"
	"omnix/ddl/db1/material"
	"omnix/ddl/db1/materialgroup"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/productitem"
	"omnix/ddl/db1/psd"
	"omnix/ddl/db1/psdcombineresult"
	"omnix/ddl/db1/psdcombinetask"
	"omnix/ddl/db1/psdgroup"
	"omnix/ddl/db1/registeredtrademark"
	"omnix/genpb/msgpb"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAdministrator       = "Administrator"
	TypeMaterial            = "Material"
	TypeMaterialGroup       = "MaterialGroup"
	TypeProductItem         = "ProductItem"
	TypePsd                 = "Psd"
	TypePsdCombineResult    = "PsdCombineResult"
	TypePsdCombineTask      = "PsdCombineTask"
	TypePsdGroup            = "PsdGroup"
	TypeRegisteredTrademark = "RegisteredTrademark"
)

// AdministratorMutation represents an operation that mutates the Administrator nodes in the graph.
type AdministratorMutation struct {
	config
	op            Op
	typ           string
	id            *int64
	created_time  *time.Time
	updated_time  *time.Time
	username      *string
	password      *string
	state         *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Administrator, error)
	predicates    []predicate.Administrator
}

var _ ent.Mutation = (*AdministratorMutation)(nil)

// administratorOption allows management of the mutation configuration using functional options.
type administratorOption func(*AdministratorMutation)

// newAdministratorMutation creates new mutation for the Administrator entity.
func newAdministratorMutation(c config, op Op, opts ...administratorOption) *AdministratorMutation {
	m := &AdministratorMutation{
		config:        c,
		op:            op,
		typ:           TypeAdministrator,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAdministratorID sets the ID field of the mutation.
func withAdministratorID(id int64) administratorOption {
	return func(m *AdministratorMutation) {
		var (
			err   error
			once  sync.Once
			value *Administrator
		)
		m.oldValue = func(ctx context.Context) (*Administrator, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Administrator.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAdministrator sets the old Administrator of the mutation.
func withAdministrator(node *Administrator) administratorOption {
	return func(m *AdministratorMutation) {
		m.oldValue = func(context.Context) (*Administrator, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AdministratorMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AdministratorMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Administrator entities.
func (m *AdministratorMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AdministratorMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AdministratorMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Administrator.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *AdministratorMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *AdministratorMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the Administrator entity.
// If the Administrator object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdministratorMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *AdministratorMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *AdministratorMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *AdministratorMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the Administrator entity.
// If the Administrator object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdministratorMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *AdministratorMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetUsername sets the "username" field.
func (m *AdministratorMutation) SetUsername(s string) {
	m.username = &s
}

// Username returns the value of the "username" field in the mutation.
func (m *AdministratorMutation) Username() (r string, exists bool) {
	v := m.username
	if v == nil {
		return
	}
	return *v, true
}

// OldUsername returns the old "username" field's value of the Administrator entity.
// If the Administrator object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdministratorMutation) OldUsername(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUsername is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUsername requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUsername: %w", err)
	}
	return oldValue.Username, nil
}

// ResetUsername resets all changes to the "username" field.
func (m *AdministratorMutation) ResetUsername() {
	m.username = nil
}

// SetPassword sets the "password" field.
func (m *AdministratorMutation) SetPassword(s string) {
	m.password = &s
}

// Password returns the value of the "password" field in the mutation.
func (m *AdministratorMutation) Password() (r string, exists bool) {
	v := m.password
	if v == nil {
		return
	}
	return *v, true
}

// OldPassword returns the old "password" field's value of the Administrator entity.
// If the Administrator object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdministratorMutation) OldPassword(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPassword is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPassword requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPassword: %w", err)
	}
	return oldValue.Password, nil
}

// ResetPassword resets all changes to the "password" field.
func (m *AdministratorMutation) ResetPassword() {
	m.password = nil
}

// SetState sets the "state" field.
func (m *AdministratorMutation) SetState(s string) {
	m.state = &s
}

// State returns the value of the "state" field in the mutation.
func (m *AdministratorMutation) State() (r string, exists bool) {
	v := m.state
	if v == nil {
		return
	}
	return *v, true
}

// OldState returns the old "state" field's value of the Administrator entity.
// If the Administrator object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdministratorMutation) OldState(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldState is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldState requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldState: %w", err)
	}
	return oldValue.State, nil
}

// ResetState resets all changes to the "state" field.
func (m *AdministratorMutation) ResetState() {
	m.state = nil
}

// Where appends a list predicates to the AdministratorMutation builder.
func (m *AdministratorMutation) Where(ps ...predicate.Administrator) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AdministratorMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AdministratorMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Administrator, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AdministratorMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AdministratorMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Administrator).
func (m *AdministratorMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AdministratorMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.created_time != nil {
		fields = append(fields, administrator.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, administrator.FieldUpdatedTime)
	}
	if m.username != nil {
		fields = append(fields, administrator.FieldUsername)
	}
	if m.password != nil {
		fields = append(fields, administrator.FieldPassword)
	}
	if m.state != nil {
		fields = append(fields, administrator.FieldState)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AdministratorMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case administrator.FieldCreatedTime:
		return m.CreatedTime()
	case administrator.FieldUpdatedTime:
		return m.UpdatedTime()
	case administrator.FieldUsername:
		return m.Username()
	case administrator.FieldPassword:
		return m.Password()
	case administrator.FieldState:
		return m.State()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AdministratorMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case administrator.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case administrator.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case administrator.FieldUsername:
		return m.OldUsername(ctx)
	case administrator.FieldPassword:
		return m.OldPassword(ctx)
	case administrator.FieldState:
		return m.OldState(ctx)
	}
	return nil, fmt.Errorf("unknown Administrator field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AdministratorMutation) SetField(name string, value ent.Value) error {
	switch name {
	case administrator.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case administrator.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case administrator.FieldUsername:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUsername(v)
		return nil
	case administrator.FieldPassword:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPassword(v)
		return nil
	case administrator.FieldState:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetState(v)
		return nil
	}
	return fmt.Errorf("unknown Administrator field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AdministratorMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AdministratorMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AdministratorMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown Administrator numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AdministratorMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AdministratorMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AdministratorMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Administrator nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AdministratorMutation) ResetField(name string) error {
	switch name {
	case administrator.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case administrator.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case administrator.FieldUsername:
		m.ResetUsername()
		return nil
	case administrator.FieldPassword:
		m.ResetPassword()
		return nil
	case administrator.FieldState:
		m.ResetState()
		return nil
	}
	return fmt.Errorf("unknown Administrator field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AdministratorMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AdministratorMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AdministratorMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AdministratorMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AdministratorMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AdministratorMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AdministratorMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Administrator unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AdministratorMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Administrator edge %s", name)
}

// MaterialMutation represents an operation that mutates the Material nodes in the graph.
type MaterialMutation struct {
	config
	op                Op
	typ               string
	id                *int64
	created_time      *time.Time
	updated_time      *time.Time
	title             *string
	source_url        *string
	material_group    *pq.Int64Array
	flag              *pq.StringArray
	source_temu_id    *int64
	addsource_temu_id *int64
	hash              *string
	_path             *string
	clearedFields     map[string]struct{}
	done              bool
	oldValue          func(context.Context) (*Material, error)
	predicates        []predicate.Material
}

var _ ent.Mutation = (*MaterialMutation)(nil)

// materialOption allows management of the mutation configuration using functional options.
type materialOption func(*MaterialMutation)

// newMaterialMutation creates new mutation for the Material entity.
func newMaterialMutation(c config, op Op, opts ...materialOption) *MaterialMutation {
	m := &MaterialMutation{
		config:        c,
		op:            op,
		typ:           TypeMaterial,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withMaterialID sets the ID field of the mutation.
func withMaterialID(id int64) materialOption {
	return func(m *MaterialMutation) {
		var (
			err   error
			once  sync.Once
			value *Material
		)
		m.oldValue = func(ctx context.Context) (*Material, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Material.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withMaterial sets the old Material of the mutation.
func withMaterial(node *Material) materialOption {
	return func(m *MaterialMutation) {
		m.oldValue = func(context.Context) (*Material, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m MaterialMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m MaterialMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Material entities.
func (m *MaterialMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *MaterialMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *MaterialMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Material.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *MaterialMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *MaterialMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *MaterialMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *MaterialMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *MaterialMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *MaterialMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetTitle sets the "title" field.
func (m *MaterialMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *MaterialMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ResetTitle resets all changes to the "title" field.
func (m *MaterialMutation) ResetTitle() {
	m.title = nil
}

// SetSourceURL sets the "source_url" field.
func (m *MaterialMutation) SetSourceURL(s string) {
	m.source_url = &s
}

// SourceURL returns the value of the "source_url" field in the mutation.
func (m *MaterialMutation) SourceURL() (r string, exists bool) {
	v := m.source_url
	if v == nil {
		return
	}
	return *v, true
}

// OldSourceURL returns the old "source_url" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldSourceURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSourceURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSourceURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSourceURL: %w", err)
	}
	return oldValue.SourceURL, nil
}

// ResetSourceURL resets all changes to the "source_url" field.
func (m *MaterialMutation) ResetSourceURL() {
	m.source_url = nil
}

// SetMaterialGroup sets the "material_group" field.
func (m *MaterialMutation) SetMaterialGroup(pq pq.Int64Array) {
	m.material_group = &pq
}

// MaterialGroup returns the value of the "material_group" field in the mutation.
func (m *MaterialMutation) MaterialGroup() (r pq.Int64Array, exists bool) {
	v := m.material_group
	if v == nil {
		return
	}
	return *v, true
}

// OldMaterialGroup returns the old "material_group" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldMaterialGroup(ctx context.Context) (v pq.Int64Array, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaterialGroup is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaterialGroup requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaterialGroup: %w", err)
	}
	return oldValue.MaterialGroup, nil
}

// ClearMaterialGroup clears the value of the "material_group" field.
func (m *MaterialMutation) ClearMaterialGroup() {
	m.material_group = nil
	m.clearedFields[material.FieldMaterialGroup] = struct{}{}
}

// MaterialGroupCleared returns if the "material_group" field was cleared in this mutation.
func (m *MaterialMutation) MaterialGroupCleared() bool {
	_, ok := m.clearedFields[material.FieldMaterialGroup]
	return ok
}

// ResetMaterialGroup resets all changes to the "material_group" field.
func (m *MaterialMutation) ResetMaterialGroup() {
	m.material_group = nil
	delete(m.clearedFields, material.FieldMaterialGroup)
}

// SetFlag sets the "flag" field.
func (m *MaterialMutation) SetFlag(pa pq.StringArray) {
	m.flag = &pa
}

// Flag returns the value of the "flag" field in the mutation.
func (m *MaterialMutation) Flag() (r pq.StringArray, exists bool) {
	v := m.flag
	if v == nil {
		return
	}
	return *v, true
}

// OldFlag returns the old "flag" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldFlag(ctx context.Context) (v pq.StringArray, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFlag is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFlag requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFlag: %w", err)
	}
	return oldValue.Flag, nil
}

// ClearFlag clears the value of the "flag" field.
func (m *MaterialMutation) ClearFlag() {
	m.flag = nil
	m.clearedFields[material.FieldFlag] = struct{}{}
}

// FlagCleared returns if the "flag" field was cleared in this mutation.
func (m *MaterialMutation) FlagCleared() bool {
	_, ok := m.clearedFields[material.FieldFlag]
	return ok
}

// ResetFlag resets all changes to the "flag" field.
func (m *MaterialMutation) ResetFlag() {
	m.flag = nil
	delete(m.clearedFields, material.FieldFlag)
}

// SetSourceTemuID sets the "source_temu_id" field.
func (m *MaterialMutation) SetSourceTemuID(i int64) {
	m.source_temu_id = &i
	m.addsource_temu_id = nil
}

// SourceTemuID returns the value of the "source_temu_id" field in the mutation.
func (m *MaterialMutation) SourceTemuID() (r int64, exists bool) {
	v := m.source_temu_id
	if v == nil {
		return
	}
	return *v, true
}

// OldSourceTemuID returns the old "source_temu_id" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldSourceTemuID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSourceTemuID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSourceTemuID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSourceTemuID: %w", err)
	}
	return oldValue.SourceTemuID, nil
}

// AddSourceTemuID adds i to the "source_temu_id" field.
func (m *MaterialMutation) AddSourceTemuID(i int64) {
	if m.addsource_temu_id != nil {
		*m.addsource_temu_id += i
	} else {
		m.addsource_temu_id = &i
	}
}

// AddedSourceTemuID returns the value that was added to the "source_temu_id" field in this mutation.
func (m *MaterialMutation) AddedSourceTemuID() (r int64, exists bool) {
	v := m.addsource_temu_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetSourceTemuID resets all changes to the "source_temu_id" field.
func (m *MaterialMutation) ResetSourceTemuID() {
	m.source_temu_id = nil
	m.addsource_temu_id = nil
}

// SetHash sets the "hash" field.
func (m *MaterialMutation) SetHash(s string) {
	m.hash = &s
}

// Hash returns the value of the "hash" field in the mutation.
func (m *MaterialMutation) Hash() (r string, exists bool) {
	v := m.hash
	if v == nil {
		return
	}
	return *v, true
}

// OldHash returns the old "hash" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldHash(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHash is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHash requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHash: %w", err)
	}
	return oldValue.Hash, nil
}

// ResetHash resets all changes to the "hash" field.
func (m *MaterialMutation) ResetHash() {
	m.hash = nil
}

// SetPath sets the "path" field.
func (m *MaterialMutation) SetPath(s string) {
	m._path = &s
}

// Path returns the value of the "path" field in the mutation.
func (m *MaterialMutation) Path() (r string, exists bool) {
	v := m._path
	if v == nil {
		return
	}
	return *v, true
}

// OldPath returns the old "path" field's value of the Material entity.
// If the Material object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialMutation) OldPath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPath: %w", err)
	}
	return oldValue.Path, nil
}

// ResetPath resets all changes to the "path" field.
func (m *MaterialMutation) ResetPath() {
	m._path = nil
}

// Where appends a list predicates to the MaterialMutation builder.
func (m *MaterialMutation) Where(ps ...predicate.Material) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the MaterialMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *MaterialMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Material, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *MaterialMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *MaterialMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Material).
func (m *MaterialMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *MaterialMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.created_time != nil {
		fields = append(fields, material.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, material.FieldUpdatedTime)
	}
	if m.title != nil {
		fields = append(fields, material.FieldTitle)
	}
	if m.source_url != nil {
		fields = append(fields, material.FieldSourceURL)
	}
	if m.material_group != nil {
		fields = append(fields, material.FieldMaterialGroup)
	}
	if m.flag != nil {
		fields = append(fields, material.FieldFlag)
	}
	if m.source_temu_id != nil {
		fields = append(fields, material.FieldSourceTemuID)
	}
	if m.hash != nil {
		fields = append(fields, material.FieldHash)
	}
	if m._path != nil {
		fields = append(fields, material.FieldPath)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *MaterialMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case material.FieldCreatedTime:
		return m.CreatedTime()
	case material.FieldUpdatedTime:
		return m.UpdatedTime()
	case material.FieldTitle:
		return m.Title()
	case material.FieldSourceURL:
		return m.SourceURL()
	case material.FieldMaterialGroup:
		return m.MaterialGroup()
	case material.FieldFlag:
		return m.Flag()
	case material.FieldSourceTemuID:
		return m.SourceTemuID()
	case material.FieldHash:
		return m.Hash()
	case material.FieldPath:
		return m.Path()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *MaterialMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case material.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case material.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case material.FieldTitle:
		return m.OldTitle(ctx)
	case material.FieldSourceURL:
		return m.OldSourceURL(ctx)
	case material.FieldMaterialGroup:
		return m.OldMaterialGroup(ctx)
	case material.FieldFlag:
		return m.OldFlag(ctx)
	case material.FieldSourceTemuID:
		return m.OldSourceTemuID(ctx)
	case material.FieldHash:
		return m.OldHash(ctx)
	case material.FieldPath:
		return m.OldPath(ctx)
	}
	return nil, fmt.Errorf("unknown Material field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *MaterialMutation) SetField(name string, value ent.Value) error {
	switch name {
	case material.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case material.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case material.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case material.FieldSourceURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSourceURL(v)
		return nil
	case material.FieldMaterialGroup:
		v, ok := value.(pq.Int64Array)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaterialGroup(v)
		return nil
	case material.FieldFlag:
		v, ok := value.(pq.StringArray)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFlag(v)
		return nil
	case material.FieldSourceTemuID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSourceTemuID(v)
		return nil
	case material.FieldHash:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHash(v)
		return nil
	case material.FieldPath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPath(v)
		return nil
	}
	return fmt.Errorf("unknown Material field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *MaterialMutation) AddedFields() []string {
	var fields []string
	if m.addsource_temu_id != nil {
		fields = append(fields, material.FieldSourceTemuID)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *MaterialMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case material.FieldSourceTemuID:
		return m.AddedSourceTemuID()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *MaterialMutation) AddField(name string, value ent.Value) error {
	switch name {
	case material.FieldSourceTemuID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddSourceTemuID(v)
		return nil
	}
	return fmt.Errorf("unknown Material numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *MaterialMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(material.FieldMaterialGroup) {
		fields = append(fields, material.FieldMaterialGroup)
	}
	if m.FieldCleared(material.FieldFlag) {
		fields = append(fields, material.FieldFlag)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *MaterialMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *MaterialMutation) ClearField(name string) error {
	switch name {
	case material.FieldMaterialGroup:
		m.ClearMaterialGroup()
		return nil
	case material.FieldFlag:
		m.ClearFlag()
		return nil
	}
	return fmt.Errorf("unknown Material nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *MaterialMutation) ResetField(name string) error {
	switch name {
	case material.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case material.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case material.FieldTitle:
		m.ResetTitle()
		return nil
	case material.FieldSourceURL:
		m.ResetSourceURL()
		return nil
	case material.FieldMaterialGroup:
		m.ResetMaterialGroup()
		return nil
	case material.FieldFlag:
		m.ResetFlag()
		return nil
	case material.FieldSourceTemuID:
		m.ResetSourceTemuID()
		return nil
	case material.FieldHash:
		m.ResetHash()
		return nil
	case material.FieldPath:
		m.ResetPath()
		return nil
	}
	return fmt.Errorf("unknown Material field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *MaterialMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *MaterialMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *MaterialMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *MaterialMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *MaterialMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *MaterialMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *MaterialMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Material unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *MaterialMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Material edge %s", name)
}

// MaterialGroupMutation represents an operation that mutates the MaterialGroup nodes in the graph.
type MaterialGroupMutation struct {
	config
	op            Op
	typ           string
	id            *int64
	created_time  *time.Time
	updated_time  *time.Time
	name          *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*MaterialGroup, error)
	predicates    []predicate.MaterialGroup
}

var _ ent.Mutation = (*MaterialGroupMutation)(nil)

// materialgroupOption allows management of the mutation configuration using functional options.
type materialgroupOption func(*MaterialGroupMutation)

// newMaterialGroupMutation creates new mutation for the MaterialGroup entity.
func newMaterialGroupMutation(c config, op Op, opts ...materialgroupOption) *MaterialGroupMutation {
	m := &MaterialGroupMutation{
		config:        c,
		op:            op,
		typ:           TypeMaterialGroup,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withMaterialGroupID sets the ID field of the mutation.
func withMaterialGroupID(id int64) materialgroupOption {
	return func(m *MaterialGroupMutation) {
		var (
			err   error
			once  sync.Once
			value *MaterialGroup
		)
		m.oldValue = func(ctx context.Context) (*MaterialGroup, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().MaterialGroup.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withMaterialGroup sets the old MaterialGroup of the mutation.
func withMaterialGroup(node *MaterialGroup) materialgroupOption {
	return func(m *MaterialGroupMutation) {
		m.oldValue = func(context.Context) (*MaterialGroup, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m MaterialGroupMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m MaterialGroupMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of MaterialGroup entities.
func (m *MaterialGroupMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *MaterialGroupMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *MaterialGroupMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().MaterialGroup.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *MaterialGroupMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *MaterialGroupMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the MaterialGroup entity.
// If the MaterialGroup object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialGroupMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *MaterialGroupMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *MaterialGroupMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *MaterialGroupMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the MaterialGroup entity.
// If the MaterialGroup object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialGroupMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *MaterialGroupMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetName sets the "name" field.
func (m *MaterialGroupMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *MaterialGroupMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the MaterialGroup entity.
// If the MaterialGroup object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *MaterialGroupMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *MaterialGroupMutation) ResetName() {
	m.name = nil
}

// Where appends a list predicates to the MaterialGroupMutation builder.
func (m *MaterialGroupMutation) Where(ps ...predicate.MaterialGroup) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the MaterialGroupMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *MaterialGroupMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.MaterialGroup, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *MaterialGroupMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *MaterialGroupMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (MaterialGroup).
func (m *MaterialGroupMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *MaterialGroupMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m.created_time != nil {
		fields = append(fields, materialgroup.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, materialgroup.FieldUpdatedTime)
	}
	if m.name != nil {
		fields = append(fields, materialgroup.FieldName)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *MaterialGroupMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case materialgroup.FieldCreatedTime:
		return m.CreatedTime()
	case materialgroup.FieldUpdatedTime:
		return m.UpdatedTime()
	case materialgroup.FieldName:
		return m.Name()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *MaterialGroupMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case materialgroup.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case materialgroup.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case materialgroup.FieldName:
		return m.OldName(ctx)
	}
	return nil, fmt.Errorf("unknown MaterialGroup field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *MaterialGroupMutation) SetField(name string, value ent.Value) error {
	switch name {
	case materialgroup.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case materialgroup.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case materialgroup.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	}
	return fmt.Errorf("unknown MaterialGroup field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *MaterialGroupMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *MaterialGroupMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *MaterialGroupMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown MaterialGroup numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *MaterialGroupMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *MaterialGroupMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *MaterialGroupMutation) ClearField(name string) error {
	return fmt.Errorf("unknown MaterialGroup nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *MaterialGroupMutation) ResetField(name string) error {
	switch name {
	case materialgroup.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case materialgroup.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case materialgroup.FieldName:
		m.ResetName()
		return nil
	}
	return fmt.Errorf("unknown MaterialGroup field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *MaterialGroupMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *MaterialGroupMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *MaterialGroupMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *MaterialGroupMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *MaterialGroupMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *MaterialGroupMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *MaterialGroupMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown MaterialGroup unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *MaterialGroupMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown MaterialGroup edge %s", name)
}

// ProductItemMutation represents an operation that mutates the ProductItem nodes in the graph.
type ProductItemMutation struct {
	config
	op            Op
	typ           string
	id            *int64
	created_time  *time.Time
	updated_time  *time.Time
	hash          *string
	item_id       *string
	platform      *string
	spec          **msgpb.ProductSpec
	filter        **msgpb.ProductFilter
	mark          *[]string
	appendmark    []string
	data          *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*ProductItem, error)
	predicates    []predicate.ProductItem
}

var _ ent.Mutation = (*ProductItemMutation)(nil)

// productitemOption allows management of the mutation configuration using functional options.
type productitemOption func(*ProductItemMutation)

// newProductItemMutation creates new mutation for the ProductItem entity.
func newProductItemMutation(c config, op Op, opts ...productitemOption) *ProductItemMutation {
	m := &ProductItemMutation{
		config:        c,
		op:            op,
		typ:           TypeProductItem,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withProductItemID sets the ID field of the mutation.
func withProductItemID(id int64) productitemOption {
	return func(m *ProductItemMutation) {
		var (
			err   error
			once  sync.Once
			value *ProductItem
		)
		m.oldValue = func(ctx context.Context) (*ProductItem, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ProductItem.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withProductItem sets the old ProductItem of the mutation.
func withProductItem(node *ProductItem) productitemOption {
	return func(m *ProductItemMutation) {
		m.oldValue = func(context.Context) (*ProductItem, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ProductItemMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ProductItemMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of ProductItem entities.
func (m *ProductItemMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ProductItemMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ProductItemMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ProductItem.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *ProductItemMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *ProductItemMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *ProductItemMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *ProductItemMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *ProductItemMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *ProductItemMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetHash sets the "hash" field.
func (m *ProductItemMutation) SetHash(s string) {
	m.hash = &s
}

// Hash returns the value of the "hash" field in the mutation.
func (m *ProductItemMutation) Hash() (r string, exists bool) {
	v := m.hash
	if v == nil {
		return
	}
	return *v, true
}

// OldHash returns the old "hash" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldHash(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldHash is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldHash requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldHash: %w", err)
	}
	return oldValue.Hash, nil
}

// ResetHash resets all changes to the "hash" field.
func (m *ProductItemMutation) ResetHash() {
	m.hash = nil
}

// SetItemID sets the "item_id" field.
func (m *ProductItemMutation) SetItemID(s string) {
	m.item_id = &s
}

// ItemID returns the value of the "item_id" field in the mutation.
func (m *ProductItemMutation) ItemID() (r string, exists bool) {
	v := m.item_id
	if v == nil {
		return
	}
	return *v, true
}

// OldItemID returns the old "item_id" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldItemID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldItemID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldItemID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldItemID: %w", err)
	}
	return oldValue.ItemID, nil
}

// ResetItemID resets all changes to the "item_id" field.
func (m *ProductItemMutation) ResetItemID() {
	m.item_id = nil
}

// SetPlatform sets the "platform" field.
func (m *ProductItemMutation) SetPlatform(s string) {
	m.platform = &s
}

// Platform returns the value of the "platform" field in the mutation.
func (m *ProductItemMutation) Platform() (r string, exists bool) {
	v := m.platform
	if v == nil {
		return
	}
	return *v, true
}

// OldPlatform returns the old "platform" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldPlatform(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPlatform is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPlatform requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPlatform: %w", err)
	}
	return oldValue.Platform, nil
}

// ResetPlatform resets all changes to the "platform" field.
func (m *ProductItemMutation) ResetPlatform() {
	m.platform = nil
}

// SetSpec sets the "spec" field.
func (m *ProductItemMutation) SetSpec(ms *msgpb.ProductSpec) {
	m.spec = &ms
}

// Spec returns the value of the "spec" field in the mutation.
func (m *ProductItemMutation) Spec() (r *msgpb.ProductSpec, exists bool) {
	v := m.spec
	if v == nil {
		return
	}
	return *v, true
}

// OldSpec returns the old "spec" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldSpec(ctx context.Context) (v *msgpb.ProductSpec, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSpec is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSpec requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSpec: %w", err)
	}
	return oldValue.Spec, nil
}

// ResetSpec resets all changes to the "spec" field.
func (m *ProductItemMutation) ResetSpec() {
	m.spec = nil
}

// SetFilter sets the "filter" field.
func (m *ProductItemMutation) SetFilter(mf *msgpb.ProductFilter) {
	m.filter = &mf
}

// Filter returns the value of the "filter" field in the mutation.
func (m *ProductItemMutation) Filter() (r *msgpb.ProductFilter, exists bool) {
	v := m.filter
	if v == nil {
		return
	}
	return *v, true
}

// OldFilter returns the old "filter" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldFilter(ctx context.Context) (v *msgpb.ProductFilter, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFilter is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFilter requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFilter: %w", err)
	}
	return oldValue.Filter, nil
}

// ResetFilter resets all changes to the "filter" field.
func (m *ProductItemMutation) ResetFilter() {
	m.filter = nil
}

// SetMark sets the "mark" field.
func (m *ProductItemMutation) SetMark(s []string) {
	m.mark = &s
	m.appendmark = nil
}

// Mark returns the value of the "mark" field in the mutation.
func (m *ProductItemMutation) Mark() (r []string, exists bool) {
	v := m.mark
	if v == nil {
		return
	}
	return *v, true
}

// OldMark returns the old "mark" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldMark(ctx context.Context) (v []string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMark is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMark requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMark: %w", err)
	}
	return oldValue.Mark, nil
}

// AppendMark adds s to the "mark" field.
func (m *ProductItemMutation) AppendMark(s []string) {
	m.appendmark = append(m.appendmark, s...)
}

// AppendedMark returns the list of values that were appended to the "mark" field in this mutation.
func (m *ProductItemMutation) AppendedMark() ([]string, bool) {
	if len(m.appendmark) == 0 {
		return nil, false
	}
	return m.appendmark, true
}

// ClearMark clears the value of the "mark" field.
func (m *ProductItemMutation) ClearMark() {
	m.mark = nil
	m.appendmark = nil
	m.clearedFields[productitem.FieldMark] = struct{}{}
}

// MarkCleared returns if the "mark" field was cleared in this mutation.
func (m *ProductItemMutation) MarkCleared() bool {
	_, ok := m.clearedFields[productitem.FieldMark]
	return ok
}

// ResetMark resets all changes to the "mark" field.
func (m *ProductItemMutation) ResetMark() {
	m.mark = nil
	m.appendmark = nil
	delete(m.clearedFields, productitem.FieldMark)
}

// SetData sets the "data" field.
func (m *ProductItemMutation) SetData(s string) {
	m.data = &s
}

// Data returns the value of the "data" field in the mutation.
func (m *ProductItemMutation) Data() (r string, exists bool) {
	v := m.data
	if v == nil {
		return
	}
	return *v, true
}

// OldData returns the old "data" field's value of the ProductItem entity.
// If the ProductItem object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProductItemMutation) OldData(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldData: %w", err)
	}
	return oldValue.Data, nil
}

// ResetData resets all changes to the "data" field.
func (m *ProductItemMutation) ResetData() {
	m.data = nil
}

// Where appends a list predicates to the ProductItemMutation builder.
func (m *ProductItemMutation) Where(ps ...predicate.ProductItem) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ProductItemMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ProductItemMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ProductItem, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ProductItemMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ProductItemMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ProductItem).
func (m *ProductItemMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ProductItemMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.created_time != nil {
		fields = append(fields, productitem.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, productitem.FieldUpdatedTime)
	}
	if m.hash != nil {
		fields = append(fields, productitem.FieldHash)
	}
	if m.item_id != nil {
		fields = append(fields, productitem.FieldItemID)
	}
	if m.platform != nil {
		fields = append(fields, productitem.FieldPlatform)
	}
	if m.spec != nil {
		fields = append(fields, productitem.FieldSpec)
	}
	if m.filter != nil {
		fields = append(fields, productitem.FieldFilter)
	}
	if m.mark != nil {
		fields = append(fields, productitem.FieldMark)
	}
	if m.data != nil {
		fields = append(fields, productitem.FieldData)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ProductItemMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case productitem.FieldCreatedTime:
		return m.CreatedTime()
	case productitem.FieldUpdatedTime:
		return m.UpdatedTime()
	case productitem.FieldHash:
		return m.Hash()
	case productitem.FieldItemID:
		return m.ItemID()
	case productitem.FieldPlatform:
		return m.Platform()
	case productitem.FieldSpec:
		return m.Spec()
	case productitem.FieldFilter:
		return m.Filter()
	case productitem.FieldMark:
		return m.Mark()
	case productitem.FieldData:
		return m.Data()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ProductItemMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case productitem.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case productitem.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case productitem.FieldHash:
		return m.OldHash(ctx)
	case productitem.FieldItemID:
		return m.OldItemID(ctx)
	case productitem.FieldPlatform:
		return m.OldPlatform(ctx)
	case productitem.FieldSpec:
		return m.OldSpec(ctx)
	case productitem.FieldFilter:
		return m.OldFilter(ctx)
	case productitem.FieldMark:
		return m.OldMark(ctx)
	case productitem.FieldData:
		return m.OldData(ctx)
	}
	return nil, fmt.Errorf("unknown ProductItem field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ProductItemMutation) SetField(name string, value ent.Value) error {
	switch name {
	case productitem.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case productitem.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case productitem.FieldHash:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetHash(v)
		return nil
	case productitem.FieldItemID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetItemID(v)
		return nil
	case productitem.FieldPlatform:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPlatform(v)
		return nil
	case productitem.FieldSpec:
		v, ok := value.(*msgpb.ProductSpec)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSpec(v)
		return nil
	case productitem.FieldFilter:
		v, ok := value.(*msgpb.ProductFilter)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFilter(v)
		return nil
	case productitem.FieldMark:
		v, ok := value.([]string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMark(v)
		return nil
	case productitem.FieldData:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetData(v)
		return nil
	}
	return fmt.Errorf("unknown ProductItem field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ProductItemMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ProductItemMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ProductItemMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown ProductItem numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ProductItemMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(productitem.FieldMark) {
		fields = append(fields, productitem.FieldMark)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ProductItemMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ProductItemMutation) ClearField(name string) error {
	switch name {
	case productitem.FieldMark:
		m.ClearMark()
		return nil
	}
	return fmt.Errorf("unknown ProductItem nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ProductItemMutation) ResetField(name string) error {
	switch name {
	case productitem.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case productitem.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case productitem.FieldHash:
		m.ResetHash()
		return nil
	case productitem.FieldItemID:
		m.ResetItemID()
		return nil
	case productitem.FieldPlatform:
		m.ResetPlatform()
		return nil
	case productitem.FieldSpec:
		m.ResetSpec()
		return nil
	case productitem.FieldFilter:
		m.ResetFilter()
		return nil
	case productitem.FieldMark:
		m.ResetMark()
		return nil
	case productitem.FieldData:
		m.ResetData()
		return nil
	}
	return fmt.Errorf("unknown ProductItem field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ProductItemMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ProductItemMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ProductItemMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ProductItemMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ProductItemMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ProductItemMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ProductItemMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown ProductItem unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ProductItemMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown ProductItem edge %s", name)
}

// PsdMutation represents an operation that mutates the Psd nodes in the graph.
type PsdMutation struct {
	config
	op            Op
	typ           string
	id            *int64
	created_time  *time.Time
	updated_time  *time.Time
	psd_group_id  *pq.Int64Array
	desc          *string
	file_path     *string
	file_sha1     *string
	file_valid    *int32
	addfile_valid *int32
	weight        *int32
	addweight     *int32
	is_cover      *int32
	addis_cover   *int32
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Psd, error)
	predicates    []predicate.Psd
}

var _ ent.Mutation = (*PsdMutation)(nil)

// psdOption allows management of the mutation configuration using functional options.
type psdOption func(*PsdMutation)

// newPsdMutation creates new mutation for the Psd entity.
func newPsdMutation(c config, op Op, opts ...psdOption) *PsdMutation {
	m := &PsdMutation{
		config:        c,
		op:            op,
		typ:           TypePsd,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPsdID sets the ID field of the mutation.
func withPsdID(id int64) psdOption {
	return func(m *PsdMutation) {
		var (
			err   error
			once  sync.Once
			value *Psd
		)
		m.oldValue = func(ctx context.Context) (*Psd, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Psd.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPsd sets the old Psd of the mutation.
func withPsd(node *Psd) psdOption {
	return func(m *PsdMutation) {
		m.oldValue = func(context.Context) (*Psd, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PsdMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PsdMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Psd entities.
func (m *PsdMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PsdMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PsdMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Psd.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *PsdMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *PsdMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *PsdMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *PsdMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *PsdMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *PsdMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetPsdGroupID sets the "psd_group_id" field.
func (m *PsdMutation) SetPsdGroupID(pq pq.Int64Array) {
	m.psd_group_id = &pq
}

// PsdGroupID returns the value of the "psd_group_id" field in the mutation.
func (m *PsdMutation) PsdGroupID() (r pq.Int64Array, exists bool) {
	v := m.psd_group_id
	if v == nil {
		return
	}
	return *v, true
}

// OldPsdGroupID returns the old "psd_group_id" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldPsdGroupID(ctx context.Context) (v pq.Int64Array, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPsdGroupID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPsdGroupID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPsdGroupID: %w", err)
	}
	return oldValue.PsdGroupID, nil
}

// ClearPsdGroupID clears the value of the "psd_group_id" field.
func (m *PsdMutation) ClearPsdGroupID() {
	m.psd_group_id = nil
	m.clearedFields[psd.FieldPsdGroupID] = struct{}{}
}

// PsdGroupIDCleared returns if the "psd_group_id" field was cleared in this mutation.
func (m *PsdMutation) PsdGroupIDCleared() bool {
	_, ok := m.clearedFields[psd.FieldPsdGroupID]
	return ok
}

// ResetPsdGroupID resets all changes to the "psd_group_id" field.
func (m *PsdMutation) ResetPsdGroupID() {
	m.psd_group_id = nil
	delete(m.clearedFields, psd.FieldPsdGroupID)
}

// SetDesc sets the "desc" field.
func (m *PsdMutation) SetDesc(s string) {
	m.desc = &s
}

// Desc returns the value of the "desc" field in the mutation.
func (m *PsdMutation) Desc() (r string, exists bool) {
	v := m.desc
	if v == nil {
		return
	}
	return *v, true
}

// OldDesc returns the old "desc" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldDesc(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDesc is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDesc requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDesc: %w", err)
	}
	return oldValue.Desc, nil
}

// ClearDesc clears the value of the "desc" field.
func (m *PsdMutation) ClearDesc() {
	m.desc = nil
	m.clearedFields[psd.FieldDesc] = struct{}{}
}

// DescCleared returns if the "desc" field was cleared in this mutation.
func (m *PsdMutation) DescCleared() bool {
	_, ok := m.clearedFields[psd.FieldDesc]
	return ok
}

// ResetDesc resets all changes to the "desc" field.
func (m *PsdMutation) ResetDesc() {
	m.desc = nil
	delete(m.clearedFields, psd.FieldDesc)
}

// SetFilePath sets the "file_path" field.
func (m *PsdMutation) SetFilePath(s string) {
	m.file_path = &s
}

// FilePath returns the value of the "file_path" field in the mutation.
func (m *PsdMutation) FilePath() (r string, exists bool) {
	v := m.file_path
	if v == nil {
		return
	}
	return *v, true
}

// OldFilePath returns the old "file_path" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldFilePath(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFilePath is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFilePath requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFilePath: %w", err)
	}
	return oldValue.FilePath, nil
}

// ResetFilePath resets all changes to the "file_path" field.
func (m *PsdMutation) ResetFilePath() {
	m.file_path = nil
}

// SetFileSha1 sets the "file_sha1" field.
func (m *PsdMutation) SetFileSha1(s string) {
	m.file_sha1 = &s
}

// FileSha1 returns the value of the "file_sha1" field in the mutation.
func (m *PsdMutation) FileSha1() (r string, exists bool) {
	v := m.file_sha1
	if v == nil {
		return
	}
	return *v, true
}

// OldFileSha1 returns the old "file_sha1" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldFileSha1(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFileSha1 is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFileSha1 requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFileSha1: %w", err)
	}
	return oldValue.FileSha1, nil
}

// ResetFileSha1 resets all changes to the "file_sha1" field.
func (m *PsdMutation) ResetFileSha1() {
	m.file_sha1 = nil
}

// SetFileValid sets the "file_valid" field.
func (m *PsdMutation) SetFileValid(i int32) {
	m.file_valid = &i
	m.addfile_valid = nil
}

// FileValid returns the value of the "file_valid" field in the mutation.
func (m *PsdMutation) FileValid() (r int32, exists bool) {
	v := m.file_valid
	if v == nil {
		return
	}
	return *v, true
}

// OldFileValid returns the old "file_valid" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldFileValid(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFileValid is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFileValid requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFileValid: %w", err)
	}
	return oldValue.FileValid, nil
}

// AddFileValid adds i to the "file_valid" field.
func (m *PsdMutation) AddFileValid(i int32) {
	if m.addfile_valid != nil {
		*m.addfile_valid += i
	} else {
		m.addfile_valid = &i
	}
}

// AddedFileValid returns the value that was added to the "file_valid" field in this mutation.
func (m *PsdMutation) AddedFileValid() (r int32, exists bool) {
	v := m.addfile_valid
	if v == nil {
		return
	}
	return *v, true
}

// ResetFileValid resets all changes to the "file_valid" field.
func (m *PsdMutation) ResetFileValid() {
	m.file_valid = nil
	m.addfile_valid = nil
}

// SetWeight sets the "weight" field.
func (m *PsdMutation) SetWeight(i int32) {
	m.weight = &i
	m.addweight = nil
}

// Weight returns the value of the "weight" field in the mutation.
func (m *PsdMutation) Weight() (r int32, exists bool) {
	v := m.weight
	if v == nil {
		return
	}
	return *v, true
}

// OldWeight returns the old "weight" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldWeight(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWeight is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWeight requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWeight: %w", err)
	}
	return oldValue.Weight, nil
}

// AddWeight adds i to the "weight" field.
func (m *PsdMutation) AddWeight(i int32) {
	if m.addweight != nil {
		*m.addweight += i
	} else {
		m.addweight = &i
	}
}

// AddedWeight returns the value that was added to the "weight" field in this mutation.
func (m *PsdMutation) AddedWeight() (r int32, exists bool) {
	v := m.addweight
	if v == nil {
		return
	}
	return *v, true
}

// ResetWeight resets all changes to the "weight" field.
func (m *PsdMutation) ResetWeight() {
	m.weight = nil
	m.addweight = nil
}

// SetIsCover sets the "is_cover" field.
func (m *PsdMutation) SetIsCover(i int32) {
	m.is_cover = &i
	m.addis_cover = nil
}

// IsCover returns the value of the "is_cover" field in the mutation.
func (m *PsdMutation) IsCover() (r int32, exists bool) {
	v := m.is_cover
	if v == nil {
		return
	}
	return *v, true
}

// OldIsCover returns the old "is_cover" field's value of the Psd entity.
// If the Psd object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdMutation) OldIsCover(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsCover is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsCover requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsCover: %w", err)
	}
	return oldValue.IsCover, nil
}

// AddIsCover adds i to the "is_cover" field.
func (m *PsdMutation) AddIsCover(i int32) {
	if m.addis_cover != nil {
		*m.addis_cover += i
	} else {
		m.addis_cover = &i
	}
}

// AddedIsCover returns the value that was added to the "is_cover" field in this mutation.
func (m *PsdMutation) AddedIsCover() (r int32, exists bool) {
	v := m.addis_cover
	if v == nil {
		return
	}
	return *v, true
}

// ResetIsCover resets all changes to the "is_cover" field.
func (m *PsdMutation) ResetIsCover() {
	m.is_cover = nil
	m.addis_cover = nil
}

// Where appends a list predicates to the PsdMutation builder.
func (m *PsdMutation) Where(ps ...predicate.Psd) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PsdMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PsdMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Psd, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PsdMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PsdMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Psd).
func (m *PsdMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PsdMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.created_time != nil {
		fields = append(fields, psd.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, psd.FieldUpdatedTime)
	}
	if m.psd_group_id != nil {
		fields = append(fields, psd.FieldPsdGroupID)
	}
	if m.desc != nil {
		fields = append(fields, psd.FieldDesc)
	}
	if m.file_path != nil {
		fields = append(fields, psd.FieldFilePath)
	}
	if m.file_sha1 != nil {
		fields = append(fields, psd.FieldFileSha1)
	}
	if m.file_valid != nil {
		fields = append(fields, psd.FieldFileValid)
	}
	if m.weight != nil {
		fields = append(fields, psd.FieldWeight)
	}
	if m.is_cover != nil {
		fields = append(fields, psd.FieldIsCover)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PsdMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case psd.FieldCreatedTime:
		return m.CreatedTime()
	case psd.FieldUpdatedTime:
		return m.UpdatedTime()
	case psd.FieldPsdGroupID:
		return m.PsdGroupID()
	case psd.FieldDesc:
		return m.Desc()
	case psd.FieldFilePath:
		return m.FilePath()
	case psd.FieldFileSha1:
		return m.FileSha1()
	case psd.FieldFileValid:
		return m.FileValid()
	case psd.FieldWeight:
		return m.Weight()
	case psd.FieldIsCover:
		return m.IsCover()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PsdMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case psd.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case psd.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case psd.FieldPsdGroupID:
		return m.OldPsdGroupID(ctx)
	case psd.FieldDesc:
		return m.OldDesc(ctx)
	case psd.FieldFilePath:
		return m.OldFilePath(ctx)
	case psd.FieldFileSha1:
		return m.OldFileSha1(ctx)
	case psd.FieldFileValid:
		return m.OldFileValid(ctx)
	case psd.FieldWeight:
		return m.OldWeight(ctx)
	case psd.FieldIsCover:
		return m.OldIsCover(ctx)
	}
	return nil, fmt.Errorf("unknown Psd field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdMutation) SetField(name string, value ent.Value) error {
	switch name {
	case psd.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case psd.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case psd.FieldPsdGroupID:
		v, ok := value.(pq.Int64Array)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPsdGroupID(v)
		return nil
	case psd.FieldDesc:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDesc(v)
		return nil
	case psd.FieldFilePath:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFilePath(v)
		return nil
	case psd.FieldFileSha1:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFileSha1(v)
		return nil
	case psd.FieldFileValid:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFileValid(v)
		return nil
	case psd.FieldWeight:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWeight(v)
		return nil
	case psd.FieldIsCover:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsCover(v)
		return nil
	}
	return fmt.Errorf("unknown Psd field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PsdMutation) AddedFields() []string {
	var fields []string
	if m.addfile_valid != nil {
		fields = append(fields, psd.FieldFileValid)
	}
	if m.addweight != nil {
		fields = append(fields, psd.FieldWeight)
	}
	if m.addis_cover != nil {
		fields = append(fields, psd.FieldIsCover)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PsdMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case psd.FieldFileValid:
		return m.AddedFileValid()
	case psd.FieldWeight:
		return m.AddedWeight()
	case psd.FieldIsCover:
		return m.AddedIsCover()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdMutation) AddField(name string, value ent.Value) error {
	switch name {
	case psd.FieldFileValid:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddFileValid(v)
		return nil
	case psd.FieldWeight:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddWeight(v)
		return nil
	case psd.FieldIsCover:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddIsCover(v)
		return nil
	}
	return fmt.Errorf("unknown Psd numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PsdMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(psd.FieldPsdGroupID) {
		fields = append(fields, psd.FieldPsdGroupID)
	}
	if m.FieldCleared(psd.FieldDesc) {
		fields = append(fields, psd.FieldDesc)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PsdMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PsdMutation) ClearField(name string) error {
	switch name {
	case psd.FieldPsdGroupID:
		m.ClearPsdGroupID()
		return nil
	case psd.FieldDesc:
		m.ClearDesc()
		return nil
	}
	return fmt.Errorf("unknown Psd nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PsdMutation) ResetField(name string) error {
	switch name {
	case psd.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case psd.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case psd.FieldPsdGroupID:
		m.ResetPsdGroupID()
		return nil
	case psd.FieldDesc:
		m.ResetDesc()
		return nil
	case psd.FieldFilePath:
		m.ResetFilePath()
		return nil
	case psd.FieldFileSha1:
		m.ResetFileSha1()
		return nil
	case psd.FieldFileValid:
		m.ResetFileValid()
		return nil
	case psd.FieldWeight:
		m.ResetWeight()
		return nil
	case psd.FieldIsCover:
		m.ResetIsCover()
		return nil
	}
	return fmt.Errorf("unknown Psd field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PsdMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PsdMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PsdMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PsdMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PsdMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PsdMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PsdMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Psd unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PsdMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Psd edge %s", name)
}

// PsdCombineResultMutation represents an operation that mutates the PsdCombineResult nodes in the graph.
type PsdCombineResultMutation struct {
	config
	op            Op
	typ           string
	id            *int64
	created_time  *time.Time
	updated_time  *time.Time
	task_id       *int64
	addtask_id    *int64
	result_url    *string
	weight        *int32
	addweight     *int32
	is_cover      *int32
	addis_cover   *int32
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*PsdCombineResult, error)
	predicates    []predicate.PsdCombineResult
}

var _ ent.Mutation = (*PsdCombineResultMutation)(nil)

// psdcombineresultOption allows management of the mutation configuration using functional options.
type psdcombineresultOption func(*PsdCombineResultMutation)

// newPsdCombineResultMutation creates new mutation for the PsdCombineResult entity.
func newPsdCombineResultMutation(c config, op Op, opts ...psdcombineresultOption) *PsdCombineResultMutation {
	m := &PsdCombineResultMutation{
		config:        c,
		op:            op,
		typ:           TypePsdCombineResult,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPsdCombineResultID sets the ID field of the mutation.
func withPsdCombineResultID(id int64) psdcombineresultOption {
	return func(m *PsdCombineResultMutation) {
		var (
			err   error
			once  sync.Once
			value *PsdCombineResult
		)
		m.oldValue = func(ctx context.Context) (*PsdCombineResult, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().PsdCombineResult.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPsdCombineResult sets the old PsdCombineResult of the mutation.
func withPsdCombineResult(node *PsdCombineResult) psdcombineresultOption {
	return func(m *PsdCombineResultMutation) {
		m.oldValue = func(context.Context) (*PsdCombineResult, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PsdCombineResultMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PsdCombineResultMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of PsdCombineResult entities.
func (m *PsdCombineResultMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PsdCombineResultMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PsdCombineResultMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().PsdCombineResult.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *PsdCombineResultMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *PsdCombineResultMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the PsdCombineResult entity.
// If the PsdCombineResult object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineResultMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *PsdCombineResultMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *PsdCombineResultMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *PsdCombineResultMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the PsdCombineResult entity.
// If the PsdCombineResult object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineResultMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *PsdCombineResultMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetTaskID sets the "task_id" field.
func (m *PsdCombineResultMutation) SetTaskID(i int64) {
	m.task_id = &i
	m.addtask_id = nil
}

// TaskID returns the value of the "task_id" field in the mutation.
func (m *PsdCombineResultMutation) TaskID() (r int64, exists bool) {
	v := m.task_id
	if v == nil {
		return
	}
	return *v, true
}

// OldTaskID returns the old "task_id" field's value of the PsdCombineResult entity.
// If the PsdCombineResult object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineResultMutation) OldTaskID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTaskID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTaskID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTaskID: %w", err)
	}
	return oldValue.TaskID, nil
}

// AddTaskID adds i to the "task_id" field.
func (m *PsdCombineResultMutation) AddTaskID(i int64) {
	if m.addtask_id != nil {
		*m.addtask_id += i
	} else {
		m.addtask_id = &i
	}
}

// AddedTaskID returns the value that was added to the "task_id" field in this mutation.
func (m *PsdCombineResultMutation) AddedTaskID() (r int64, exists bool) {
	v := m.addtask_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetTaskID resets all changes to the "task_id" field.
func (m *PsdCombineResultMutation) ResetTaskID() {
	m.task_id = nil
	m.addtask_id = nil
}

// SetResultURL sets the "result_url" field.
func (m *PsdCombineResultMutation) SetResultURL(s string) {
	m.result_url = &s
}

// ResultURL returns the value of the "result_url" field in the mutation.
func (m *PsdCombineResultMutation) ResultURL() (r string, exists bool) {
	v := m.result_url
	if v == nil {
		return
	}
	return *v, true
}

// OldResultURL returns the old "result_url" field's value of the PsdCombineResult entity.
// If the PsdCombineResult object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineResultMutation) OldResultURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldResultURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldResultURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldResultURL: %w", err)
	}
	return oldValue.ResultURL, nil
}

// ResetResultURL resets all changes to the "result_url" field.
func (m *PsdCombineResultMutation) ResetResultURL() {
	m.result_url = nil
}

// SetWeight sets the "weight" field.
func (m *PsdCombineResultMutation) SetWeight(i int32) {
	m.weight = &i
	m.addweight = nil
}

// Weight returns the value of the "weight" field in the mutation.
func (m *PsdCombineResultMutation) Weight() (r int32, exists bool) {
	v := m.weight
	if v == nil {
		return
	}
	return *v, true
}

// OldWeight returns the old "weight" field's value of the PsdCombineResult entity.
// If the PsdCombineResult object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineResultMutation) OldWeight(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWeight is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWeight requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWeight: %w", err)
	}
	return oldValue.Weight, nil
}

// AddWeight adds i to the "weight" field.
func (m *PsdCombineResultMutation) AddWeight(i int32) {
	if m.addweight != nil {
		*m.addweight += i
	} else {
		m.addweight = &i
	}
}

// AddedWeight returns the value that was added to the "weight" field in this mutation.
func (m *PsdCombineResultMutation) AddedWeight() (r int32, exists bool) {
	v := m.addweight
	if v == nil {
		return
	}
	return *v, true
}

// ResetWeight resets all changes to the "weight" field.
func (m *PsdCombineResultMutation) ResetWeight() {
	m.weight = nil
	m.addweight = nil
}

// SetIsCover sets the "is_cover" field.
func (m *PsdCombineResultMutation) SetIsCover(i int32) {
	m.is_cover = &i
	m.addis_cover = nil
}

// IsCover returns the value of the "is_cover" field in the mutation.
func (m *PsdCombineResultMutation) IsCover() (r int32, exists bool) {
	v := m.is_cover
	if v == nil {
		return
	}
	return *v, true
}

// OldIsCover returns the old "is_cover" field's value of the PsdCombineResult entity.
// If the PsdCombineResult object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineResultMutation) OldIsCover(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIsCover is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIsCover requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIsCover: %w", err)
	}
	return oldValue.IsCover, nil
}

// AddIsCover adds i to the "is_cover" field.
func (m *PsdCombineResultMutation) AddIsCover(i int32) {
	if m.addis_cover != nil {
		*m.addis_cover += i
	} else {
		m.addis_cover = &i
	}
}

// AddedIsCover returns the value that was added to the "is_cover" field in this mutation.
func (m *PsdCombineResultMutation) AddedIsCover() (r int32, exists bool) {
	v := m.addis_cover
	if v == nil {
		return
	}
	return *v, true
}

// ResetIsCover resets all changes to the "is_cover" field.
func (m *PsdCombineResultMutation) ResetIsCover() {
	m.is_cover = nil
	m.addis_cover = nil
}

// Where appends a list predicates to the PsdCombineResultMutation builder.
func (m *PsdCombineResultMutation) Where(ps ...predicate.PsdCombineResult) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PsdCombineResultMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PsdCombineResultMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.PsdCombineResult, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PsdCombineResultMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PsdCombineResultMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (PsdCombineResult).
func (m *PsdCombineResultMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PsdCombineResultMutation) Fields() []string {
	fields := make([]string, 0, 6)
	if m.created_time != nil {
		fields = append(fields, psdcombineresult.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, psdcombineresult.FieldUpdatedTime)
	}
	if m.task_id != nil {
		fields = append(fields, psdcombineresult.FieldTaskID)
	}
	if m.result_url != nil {
		fields = append(fields, psdcombineresult.FieldResultURL)
	}
	if m.weight != nil {
		fields = append(fields, psdcombineresult.FieldWeight)
	}
	if m.is_cover != nil {
		fields = append(fields, psdcombineresult.FieldIsCover)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PsdCombineResultMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case psdcombineresult.FieldCreatedTime:
		return m.CreatedTime()
	case psdcombineresult.FieldUpdatedTime:
		return m.UpdatedTime()
	case psdcombineresult.FieldTaskID:
		return m.TaskID()
	case psdcombineresult.FieldResultURL:
		return m.ResultURL()
	case psdcombineresult.FieldWeight:
		return m.Weight()
	case psdcombineresult.FieldIsCover:
		return m.IsCover()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PsdCombineResultMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case psdcombineresult.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case psdcombineresult.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case psdcombineresult.FieldTaskID:
		return m.OldTaskID(ctx)
	case psdcombineresult.FieldResultURL:
		return m.OldResultURL(ctx)
	case psdcombineresult.FieldWeight:
		return m.OldWeight(ctx)
	case psdcombineresult.FieldIsCover:
		return m.OldIsCover(ctx)
	}
	return nil, fmt.Errorf("unknown PsdCombineResult field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdCombineResultMutation) SetField(name string, value ent.Value) error {
	switch name {
	case psdcombineresult.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case psdcombineresult.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case psdcombineresult.FieldTaskID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTaskID(v)
		return nil
	case psdcombineresult.FieldResultURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetResultURL(v)
		return nil
	case psdcombineresult.FieldWeight:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWeight(v)
		return nil
	case psdcombineresult.FieldIsCover:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIsCover(v)
		return nil
	}
	return fmt.Errorf("unknown PsdCombineResult field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PsdCombineResultMutation) AddedFields() []string {
	var fields []string
	if m.addtask_id != nil {
		fields = append(fields, psdcombineresult.FieldTaskID)
	}
	if m.addweight != nil {
		fields = append(fields, psdcombineresult.FieldWeight)
	}
	if m.addis_cover != nil {
		fields = append(fields, psdcombineresult.FieldIsCover)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PsdCombineResultMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case psdcombineresult.FieldTaskID:
		return m.AddedTaskID()
	case psdcombineresult.FieldWeight:
		return m.AddedWeight()
	case psdcombineresult.FieldIsCover:
		return m.AddedIsCover()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdCombineResultMutation) AddField(name string, value ent.Value) error {
	switch name {
	case psdcombineresult.FieldTaskID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTaskID(v)
		return nil
	case psdcombineresult.FieldWeight:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddWeight(v)
		return nil
	case psdcombineresult.FieldIsCover:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddIsCover(v)
		return nil
	}
	return fmt.Errorf("unknown PsdCombineResult numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PsdCombineResultMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PsdCombineResultMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PsdCombineResultMutation) ClearField(name string) error {
	return fmt.Errorf("unknown PsdCombineResult nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PsdCombineResultMutation) ResetField(name string) error {
	switch name {
	case psdcombineresult.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case psdcombineresult.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case psdcombineresult.FieldTaskID:
		m.ResetTaskID()
		return nil
	case psdcombineresult.FieldResultURL:
		m.ResetResultURL()
		return nil
	case psdcombineresult.FieldWeight:
		m.ResetWeight()
		return nil
	case psdcombineresult.FieldIsCover:
		m.ResetIsCover()
		return nil
	}
	return fmt.Errorf("unknown PsdCombineResult field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PsdCombineResultMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PsdCombineResultMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PsdCombineResultMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PsdCombineResultMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PsdCombineResultMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PsdCombineResultMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PsdCombineResultMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown PsdCombineResult unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PsdCombineResultMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown PsdCombineResult edge %s", name)
}

// PsdCombineTaskMutation represents an operation that mutates the PsdCombineTask nodes in the graph.
type PsdCombineTaskMutation struct {
	config
	op              Op
	typ             string
	id              *int64
	created_time    *time.Time
	updated_time    *time.Time
	psd_group_id    *int64
	addpsd_group_id *int64
	material_url    *string
	reference_id    *int64
	addreference_id *int64
	extra_params    *string
	status          *string
	error_msg       *string
	clearedFields   map[string]struct{}
	done            bool
	oldValue        func(context.Context) (*PsdCombineTask, error)
	predicates      []predicate.PsdCombineTask
}

var _ ent.Mutation = (*PsdCombineTaskMutation)(nil)

// psdcombinetaskOption allows management of the mutation configuration using functional options.
type psdcombinetaskOption func(*PsdCombineTaskMutation)

// newPsdCombineTaskMutation creates new mutation for the PsdCombineTask entity.
func newPsdCombineTaskMutation(c config, op Op, opts ...psdcombinetaskOption) *PsdCombineTaskMutation {
	m := &PsdCombineTaskMutation{
		config:        c,
		op:            op,
		typ:           TypePsdCombineTask,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPsdCombineTaskID sets the ID field of the mutation.
func withPsdCombineTaskID(id int64) psdcombinetaskOption {
	return func(m *PsdCombineTaskMutation) {
		var (
			err   error
			once  sync.Once
			value *PsdCombineTask
		)
		m.oldValue = func(ctx context.Context) (*PsdCombineTask, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().PsdCombineTask.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPsdCombineTask sets the old PsdCombineTask of the mutation.
func withPsdCombineTask(node *PsdCombineTask) psdcombinetaskOption {
	return func(m *PsdCombineTaskMutation) {
		m.oldValue = func(context.Context) (*PsdCombineTask, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PsdCombineTaskMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PsdCombineTaskMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of PsdCombineTask entities.
func (m *PsdCombineTaskMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PsdCombineTaskMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PsdCombineTaskMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().PsdCombineTask.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *PsdCombineTaskMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *PsdCombineTaskMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *PsdCombineTaskMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *PsdCombineTaskMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *PsdCombineTaskMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *PsdCombineTaskMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetPsdGroupID sets the "psd_group_id" field.
func (m *PsdCombineTaskMutation) SetPsdGroupID(i int64) {
	m.psd_group_id = &i
	m.addpsd_group_id = nil
}

// PsdGroupID returns the value of the "psd_group_id" field in the mutation.
func (m *PsdCombineTaskMutation) PsdGroupID() (r int64, exists bool) {
	v := m.psd_group_id
	if v == nil {
		return
	}
	return *v, true
}

// OldPsdGroupID returns the old "psd_group_id" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldPsdGroupID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPsdGroupID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPsdGroupID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPsdGroupID: %w", err)
	}
	return oldValue.PsdGroupID, nil
}

// AddPsdGroupID adds i to the "psd_group_id" field.
func (m *PsdCombineTaskMutation) AddPsdGroupID(i int64) {
	if m.addpsd_group_id != nil {
		*m.addpsd_group_id += i
	} else {
		m.addpsd_group_id = &i
	}
}

// AddedPsdGroupID returns the value that was added to the "psd_group_id" field in this mutation.
func (m *PsdCombineTaskMutation) AddedPsdGroupID() (r int64, exists bool) {
	v := m.addpsd_group_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetPsdGroupID resets all changes to the "psd_group_id" field.
func (m *PsdCombineTaskMutation) ResetPsdGroupID() {
	m.psd_group_id = nil
	m.addpsd_group_id = nil
}

// SetMaterialURL sets the "material_url" field.
func (m *PsdCombineTaskMutation) SetMaterialURL(s string) {
	m.material_url = &s
}

// MaterialURL returns the value of the "material_url" field in the mutation.
func (m *PsdCombineTaskMutation) MaterialURL() (r string, exists bool) {
	v := m.material_url
	if v == nil {
		return
	}
	return *v, true
}

// OldMaterialURL returns the old "material_url" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldMaterialURL(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMaterialURL is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMaterialURL requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMaterialURL: %w", err)
	}
	return oldValue.MaterialURL, nil
}

// ResetMaterialURL resets all changes to the "material_url" field.
func (m *PsdCombineTaskMutation) ResetMaterialURL() {
	m.material_url = nil
}

// SetReferenceID sets the "reference_id" field.
func (m *PsdCombineTaskMutation) SetReferenceID(i int64) {
	m.reference_id = &i
	m.addreference_id = nil
}

// ReferenceID returns the value of the "reference_id" field in the mutation.
func (m *PsdCombineTaskMutation) ReferenceID() (r int64, exists bool) {
	v := m.reference_id
	if v == nil {
		return
	}
	return *v, true
}

// OldReferenceID returns the old "reference_id" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldReferenceID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldReferenceID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldReferenceID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldReferenceID: %w", err)
	}
	return oldValue.ReferenceID, nil
}

// AddReferenceID adds i to the "reference_id" field.
func (m *PsdCombineTaskMutation) AddReferenceID(i int64) {
	if m.addreference_id != nil {
		*m.addreference_id += i
	} else {
		m.addreference_id = &i
	}
}

// AddedReferenceID returns the value that was added to the "reference_id" field in this mutation.
func (m *PsdCombineTaskMutation) AddedReferenceID() (r int64, exists bool) {
	v := m.addreference_id
	if v == nil {
		return
	}
	return *v, true
}

// ClearReferenceID clears the value of the "reference_id" field.
func (m *PsdCombineTaskMutation) ClearReferenceID() {
	m.reference_id = nil
	m.addreference_id = nil
	m.clearedFields[psdcombinetask.FieldReferenceID] = struct{}{}
}

// ReferenceIDCleared returns if the "reference_id" field was cleared in this mutation.
func (m *PsdCombineTaskMutation) ReferenceIDCleared() bool {
	_, ok := m.clearedFields[psdcombinetask.FieldReferenceID]
	return ok
}

// ResetReferenceID resets all changes to the "reference_id" field.
func (m *PsdCombineTaskMutation) ResetReferenceID() {
	m.reference_id = nil
	m.addreference_id = nil
	delete(m.clearedFields, psdcombinetask.FieldReferenceID)
}

// SetExtraParams sets the "extra_params" field.
func (m *PsdCombineTaskMutation) SetExtraParams(s string) {
	m.extra_params = &s
}

// ExtraParams returns the value of the "extra_params" field in the mutation.
func (m *PsdCombineTaskMutation) ExtraParams() (r string, exists bool) {
	v := m.extra_params
	if v == nil {
		return
	}
	return *v, true
}

// OldExtraParams returns the old "extra_params" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldExtraParams(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExtraParams is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExtraParams requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExtraParams: %w", err)
	}
	return oldValue.ExtraParams, nil
}

// ClearExtraParams clears the value of the "extra_params" field.
func (m *PsdCombineTaskMutation) ClearExtraParams() {
	m.extra_params = nil
	m.clearedFields[psdcombinetask.FieldExtraParams] = struct{}{}
}

// ExtraParamsCleared returns if the "extra_params" field was cleared in this mutation.
func (m *PsdCombineTaskMutation) ExtraParamsCleared() bool {
	_, ok := m.clearedFields[psdcombinetask.FieldExtraParams]
	return ok
}

// ResetExtraParams resets all changes to the "extra_params" field.
func (m *PsdCombineTaskMutation) ResetExtraParams() {
	m.extra_params = nil
	delete(m.clearedFields, psdcombinetask.FieldExtraParams)
}

// SetStatus sets the "status" field.
func (m *PsdCombineTaskMutation) SetStatus(s string) {
	m.status = &s
}

// Status returns the value of the "status" field in the mutation.
func (m *PsdCombineTaskMutation) Status() (r string, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldStatus(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *PsdCombineTaskMutation) ResetStatus() {
	m.status = nil
}

// SetErrorMsg sets the "error_msg" field.
func (m *PsdCombineTaskMutation) SetErrorMsg(s string) {
	m.error_msg = &s
}

// ErrorMsg returns the value of the "error_msg" field in the mutation.
func (m *PsdCombineTaskMutation) ErrorMsg() (r string, exists bool) {
	v := m.error_msg
	if v == nil {
		return
	}
	return *v, true
}

// OldErrorMsg returns the old "error_msg" field's value of the PsdCombineTask entity.
// If the PsdCombineTask object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdCombineTaskMutation) OldErrorMsg(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldErrorMsg is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldErrorMsg requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldErrorMsg: %w", err)
	}
	return oldValue.ErrorMsg, nil
}

// ClearErrorMsg clears the value of the "error_msg" field.
func (m *PsdCombineTaskMutation) ClearErrorMsg() {
	m.error_msg = nil
	m.clearedFields[psdcombinetask.FieldErrorMsg] = struct{}{}
}

// ErrorMsgCleared returns if the "error_msg" field was cleared in this mutation.
func (m *PsdCombineTaskMutation) ErrorMsgCleared() bool {
	_, ok := m.clearedFields[psdcombinetask.FieldErrorMsg]
	return ok
}

// ResetErrorMsg resets all changes to the "error_msg" field.
func (m *PsdCombineTaskMutation) ResetErrorMsg() {
	m.error_msg = nil
	delete(m.clearedFields, psdcombinetask.FieldErrorMsg)
}

// Where appends a list predicates to the PsdCombineTaskMutation builder.
func (m *PsdCombineTaskMutation) Where(ps ...predicate.PsdCombineTask) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PsdCombineTaskMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PsdCombineTaskMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.PsdCombineTask, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PsdCombineTaskMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PsdCombineTaskMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (PsdCombineTask).
func (m *PsdCombineTaskMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PsdCombineTaskMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.created_time != nil {
		fields = append(fields, psdcombinetask.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, psdcombinetask.FieldUpdatedTime)
	}
	if m.psd_group_id != nil {
		fields = append(fields, psdcombinetask.FieldPsdGroupID)
	}
	if m.material_url != nil {
		fields = append(fields, psdcombinetask.FieldMaterialURL)
	}
	if m.reference_id != nil {
		fields = append(fields, psdcombinetask.FieldReferenceID)
	}
	if m.extra_params != nil {
		fields = append(fields, psdcombinetask.FieldExtraParams)
	}
	if m.status != nil {
		fields = append(fields, psdcombinetask.FieldStatus)
	}
	if m.error_msg != nil {
		fields = append(fields, psdcombinetask.FieldErrorMsg)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PsdCombineTaskMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case psdcombinetask.FieldCreatedTime:
		return m.CreatedTime()
	case psdcombinetask.FieldUpdatedTime:
		return m.UpdatedTime()
	case psdcombinetask.FieldPsdGroupID:
		return m.PsdGroupID()
	case psdcombinetask.FieldMaterialURL:
		return m.MaterialURL()
	case psdcombinetask.FieldReferenceID:
		return m.ReferenceID()
	case psdcombinetask.FieldExtraParams:
		return m.ExtraParams()
	case psdcombinetask.FieldStatus:
		return m.Status()
	case psdcombinetask.FieldErrorMsg:
		return m.ErrorMsg()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PsdCombineTaskMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case psdcombinetask.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case psdcombinetask.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case psdcombinetask.FieldPsdGroupID:
		return m.OldPsdGroupID(ctx)
	case psdcombinetask.FieldMaterialURL:
		return m.OldMaterialURL(ctx)
	case psdcombinetask.FieldReferenceID:
		return m.OldReferenceID(ctx)
	case psdcombinetask.FieldExtraParams:
		return m.OldExtraParams(ctx)
	case psdcombinetask.FieldStatus:
		return m.OldStatus(ctx)
	case psdcombinetask.FieldErrorMsg:
		return m.OldErrorMsg(ctx)
	}
	return nil, fmt.Errorf("unknown PsdCombineTask field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdCombineTaskMutation) SetField(name string, value ent.Value) error {
	switch name {
	case psdcombinetask.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case psdcombinetask.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case psdcombinetask.FieldPsdGroupID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPsdGroupID(v)
		return nil
	case psdcombinetask.FieldMaterialURL:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMaterialURL(v)
		return nil
	case psdcombinetask.FieldReferenceID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetReferenceID(v)
		return nil
	case psdcombinetask.FieldExtraParams:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExtraParams(v)
		return nil
	case psdcombinetask.FieldStatus:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case psdcombinetask.FieldErrorMsg:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetErrorMsg(v)
		return nil
	}
	return fmt.Errorf("unknown PsdCombineTask field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PsdCombineTaskMutation) AddedFields() []string {
	var fields []string
	if m.addpsd_group_id != nil {
		fields = append(fields, psdcombinetask.FieldPsdGroupID)
	}
	if m.addreference_id != nil {
		fields = append(fields, psdcombinetask.FieldReferenceID)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PsdCombineTaskMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case psdcombinetask.FieldPsdGroupID:
		return m.AddedPsdGroupID()
	case psdcombinetask.FieldReferenceID:
		return m.AddedReferenceID()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdCombineTaskMutation) AddField(name string, value ent.Value) error {
	switch name {
	case psdcombinetask.FieldPsdGroupID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPsdGroupID(v)
		return nil
	case psdcombinetask.FieldReferenceID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddReferenceID(v)
		return nil
	}
	return fmt.Errorf("unknown PsdCombineTask numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PsdCombineTaskMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(psdcombinetask.FieldReferenceID) {
		fields = append(fields, psdcombinetask.FieldReferenceID)
	}
	if m.FieldCleared(psdcombinetask.FieldExtraParams) {
		fields = append(fields, psdcombinetask.FieldExtraParams)
	}
	if m.FieldCleared(psdcombinetask.FieldErrorMsg) {
		fields = append(fields, psdcombinetask.FieldErrorMsg)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PsdCombineTaskMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PsdCombineTaskMutation) ClearField(name string) error {
	switch name {
	case psdcombinetask.FieldReferenceID:
		m.ClearReferenceID()
		return nil
	case psdcombinetask.FieldExtraParams:
		m.ClearExtraParams()
		return nil
	case psdcombinetask.FieldErrorMsg:
		m.ClearErrorMsg()
		return nil
	}
	return fmt.Errorf("unknown PsdCombineTask nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PsdCombineTaskMutation) ResetField(name string) error {
	switch name {
	case psdcombinetask.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case psdcombinetask.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case psdcombinetask.FieldPsdGroupID:
		m.ResetPsdGroupID()
		return nil
	case psdcombinetask.FieldMaterialURL:
		m.ResetMaterialURL()
		return nil
	case psdcombinetask.FieldReferenceID:
		m.ResetReferenceID()
		return nil
	case psdcombinetask.FieldExtraParams:
		m.ResetExtraParams()
		return nil
	case psdcombinetask.FieldStatus:
		m.ResetStatus()
		return nil
	case psdcombinetask.FieldErrorMsg:
		m.ResetErrorMsg()
		return nil
	}
	return fmt.Errorf("unknown PsdCombineTask field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PsdCombineTaskMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PsdCombineTaskMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PsdCombineTaskMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PsdCombineTaskMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PsdCombineTaskMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PsdCombineTaskMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PsdCombineTaskMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown PsdCombineTask unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PsdCombineTaskMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown PsdCombineTask edge %s", name)
}

// PsdGroupMutation represents an operation that mutates the PsdGroup nodes in the graph.
type PsdGroupMutation struct {
	config
	op            Op
	typ           string
	id            *int64
	created_time  *time.Time
	updated_time  *time.Time
	name          *string
	desc          *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*PsdGroup, error)
	predicates    []predicate.PsdGroup
}

var _ ent.Mutation = (*PsdGroupMutation)(nil)

// psdgroupOption allows management of the mutation configuration using functional options.
type psdgroupOption func(*PsdGroupMutation)

// newPsdGroupMutation creates new mutation for the PsdGroup entity.
func newPsdGroupMutation(c config, op Op, opts ...psdgroupOption) *PsdGroupMutation {
	m := &PsdGroupMutation{
		config:        c,
		op:            op,
		typ:           TypePsdGroup,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withPsdGroupID sets the ID field of the mutation.
func withPsdGroupID(id int64) psdgroupOption {
	return func(m *PsdGroupMutation) {
		var (
			err   error
			once  sync.Once
			value *PsdGroup
		)
		m.oldValue = func(ctx context.Context) (*PsdGroup, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().PsdGroup.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withPsdGroup sets the old PsdGroup of the mutation.
func withPsdGroup(node *PsdGroup) psdgroupOption {
	return func(m *PsdGroupMutation) {
		m.oldValue = func(context.Context) (*PsdGroup, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m PsdGroupMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m PsdGroupMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of PsdGroup entities.
func (m *PsdGroupMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *PsdGroupMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *PsdGroupMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().PsdGroup.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *PsdGroupMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *PsdGroupMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the PsdGroup entity.
// If the PsdGroup object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdGroupMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *PsdGroupMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *PsdGroupMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *PsdGroupMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the PsdGroup entity.
// If the PsdGroup object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdGroupMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *PsdGroupMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetName sets the "name" field.
func (m *PsdGroupMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *PsdGroupMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the PsdGroup entity.
// If the PsdGroup object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdGroupMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *PsdGroupMutation) ResetName() {
	m.name = nil
}

// SetDesc sets the "desc" field.
func (m *PsdGroupMutation) SetDesc(s string) {
	m.desc = &s
}

// Desc returns the value of the "desc" field in the mutation.
func (m *PsdGroupMutation) Desc() (r string, exists bool) {
	v := m.desc
	if v == nil {
		return
	}
	return *v, true
}

// OldDesc returns the old "desc" field's value of the PsdGroup entity.
// If the PsdGroup object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *PsdGroupMutation) OldDesc(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDesc is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDesc requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDesc: %w", err)
	}
	return oldValue.Desc, nil
}

// ClearDesc clears the value of the "desc" field.
func (m *PsdGroupMutation) ClearDesc() {
	m.desc = nil
	m.clearedFields[psdgroup.FieldDesc] = struct{}{}
}

// DescCleared returns if the "desc" field was cleared in this mutation.
func (m *PsdGroupMutation) DescCleared() bool {
	_, ok := m.clearedFields[psdgroup.FieldDesc]
	return ok
}

// ResetDesc resets all changes to the "desc" field.
func (m *PsdGroupMutation) ResetDesc() {
	m.desc = nil
	delete(m.clearedFields, psdgroup.FieldDesc)
}

// Where appends a list predicates to the PsdGroupMutation builder.
func (m *PsdGroupMutation) Where(ps ...predicate.PsdGroup) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the PsdGroupMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *PsdGroupMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.PsdGroup, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *PsdGroupMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *PsdGroupMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (PsdGroup).
func (m *PsdGroupMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *PsdGroupMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.created_time != nil {
		fields = append(fields, psdgroup.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, psdgroup.FieldUpdatedTime)
	}
	if m.name != nil {
		fields = append(fields, psdgroup.FieldName)
	}
	if m.desc != nil {
		fields = append(fields, psdgroup.FieldDesc)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *PsdGroupMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case psdgroup.FieldCreatedTime:
		return m.CreatedTime()
	case psdgroup.FieldUpdatedTime:
		return m.UpdatedTime()
	case psdgroup.FieldName:
		return m.Name()
	case psdgroup.FieldDesc:
		return m.Desc()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *PsdGroupMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case psdgroup.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case psdgroup.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case psdgroup.FieldName:
		return m.OldName(ctx)
	case psdgroup.FieldDesc:
		return m.OldDesc(ctx)
	}
	return nil, fmt.Errorf("unknown PsdGroup field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdGroupMutation) SetField(name string, value ent.Value) error {
	switch name {
	case psdgroup.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case psdgroup.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case psdgroup.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case psdgroup.FieldDesc:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDesc(v)
		return nil
	}
	return fmt.Errorf("unknown PsdGroup field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *PsdGroupMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *PsdGroupMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *PsdGroupMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown PsdGroup numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *PsdGroupMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(psdgroup.FieldDesc) {
		fields = append(fields, psdgroup.FieldDesc)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *PsdGroupMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *PsdGroupMutation) ClearField(name string) error {
	switch name {
	case psdgroup.FieldDesc:
		m.ClearDesc()
		return nil
	}
	return fmt.Errorf("unknown PsdGroup nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *PsdGroupMutation) ResetField(name string) error {
	switch name {
	case psdgroup.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case psdgroup.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case psdgroup.FieldName:
		m.ResetName()
		return nil
	case psdgroup.FieldDesc:
		m.ResetDesc()
		return nil
	}
	return fmt.Errorf("unknown PsdGroup field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *PsdGroupMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *PsdGroupMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *PsdGroupMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *PsdGroupMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *PsdGroupMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *PsdGroupMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *PsdGroupMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown PsdGroup unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *PsdGroupMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown PsdGroup edge %s", name)
}

// RegisteredTrademarkMutation represents an operation that mutates the RegisteredTrademark nodes in the graph.
type RegisteredTrademarkMutation struct {
	config
	op            Op
	typ           string
	id            *int64
	created_time  *time.Time
	updated_time  *time.Time
	tid           *int32
	addtid        *int32
	data          *json.RawMessage
	appenddata    json.RawMessage
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*RegisteredTrademark, error)
	predicates    []predicate.RegisteredTrademark
}

var _ ent.Mutation = (*RegisteredTrademarkMutation)(nil)

// registeredtrademarkOption allows management of the mutation configuration using functional options.
type registeredtrademarkOption func(*RegisteredTrademarkMutation)

// newRegisteredTrademarkMutation creates new mutation for the RegisteredTrademark entity.
func newRegisteredTrademarkMutation(c config, op Op, opts ...registeredtrademarkOption) *RegisteredTrademarkMutation {
	m := &RegisteredTrademarkMutation{
		config:        c,
		op:            op,
		typ:           TypeRegisteredTrademark,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withRegisteredTrademarkID sets the ID field of the mutation.
func withRegisteredTrademarkID(id int64) registeredtrademarkOption {
	return func(m *RegisteredTrademarkMutation) {
		var (
			err   error
			once  sync.Once
			value *RegisteredTrademark
		)
		m.oldValue = func(ctx context.Context) (*RegisteredTrademark, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().RegisteredTrademark.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withRegisteredTrademark sets the old RegisteredTrademark of the mutation.
func withRegisteredTrademark(node *RegisteredTrademark) registeredtrademarkOption {
	return func(m *RegisteredTrademarkMutation) {
		m.oldValue = func(context.Context) (*RegisteredTrademark, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m RegisteredTrademarkMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m RegisteredTrademarkMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("db1: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of RegisteredTrademark entities.
func (m *RegisteredTrademarkMutation) SetID(id int64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *RegisteredTrademarkMutation) ID() (id int64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *RegisteredTrademarkMutation) IDs(ctx context.Context) ([]int64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().RegisteredTrademark.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedTime sets the "created_time" field.
func (m *RegisteredTrademarkMutation) SetCreatedTime(t time.Time) {
	m.created_time = &t
}

// CreatedTime returns the value of the "created_time" field in the mutation.
func (m *RegisteredTrademarkMutation) CreatedTime() (r time.Time, exists bool) {
	v := m.created_time
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedTime returns the old "created_time" field's value of the RegisteredTrademark entity.
// If the RegisteredTrademark object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RegisteredTrademarkMutation) OldCreatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedTime: %w", err)
	}
	return oldValue.CreatedTime, nil
}

// ResetCreatedTime resets all changes to the "created_time" field.
func (m *RegisteredTrademarkMutation) ResetCreatedTime() {
	m.created_time = nil
}

// SetUpdatedTime sets the "updated_time" field.
func (m *RegisteredTrademarkMutation) SetUpdatedTime(t time.Time) {
	m.updated_time = &t
}

// UpdatedTime returns the value of the "updated_time" field in the mutation.
func (m *RegisteredTrademarkMutation) UpdatedTime() (r time.Time, exists bool) {
	v := m.updated_time
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedTime returns the old "updated_time" field's value of the RegisteredTrademark entity.
// If the RegisteredTrademark object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RegisteredTrademarkMutation) OldUpdatedTime(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedTime: %w", err)
	}
	return oldValue.UpdatedTime, nil
}

// ResetUpdatedTime resets all changes to the "updated_time" field.
func (m *RegisteredTrademarkMutation) ResetUpdatedTime() {
	m.updated_time = nil
}

// SetTid sets the "tid" field.
func (m *RegisteredTrademarkMutation) SetTid(i int32) {
	m.tid = &i
	m.addtid = nil
}

// Tid returns the value of the "tid" field in the mutation.
func (m *RegisteredTrademarkMutation) Tid() (r int32, exists bool) {
	v := m.tid
	if v == nil {
		return
	}
	return *v, true
}

// OldTid returns the old "tid" field's value of the RegisteredTrademark entity.
// If the RegisteredTrademark object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RegisteredTrademarkMutation) OldTid(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTid is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTid requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTid: %w", err)
	}
	return oldValue.Tid, nil
}

// AddTid adds i to the "tid" field.
func (m *RegisteredTrademarkMutation) AddTid(i int32) {
	if m.addtid != nil {
		*m.addtid += i
	} else {
		m.addtid = &i
	}
}

// AddedTid returns the value that was added to the "tid" field in this mutation.
func (m *RegisteredTrademarkMutation) AddedTid() (r int32, exists bool) {
	v := m.addtid
	if v == nil {
		return
	}
	return *v, true
}

// ResetTid resets all changes to the "tid" field.
func (m *RegisteredTrademarkMutation) ResetTid() {
	m.tid = nil
	m.addtid = nil
}

// SetData sets the "data" field.
func (m *RegisteredTrademarkMutation) SetData(jm json.RawMessage) {
	m.data = &jm
	m.appenddata = nil
}

// Data returns the value of the "data" field in the mutation.
func (m *RegisteredTrademarkMutation) Data() (r json.RawMessage, exists bool) {
	v := m.data
	if v == nil {
		return
	}
	return *v, true
}

// OldData returns the old "data" field's value of the RegisteredTrademark entity.
// If the RegisteredTrademark object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *RegisteredTrademarkMutation) OldData(ctx context.Context) (v json.RawMessage, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldData is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldData requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldData: %w", err)
	}
	return oldValue.Data, nil
}

// AppendData adds jm to the "data" field.
func (m *RegisteredTrademarkMutation) AppendData(jm json.RawMessage) {
	m.appenddata = append(m.appenddata, jm...)
}

// AppendedData returns the list of values that were appended to the "data" field in this mutation.
func (m *RegisteredTrademarkMutation) AppendedData() (json.RawMessage, bool) {
	if len(m.appenddata) == 0 {
		return nil, false
	}
	return m.appenddata, true
}

// ResetData resets all changes to the "data" field.
func (m *RegisteredTrademarkMutation) ResetData() {
	m.data = nil
	m.appenddata = nil
}

// Where appends a list predicates to the RegisteredTrademarkMutation builder.
func (m *RegisteredTrademarkMutation) Where(ps ...predicate.RegisteredTrademark) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the RegisteredTrademarkMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *RegisteredTrademarkMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.RegisteredTrademark, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *RegisteredTrademarkMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *RegisteredTrademarkMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (RegisteredTrademark).
func (m *RegisteredTrademarkMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *RegisteredTrademarkMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.created_time != nil {
		fields = append(fields, registeredtrademark.FieldCreatedTime)
	}
	if m.updated_time != nil {
		fields = append(fields, registeredtrademark.FieldUpdatedTime)
	}
	if m.tid != nil {
		fields = append(fields, registeredtrademark.FieldTid)
	}
	if m.data != nil {
		fields = append(fields, registeredtrademark.FieldData)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *RegisteredTrademarkMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case registeredtrademark.FieldCreatedTime:
		return m.CreatedTime()
	case registeredtrademark.FieldUpdatedTime:
		return m.UpdatedTime()
	case registeredtrademark.FieldTid:
		return m.Tid()
	case registeredtrademark.FieldData:
		return m.Data()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *RegisteredTrademarkMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case registeredtrademark.FieldCreatedTime:
		return m.OldCreatedTime(ctx)
	case registeredtrademark.FieldUpdatedTime:
		return m.OldUpdatedTime(ctx)
	case registeredtrademark.FieldTid:
		return m.OldTid(ctx)
	case registeredtrademark.FieldData:
		return m.OldData(ctx)
	}
	return nil, fmt.Errorf("unknown RegisteredTrademark field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *RegisteredTrademarkMutation) SetField(name string, value ent.Value) error {
	switch name {
	case registeredtrademark.FieldCreatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedTime(v)
		return nil
	case registeredtrademark.FieldUpdatedTime:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedTime(v)
		return nil
	case registeredtrademark.FieldTid:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTid(v)
		return nil
	case registeredtrademark.FieldData:
		v, ok := value.(json.RawMessage)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetData(v)
		return nil
	}
	return fmt.Errorf("unknown RegisteredTrademark field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *RegisteredTrademarkMutation) AddedFields() []string {
	var fields []string
	if m.addtid != nil {
		fields = append(fields, registeredtrademark.FieldTid)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *RegisteredTrademarkMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case registeredtrademark.FieldTid:
		return m.AddedTid()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *RegisteredTrademarkMutation) AddField(name string, value ent.Value) error {
	switch name {
	case registeredtrademark.FieldTid:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTid(v)
		return nil
	}
	return fmt.Errorf("unknown RegisteredTrademark numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *RegisteredTrademarkMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *RegisteredTrademarkMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *RegisteredTrademarkMutation) ClearField(name string) error {
	return fmt.Errorf("unknown RegisteredTrademark nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *RegisteredTrademarkMutation) ResetField(name string) error {
	switch name {
	case registeredtrademark.FieldCreatedTime:
		m.ResetCreatedTime()
		return nil
	case registeredtrademark.FieldUpdatedTime:
		m.ResetUpdatedTime()
		return nil
	case registeredtrademark.FieldTid:
		m.ResetTid()
		return nil
	case registeredtrademark.FieldData:
		m.ResetData()
		return nil
	}
	return fmt.Errorf("unknown RegisteredTrademark field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *RegisteredTrademarkMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *RegisteredTrademarkMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *RegisteredTrademarkMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *RegisteredTrademarkMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *RegisteredTrademarkMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *RegisteredTrademarkMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *RegisteredTrademarkMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown RegisteredTrademark unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *RegisteredTrademarkMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown RegisteredTrademark edge %s", name)
}
