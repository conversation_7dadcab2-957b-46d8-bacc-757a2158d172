// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// Administrator is the predicate function for administrator builders.
type Administrator func(*sql.Selector)

// Material is the predicate function for material builders.
type Material func(*sql.Selector)

// MaterialGroup is the predicate function for materialgroup builders.
type MaterialGroup func(*sql.Selector)

// ProductItem is the predicate function for productitem builders.
type ProductItem func(*sql.Selector)

// Psd is the predicate function for psd builders.
type Psd func(*sql.Selector)

// PsdCombineResult is the predicate function for psdcombineresult builders.
type PsdCombineResult func(*sql.Selector)

// PsdCombineTask is the predicate function for psdcombinetask builders.
type PsdCombineTask func(*sql.Selector)

// PsdGroup is the predicate function for psdgroup builders.
type PsdGroup func(*sql.Selector)

// RegisteredTrademark is the predicate function for registeredtrademark builders.
type RegisteredTrademark func(*sql.Selector)
