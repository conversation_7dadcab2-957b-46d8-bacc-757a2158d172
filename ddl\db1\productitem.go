// Code generated by ent, DO NOT EDIT.

package db1

import (
	"encoding/json"
	"fmt"
	"omnix/ddl/db1/productitem"
	"omnix/genpb/msgpb"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// ProductItem is the model entity for the ProductItem schema.
type ProductItem struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedTime time.Time `json:"created_time,omitempty"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty"`
	// 数据唯一
	Hash string `json:"hash,omitempty"`
	// 产品ID
	ItemID string `json:"item_id,omitempty"`
	// 所属平台
	Platform string `json:"platform,omitempty"`
	// 格式化的产品数据
	Spec *msgpb.ProductSpec `json:"spec,omitempty"`
	// 产品过滤属性
	Filter *msgpb.ProductFilter `json:"filter,omitempty"`
	// 数据处理标记
	Mark []string `json:"mark,omitempty"`
	// 原始数据
	Data         string `json:"data,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ProductItem) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case productitem.FieldSpec, productitem.FieldFilter, productitem.FieldMark:
			values[i] = new([]byte)
		case productitem.FieldID:
			values[i] = new(sql.NullInt64)
		case productitem.FieldHash, productitem.FieldItemID, productitem.FieldPlatform, productitem.FieldData:
			values[i] = new(sql.NullString)
		case productitem.FieldCreatedTime, productitem.FieldUpdatedTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ProductItem fields.
func (_m *ProductItem) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case productitem.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case productitem.FieldCreatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_time", values[i])
			} else if value.Valid {
				_m.CreatedTime = value.Time
			}
		case productitem.FieldUpdatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_time", values[i])
			} else if value.Valid {
				_m.UpdatedTime = value.Time
			}
		case productitem.FieldHash:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field hash", values[i])
			} else if value.Valid {
				_m.Hash = value.String
			}
		case productitem.FieldItemID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field item_id", values[i])
			} else if value.Valid {
				_m.ItemID = value.String
			}
		case productitem.FieldPlatform:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field platform", values[i])
			} else if value.Valid {
				_m.Platform = value.String
			}
		case productitem.FieldSpec:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field spec", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &_m.Spec); err != nil {
					return fmt.Errorf("unmarshal field spec: %w", err)
				}
			}
		case productitem.FieldFilter:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field filter", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &_m.Filter); err != nil {
					return fmt.Errorf("unmarshal field filter: %w", err)
				}
			}
		case productitem.FieldMark:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field mark", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &_m.Mark); err != nil {
					return fmt.Errorf("unmarshal field mark: %w", err)
				}
			}
		case productitem.FieldData:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field data", values[i])
			} else if value.Valid {
				_m.Data = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ProductItem.
// This includes values selected through modifiers, order, etc.
func (_m *ProductItem) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this ProductItem.
// Note that you need to call ProductItem.Unwrap() before calling this method if this ProductItem
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *ProductItem) Update() *ProductItemUpdateOne {
	return NewProductItemClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the ProductItem entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *ProductItem) Unwrap() *ProductItem {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("db1: ProductItem is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *ProductItem) String() string {
	var builder strings.Builder
	builder.WriteString("ProductItem(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("created_time=")
	builder.WriteString(_m.CreatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_time=")
	builder.WriteString(_m.UpdatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("hash=")
	builder.WriteString(_m.Hash)
	builder.WriteString(", ")
	builder.WriteString("item_id=")
	builder.WriteString(_m.ItemID)
	builder.WriteString(", ")
	builder.WriteString("platform=")
	builder.WriteString(_m.Platform)
	builder.WriteString(", ")
	builder.WriteString("spec=")
	builder.WriteString(fmt.Sprintf("%v", _m.Spec))
	builder.WriteString(", ")
	builder.WriteString("filter=")
	builder.WriteString(fmt.Sprintf("%v", _m.Filter))
	builder.WriteString(", ")
	builder.WriteString("mark=")
	builder.WriteString(fmt.Sprintf("%v", _m.Mark))
	builder.WriteString(", ")
	builder.WriteString("data=")
	builder.WriteString(_m.Data)
	builder.WriteByte(')')
	return builder.String()
}

// ProductItems is a parsable slice of ProductItem.
type ProductItems []*ProductItem
