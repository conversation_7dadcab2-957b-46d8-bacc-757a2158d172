// Code generated by ent, DO NOT EDIT.

package productitem

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the productitem type in the database.
	Label = "product_item"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedTime holds the string denoting the created_time field in the database.
	FieldCreatedTime = "created_time"
	// FieldUpdatedTime holds the string denoting the updated_time field in the database.
	FieldUpdatedTime = "updated_time"
	// FieldHash holds the string denoting the hash field in the database.
	FieldHash = "hash"
	// FieldItemID holds the string denoting the item_id field in the database.
	FieldItemID = "item_id"
	// FieldPlatform holds the string denoting the platform field in the database.
	FieldPlatform = "platform"
	// FieldSpec holds the string denoting the spec field in the database.
	FieldSpec = "spec"
	// FieldFilter holds the string denoting the filter field in the database.
	FieldFilter = "filter"
	// FieldMark holds the string denoting the mark field in the database.
	FieldMark = "mark"
	// FieldData holds the string denoting the data field in the database.
	FieldData = "data"
	// Table holds the table name of the productitem in the database.
	Table = "product_items"
)

// Columns holds all SQL columns for productitem fields.
var Columns = []string{
	FieldID,
	FieldCreatedTime,
	FieldUpdatedTime,
	FieldHash,
	FieldItemID,
	FieldPlatform,
	FieldSpec,
	FieldFilter,
	FieldMark,
	FieldData,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedTime holds the default value on creation for the "created_time" field.
	DefaultCreatedTime func() time.Time
	// DefaultUpdatedTime holds the default value on creation for the "updated_time" field.
	DefaultUpdatedTime func() time.Time
	// UpdateDefaultUpdatedTime holds the default value on update for the "updated_time" field.
	UpdateDefaultUpdatedTime func() time.Time
	// HashValidator is a validator for the "hash" field. It is called by the builders before save.
	HashValidator func(string) error
	// ItemIDValidator is a validator for the "item_id" field. It is called by the builders before save.
	ItemIDValidator func(string) error
	// PlatformValidator is a validator for the "platform" field. It is called by the builders before save.
	PlatformValidator func(string) error
	// DataValidator is a validator for the "data" field. It is called by the builders before save.
	DataValidator func(string) error
)

// OrderOption defines the ordering options for the ProductItem queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedTime orders the results by the created_time field.
func ByCreatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedTime, opts...).ToFunc()
}

// ByUpdatedTime orders the results by the updated_time field.
func ByUpdatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedTime, opts...).ToFunc()
}

// ByHash orders the results by the hash field.
func ByHash(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHash, opts...).ToFunc()
}

// ByItemID orders the results by the item_id field.
func ByItemID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldItemID, opts...).ToFunc()
}

// ByPlatform orders the results by the platform field.
func ByPlatform(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPlatform, opts...).ToFunc()
}

// ByData orders the results by the data field.
func ByData(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldData, opts...).ToFunc()
}
