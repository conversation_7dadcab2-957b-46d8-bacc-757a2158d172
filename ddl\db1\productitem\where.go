// Code generated by ent, DO NOT EDIT.

package productitem

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldUpdatedTime, v))
}

// Hash applies equality check predicate on the "hash" field. It's identical to HashEQ.
func Hash(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldHash, v))
}

// ItemID applies equality check predicate on the "item_id" field. It's identical to ItemIDEQ.
func ItemID(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldItemID, v))
}

// Platform applies equality check predicate on the "platform" field. It's identical to PlatformEQ.
func Platform(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldPlatform, v))
}

// Data applies equality check predicate on the "data" field. It's identical to DataEQ.
func Data(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldData, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLTE(FieldUpdatedTime, v))
}

// HashEQ applies the EQ predicate on the "hash" field.
func HashEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldHash, v))
}

// HashNEQ applies the NEQ predicate on the "hash" field.
func HashNEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNEQ(FieldHash, v))
}

// HashIn applies the In predicate on the "hash" field.
func HashIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIn(FieldHash, vs...))
}

// HashNotIn applies the NotIn predicate on the "hash" field.
func HashNotIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotIn(FieldHash, vs...))
}

// HashGT applies the GT predicate on the "hash" field.
func HashGT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGT(FieldHash, v))
}

// HashGTE applies the GTE predicate on the "hash" field.
func HashGTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGTE(FieldHash, v))
}

// HashLT applies the LT predicate on the "hash" field.
func HashLT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLT(FieldHash, v))
}

// HashLTE applies the LTE predicate on the "hash" field.
func HashLTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLTE(FieldHash, v))
}

// HashContains applies the Contains predicate on the "hash" field.
func HashContains(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContains(FieldHash, v))
}

// HashHasPrefix applies the HasPrefix predicate on the "hash" field.
func HashHasPrefix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasPrefix(FieldHash, v))
}

// HashHasSuffix applies the HasSuffix predicate on the "hash" field.
func HashHasSuffix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasSuffix(FieldHash, v))
}

// HashEqualFold applies the EqualFold predicate on the "hash" field.
func HashEqualFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEqualFold(FieldHash, v))
}

// HashContainsFold applies the ContainsFold predicate on the "hash" field.
func HashContainsFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContainsFold(FieldHash, v))
}

// ItemIDEQ applies the EQ predicate on the "item_id" field.
func ItemIDEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldItemID, v))
}

// ItemIDNEQ applies the NEQ predicate on the "item_id" field.
func ItemIDNEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNEQ(FieldItemID, v))
}

// ItemIDIn applies the In predicate on the "item_id" field.
func ItemIDIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIn(FieldItemID, vs...))
}

// ItemIDNotIn applies the NotIn predicate on the "item_id" field.
func ItemIDNotIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotIn(FieldItemID, vs...))
}

// ItemIDGT applies the GT predicate on the "item_id" field.
func ItemIDGT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGT(FieldItemID, v))
}

// ItemIDGTE applies the GTE predicate on the "item_id" field.
func ItemIDGTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGTE(FieldItemID, v))
}

// ItemIDLT applies the LT predicate on the "item_id" field.
func ItemIDLT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLT(FieldItemID, v))
}

// ItemIDLTE applies the LTE predicate on the "item_id" field.
func ItemIDLTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLTE(FieldItemID, v))
}

// ItemIDContains applies the Contains predicate on the "item_id" field.
func ItemIDContains(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContains(FieldItemID, v))
}

// ItemIDHasPrefix applies the HasPrefix predicate on the "item_id" field.
func ItemIDHasPrefix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasPrefix(FieldItemID, v))
}

// ItemIDHasSuffix applies the HasSuffix predicate on the "item_id" field.
func ItemIDHasSuffix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasSuffix(FieldItemID, v))
}

// ItemIDEqualFold applies the EqualFold predicate on the "item_id" field.
func ItemIDEqualFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEqualFold(FieldItemID, v))
}

// ItemIDContainsFold applies the ContainsFold predicate on the "item_id" field.
func ItemIDContainsFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContainsFold(FieldItemID, v))
}

// PlatformEQ applies the EQ predicate on the "platform" field.
func PlatformEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldPlatform, v))
}

// PlatformNEQ applies the NEQ predicate on the "platform" field.
func PlatformNEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNEQ(FieldPlatform, v))
}

// PlatformIn applies the In predicate on the "platform" field.
func PlatformIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIn(FieldPlatform, vs...))
}

// PlatformNotIn applies the NotIn predicate on the "platform" field.
func PlatformNotIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotIn(FieldPlatform, vs...))
}

// PlatformGT applies the GT predicate on the "platform" field.
func PlatformGT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGT(FieldPlatform, v))
}

// PlatformGTE applies the GTE predicate on the "platform" field.
func PlatformGTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGTE(FieldPlatform, v))
}

// PlatformLT applies the LT predicate on the "platform" field.
func PlatformLT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLT(FieldPlatform, v))
}

// PlatformLTE applies the LTE predicate on the "platform" field.
func PlatformLTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLTE(FieldPlatform, v))
}

// PlatformContains applies the Contains predicate on the "platform" field.
func PlatformContains(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContains(FieldPlatform, v))
}

// PlatformHasPrefix applies the HasPrefix predicate on the "platform" field.
func PlatformHasPrefix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasPrefix(FieldPlatform, v))
}

// PlatformHasSuffix applies the HasSuffix predicate on the "platform" field.
func PlatformHasSuffix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasSuffix(FieldPlatform, v))
}

// PlatformEqualFold applies the EqualFold predicate on the "platform" field.
func PlatformEqualFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEqualFold(FieldPlatform, v))
}

// PlatformContainsFold applies the ContainsFold predicate on the "platform" field.
func PlatformContainsFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContainsFold(FieldPlatform, v))
}

// MarkIsNil applies the IsNil predicate on the "mark" field.
func MarkIsNil() predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIsNull(FieldMark))
}

// MarkNotNil applies the NotNil predicate on the "mark" field.
func MarkNotNil() predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotNull(FieldMark))
}

// DataEQ applies the EQ predicate on the "data" field.
func DataEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEQ(FieldData, v))
}

// DataNEQ applies the NEQ predicate on the "data" field.
func DataNEQ(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNEQ(FieldData, v))
}

// DataIn applies the In predicate on the "data" field.
func DataIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldIn(FieldData, vs...))
}

// DataNotIn applies the NotIn predicate on the "data" field.
func DataNotIn(vs ...string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldNotIn(FieldData, vs...))
}

// DataGT applies the GT predicate on the "data" field.
func DataGT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGT(FieldData, v))
}

// DataGTE applies the GTE predicate on the "data" field.
func DataGTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldGTE(FieldData, v))
}

// DataLT applies the LT predicate on the "data" field.
func DataLT(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLT(FieldData, v))
}

// DataLTE applies the LTE predicate on the "data" field.
func DataLTE(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldLTE(FieldData, v))
}

// DataContains applies the Contains predicate on the "data" field.
func DataContains(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContains(FieldData, v))
}

// DataHasPrefix applies the HasPrefix predicate on the "data" field.
func DataHasPrefix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasPrefix(FieldData, v))
}

// DataHasSuffix applies the HasSuffix predicate on the "data" field.
func DataHasSuffix(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldHasSuffix(FieldData, v))
}

// DataEqualFold applies the EqualFold predicate on the "data" field.
func DataEqualFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldEqualFold(FieldData, v))
}

// DataContainsFold applies the ContainsFold predicate on the "data" field.
func DataContainsFold(v string) predicate.ProductItem {
	return predicate.ProductItem(sql.FieldContainsFold(FieldData, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ProductItem) predicate.ProductItem {
	return predicate.ProductItem(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ProductItem) predicate.ProductItem {
	return predicate.ProductItem(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ProductItem) predicate.ProductItem {
	return predicate.ProductItem(sql.NotPredicates(p))
}
