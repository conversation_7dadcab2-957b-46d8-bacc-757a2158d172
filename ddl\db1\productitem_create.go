// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/productitem"
	"omnix/genpb/msgpb"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ProductItemCreate is the builder for creating a ProductItem entity.
type ProductItemCreate struct {
	config
	mutation *ProductItemMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *ProductItemCreate) SetCreatedTime(v time.Time) *ProductItemCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *ProductItemCreate) SetNillableCreatedTime(v *time.Time) *ProductItemCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *ProductItemCreate) SetUpdatedTime(v time.Time) *ProductItemCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *ProductItemCreate) SetNillableUpdatedTime(v *time.Time) *ProductItemCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetHash sets the "hash" field.
func (_c *ProductItemCreate) SetHash(v string) *ProductItemCreate {
	_c.mutation.SetHash(v)
	return _c
}

// SetItemID sets the "item_id" field.
func (_c *ProductItemCreate) SetItemID(v string) *ProductItemCreate {
	_c.mutation.SetItemID(v)
	return _c
}

// SetPlatform sets the "platform" field.
func (_c *ProductItemCreate) SetPlatform(v string) *ProductItemCreate {
	_c.mutation.SetPlatform(v)
	return _c
}

// SetSpec sets the "spec" field.
func (_c *ProductItemCreate) SetSpec(v *msgpb.ProductSpec) *ProductItemCreate {
	_c.mutation.SetSpec(v)
	return _c
}

// SetFilter sets the "filter" field.
func (_c *ProductItemCreate) SetFilter(v *msgpb.ProductFilter) *ProductItemCreate {
	_c.mutation.SetFilter(v)
	return _c
}

// SetMark sets the "mark" field.
func (_c *ProductItemCreate) SetMark(v []string) *ProductItemCreate {
	_c.mutation.SetMark(v)
	return _c
}

// SetData sets the "data" field.
func (_c *ProductItemCreate) SetData(v string) *ProductItemCreate {
	_c.mutation.SetData(v)
	return _c
}

// SetID sets the "id" field.
func (_c *ProductItemCreate) SetID(v int64) *ProductItemCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the ProductItemMutation object of the builder.
func (_c *ProductItemCreate) Mutation() *ProductItemMutation {
	return _c.mutation
}

// Save creates the ProductItem in the database.
func (_c *ProductItemCreate) Save(ctx context.Context) (*ProductItem, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *ProductItemCreate) SaveX(ctx context.Context) *ProductItem {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ProductItemCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ProductItemCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *ProductItemCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := productitem.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := productitem.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *ProductItemCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "ProductItem.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "ProductItem.updated_time"`)}
	}
	if _, ok := _c.mutation.Hash(); !ok {
		return &ValidationError{Name: "hash", err: errors.New(`db1: missing required field "ProductItem.hash"`)}
	}
	if v, ok := _c.mutation.Hash(); ok {
		if err := productitem.HashValidator(v); err != nil {
			return &ValidationError{Name: "hash", err: fmt.Errorf(`db1: validator failed for field "ProductItem.hash": %w`, err)}
		}
	}
	if _, ok := _c.mutation.ItemID(); !ok {
		return &ValidationError{Name: "item_id", err: errors.New(`db1: missing required field "ProductItem.item_id"`)}
	}
	if v, ok := _c.mutation.ItemID(); ok {
		if err := productitem.ItemIDValidator(v); err != nil {
			return &ValidationError{Name: "item_id", err: fmt.Errorf(`db1: validator failed for field "ProductItem.item_id": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Platform(); !ok {
		return &ValidationError{Name: "platform", err: errors.New(`db1: missing required field "ProductItem.platform"`)}
	}
	if v, ok := _c.mutation.Platform(); ok {
		if err := productitem.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`db1: validator failed for field "ProductItem.platform": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Spec(); !ok {
		return &ValidationError{Name: "spec", err: errors.New(`db1: missing required field "ProductItem.spec"`)}
	}
	if _, ok := _c.mutation.Filter(); !ok {
		return &ValidationError{Name: "filter", err: errors.New(`db1: missing required field "ProductItem.filter"`)}
	}
	if _, ok := _c.mutation.Data(); !ok {
		return &ValidationError{Name: "data", err: errors.New(`db1: missing required field "ProductItem.data"`)}
	}
	if v, ok := _c.mutation.Data(); ok {
		if err := productitem.DataValidator(v); err != nil {
			return &ValidationError{Name: "data", err: fmt.Errorf(`db1: validator failed for field "ProductItem.data": %w`, err)}
		}
	}
	return nil
}

func (_c *ProductItemCreate) sqlSave(ctx context.Context) (*ProductItem, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *ProductItemCreate) createSpec() (*ProductItem, *sqlgraph.CreateSpec) {
	var (
		_node = &ProductItem{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(productitem.Table, sqlgraph.NewFieldSpec(productitem.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(productitem.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(productitem.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.Hash(); ok {
		_spec.SetField(productitem.FieldHash, field.TypeString, value)
		_node.Hash = value
	}
	if value, ok := _c.mutation.ItemID(); ok {
		_spec.SetField(productitem.FieldItemID, field.TypeString, value)
		_node.ItemID = value
	}
	if value, ok := _c.mutation.Platform(); ok {
		_spec.SetField(productitem.FieldPlatform, field.TypeString, value)
		_node.Platform = value
	}
	if value, ok := _c.mutation.Spec(); ok {
		_spec.SetField(productitem.FieldSpec, field.TypeJSON, value)
		_node.Spec = value
	}
	if value, ok := _c.mutation.Filter(); ok {
		_spec.SetField(productitem.FieldFilter, field.TypeJSON, value)
		_node.Filter = value
	}
	if value, ok := _c.mutation.Mark(); ok {
		_spec.SetField(productitem.FieldMark, field.TypeJSON, value)
		_node.Mark = value
	}
	if value, ok := _c.mutation.Data(); ok {
		_spec.SetField(productitem.FieldData, field.TypeString, value)
		_node.Data = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ProductItem.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ProductItemUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *ProductItemCreate) OnConflict(opts ...sql.ConflictOption) *ProductItemUpsertOne {
	_c.conflict = opts
	return &ProductItemUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ProductItem.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ProductItemCreate) OnConflictColumns(columns ...string) *ProductItemUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ProductItemUpsertOne{
		create: _c,
	}
}

type (
	// ProductItemUpsertOne is the builder for "upsert"-ing
	//  one ProductItem node.
	ProductItemUpsertOne struct {
		create *ProductItemCreate
	}

	// ProductItemUpsert is the "OnConflict" setter.
	ProductItemUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *ProductItemUpsert) SetUpdatedTime(v time.Time) *ProductItemUpsert {
	u.Set(productitem.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdateUpdatedTime() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldUpdatedTime)
	return u
}

// SetHash sets the "hash" field.
func (u *ProductItemUpsert) SetHash(v string) *ProductItemUpsert {
	u.Set(productitem.FieldHash, v)
	return u
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdateHash() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldHash)
	return u
}

// SetItemID sets the "item_id" field.
func (u *ProductItemUpsert) SetItemID(v string) *ProductItemUpsert {
	u.Set(productitem.FieldItemID, v)
	return u
}

// UpdateItemID sets the "item_id" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdateItemID() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldItemID)
	return u
}

// SetPlatform sets the "platform" field.
func (u *ProductItemUpsert) SetPlatform(v string) *ProductItemUpsert {
	u.Set(productitem.FieldPlatform, v)
	return u
}

// UpdatePlatform sets the "platform" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdatePlatform() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldPlatform)
	return u
}

// SetSpec sets the "spec" field.
func (u *ProductItemUpsert) SetSpec(v *msgpb.ProductSpec) *ProductItemUpsert {
	u.Set(productitem.FieldSpec, v)
	return u
}

// UpdateSpec sets the "spec" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdateSpec() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldSpec)
	return u
}

// SetFilter sets the "filter" field.
func (u *ProductItemUpsert) SetFilter(v *msgpb.ProductFilter) *ProductItemUpsert {
	u.Set(productitem.FieldFilter, v)
	return u
}

// UpdateFilter sets the "filter" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdateFilter() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldFilter)
	return u
}

// SetMark sets the "mark" field.
func (u *ProductItemUpsert) SetMark(v []string) *ProductItemUpsert {
	u.Set(productitem.FieldMark, v)
	return u
}

// UpdateMark sets the "mark" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdateMark() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldMark)
	return u
}

// ClearMark clears the value of the "mark" field.
func (u *ProductItemUpsert) ClearMark() *ProductItemUpsert {
	u.SetNull(productitem.FieldMark)
	return u
}

// SetData sets the "data" field.
func (u *ProductItemUpsert) SetData(v string) *ProductItemUpsert {
	u.Set(productitem.FieldData, v)
	return u
}

// UpdateData sets the "data" field to the value that was provided on create.
func (u *ProductItemUpsert) UpdateData() *ProductItemUpsert {
	u.SetExcluded(productitem.FieldData)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.ProductItem.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(productitem.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ProductItemUpsertOne) UpdateNewValues() *ProductItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(productitem.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(productitem.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ProductItem.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ProductItemUpsertOne) Ignore() *ProductItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ProductItemUpsertOne) DoNothing() *ProductItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ProductItemCreate.OnConflict
// documentation for more info.
func (u *ProductItemUpsertOne) Update(set func(*ProductItemUpsert)) *ProductItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ProductItemUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *ProductItemUpsertOne) SetUpdatedTime(v time.Time) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdateUpdatedTime() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetHash sets the "hash" field.
func (u *ProductItemUpsertOne) SetHash(v string) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetHash(v)
	})
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdateHash() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateHash()
	})
}

// SetItemID sets the "item_id" field.
func (u *ProductItemUpsertOne) SetItemID(v string) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetItemID(v)
	})
}

// UpdateItemID sets the "item_id" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdateItemID() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateItemID()
	})
}

// SetPlatform sets the "platform" field.
func (u *ProductItemUpsertOne) SetPlatform(v string) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetPlatform(v)
	})
}

// UpdatePlatform sets the "platform" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdatePlatform() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdatePlatform()
	})
}

// SetSpec sets the "spec" field.
func (u *ProductItemUpsertOne) SetSpec(v *msgpb.ProductSpec) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetSpec(v)
	})
}

// UpdateSpec sets the "spec" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdateSpec() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateSpec()
	})
}

// SetFilter sets the "filter" field.
func (u *ProductItemUpsertOne) SetFilter(v *msgpb.ProductFilter) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetFilter(v)
	})
}

// UpdateFilter sets the "filter" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdateFilter() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateFilter()
	})
}

// SetMark sets the "mark" field.
func (u *ProductItemUpsertOne) SetMark(v []string) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetMark(v)
	})
}

// UpdateMark sets the "mark" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdateMark() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateMark()
	})
}

// ClearMark clears the value of the "mark" field.
func (u *ProductItemUpsertOne) ClearMark() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.ClearMark()
	})
}

// SetData sets the "data" field.
func (u *ProductItemUpsertOne) SetData(v string) *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetData(v)
	})
}

// UpdateData sets the "data" field to the value that was provided on create.
func (u *ProductItemUpsertOne) UpdateData() *ProductItemUpsertOne {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateData()
	})
}

// Exec executes the query.
func (u *ProductItemUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for ProductItemCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ProductItemUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ProductItemUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ProductItemUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ProductItemCreateBulk is the builder for creating many ProductItem entities in bulk.
type ProductItemCreateBulk struct {
	config
	err      error
	builders []*ProductItemCreate
	conflict []sql.ConflictOption
}

// Save creates the ProductItem entities in the database.
func (_c *ProductItemCreateBulk) Save(ctx context.Context) ([]*ProductItem, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*ProductItem, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ProductItemMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *ProductItemCreateBulk) SaveX(ctx context.Context) []*ProductItem {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *ProductItemCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *ProductItemCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ProductItem.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ProductItemUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *ProductItemCreateBulk) OnConflict(opts ...sql.ConflictOption) *ProductItemUpsertBulk {
	_c.conflict = opts
	return &ProductItemUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ProductItem.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *ProductItemCreateBulk) OnConflictColumns(columns ...string) *ProductItemUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &ProductItemUpsertBulk{
		create: _c,
	}
}

// ProductItemUpsertBulk is the builder for "upsert"-ing
// a bulk of ProductItem nodes.
type ProductItemUpsertBulk struct {
	create *ProductItemCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.ProductItem.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(productitem.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ProductItemUpsertBulk) UpdateNewValues() *ProductItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(productitem.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(productitem.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ProductItem.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ProductItemUpsertBulk) Ignore() *ProductItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ProductItemUpsertBulk) DoNothing() *ProductItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ProductItemCreateBulk.OnConflict
// documentation for more info.
func (u *ProductItemUpsertBulk) Update(set func(*ProductItemUpsert)) *ProductItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ProductItemUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *ProductItemUpsertBulk) SetUpdatedTime(v time.Time) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdateUpdatedTime() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetHash sets the "hash" field.
func (u *ProductItemUpsertBulk) SetHash(v string) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetHash(v)
	})
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdateHash() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateHash()
	})
}

// SetItemID sets the "item_id" field.
func (u *ProductItemUpsertBulk) SetItemID(v string) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetItemID(v)
	})
}

// UpdateItemID sets the "item_id" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdateItemID() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateItemID()
	})
}

// SetPlatform sets the "platform" field.
func (u *ProductItemUpsertBulk) SetPlatform(v string) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetPlatform(v)
	})
}

// UpdatePlatform sets the "platform" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdatePlatform() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdatePlatform()
	})
}

// SetSpec sets the "spec" field.
func (u *ProductItemUpsertBulk) SetSpec(v *msgpb.ProductSpec) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetSpec(v)
	})
}

// UpdateSpec sets the "spec" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdateSpec() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateSpec()
	})
}

// SetFilter sets the "filter" field.
func (u *ProductItemUpsertBulk) SetFilter(v *msgpb.ProductFilter) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetFilter(v)
	})
}

// UpdateFilter sets the "filter" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdateFilter() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateFilter()
	})
}

// SetMark sets the "mark" field.
func (u *ProductItemUpsertBulk) SetMark(v []string) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetMark(v)
	})
}

// UpdateMark sets the "mark" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdateMark() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateMark()
	})
}

// ClearMark clears the value of the "mark" field.
func (u *ProductItemUpsertBulk) ClearMark() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.ClearMark()
	})
}

// SetData sets the "data" field.
func (u *ProductItemUpsertBulk) SetData(v string) *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.SetData(v)
	})
}

// UpdateData sets the "data" field to the value that was provided on create.
func (u *ProductItemUpsertBulk) UpdateData() *ProductItemUpsertBulk {
	return u.Update(func(s *ProductItemUpsert) {
		s.UpdateData()
	})
}

// Exec executes the query.
func (u *ProductItemUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the ProductItemCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for ProductItemCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ProductItemUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
