// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/productitem"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ProductItemDelete is the builder for deleting a ProductItem entity.
type ProductItemDelete struct {
	config
	hooks    []Hook
	mutation *ProductItemMutation
}

// Where appends a list predicates to the ProductItemDelete builder.
func (_d *ProductItemDelete) Where(ps ...predicate.ProductItem) *ProductItemDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *ProductItemDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ProductItemDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *ProductItemDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(productitem.Table, sqlgraph.NewFieldSpec(productitem.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the ProductItemDelete builder.
func (_d *ProductItemDelete) WhereCreatedTime(v time.Time) *ProductItemDelete {
	_d.Where(productitem.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the ProductItemDelete builder.
func (_d *ProductItemDelete) WhereUpdatedTime(v time.Time) *ProductItemDelete {
	_d.Where(productitem.UpdatedTime(v))
	return _d
}

// WhereHash applies equality check predicate to the ProductItemDelete builder.
func (_d *ProductItemDelete) WhereHash(v string) *ProductItemDelete {
	_d.Where(productitem.Hash(v))
	return _d
}

// WhereItemID applies equality check predicate to the ProductItemDelete builder.
func (_d *ProductItemDelete) WhereItemID(v string) *ProductItemDelete {
	_d.Where(productitem.ItemID(v))
	return _d
}

// WherePlatform applies equality check predicate to the ProductItemDelete builder.
func (_d *ProductItemDelete) WherePlatform(v string) *ProductItemDelete {
	_d.Where(productitem.Platform(v))
	return _d
}

// WhereData applies equality check predicate to the ProductItemDelete builder.
func (_d *ProductItemDelete) WhereData(v string) *ProductItemDelete {
	_d.Where(productitem.Data(v))
	return _d
}

// ProductItemDeleteOne is the builder for deleting a single ProductItem entity.
type ProductItemDeleteOne struct {
	_d *ProductItemDelete
}

// Where appends a list predicates to the ProductItemDelete builder.
func (_d *ProductItemDeleteOne) Where(ps ...predicate.ProductItem) *ProductItemDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *ProductItemDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{productitem.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *ProductItemDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
