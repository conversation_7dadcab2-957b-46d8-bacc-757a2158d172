// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"fmt"
	"math"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/productitem"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ProductItemQuery is the builder for querying ProductItem entities.
type ProductItemQuery struct {
	config
	ctx        *QueryContext
	order      []productitem.OrderOption
	inters     []Interceptor
	predicates []predicate.ProductItem
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ProductItemQuery builder.
func (_q *ProductItemQuery) Where(ps ...predicate.ProductItem) *ProductItemQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *ProductItemQuery) Limit(limit int) *ProductItemQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *ProductItemQuery) Offset(offset int) *ProductItemQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *ProductItemQuery) Unique(unique bool) *ProductItemQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *ProductItemQuery) Order(o ...productitem.OrderOption) *ProductItemQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// First returns the first ProductItem entity from the query.
// Returns a *NotFoundError when no ProductItem was found.
func (_q *ProductItemQuery) First(ctx context.Context) (*ProductItem, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{productitem.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *ProductItemQuery) FirstX(ctx context.Context) *ProductItem {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ProductItem ID from the query.
// Returns a *NotFoundError when no ProductItem ID was found.
func (_q *ProductItemQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{productitem.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *ProductItemQuery) FirstIDX(ctx context.Context) int64 {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ProductItem entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ProductItem entity is found.
// Returns a *NotFoundError when no ProductItem entities are found.
func (_q *ProductItemQuery) Only(ctx context.Context) (*ProductItem, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{productitem.Label}
	default:
		return nil, &NotSingularError{productitem.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *ProductItemQuery) OnlyX(ctx context.Context) *ProductItem {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ProductItem ID in the query.
// Returns a *NotSingularError when more than one ProductItem ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *ProductItemQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{productitem.Label}
	default:
		err = &NotSingularError{productitem.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *ProductItemQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ProductItems.
func (_q *ProductItemQuery) All(ctx context.Context) ([]*ProductItem, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ProductItem, *ProductItemQuery]()
	return withInterceptors[[]*ProductItem](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *ProductItemQuery) AllX(ctx context.Context) []*ProductItem {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ProductItem IDs.
func (_q *ProductItemQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(productitem.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *ProductItemQuery) IDsX(ctx context.Context) []int64 {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *ProductItemQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*ProductItemQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *ProductItemQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *ProductItemQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("db1: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *ProductItemQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ProductItemQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *ProductItemQuery) Clone() *ProductItemQuery {
	if _q == nil {
		return nil
	}
	return &ProductItemQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]productitem.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.ProductItem{}, _q.predicates...),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ProductItem.Query().
//		GroupBy(productitem.FieldCreatedTime).
//		Aggregate(db1.Count()).
//		Scan(ctx, &v)
func (_q *ProductItemQuery) GroupBy(field string, fields ...string) *ProductItemGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ProductItemGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = productitem.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//	}
//
//	client.ProductItem.Query().
//		Select(productitem.FieldCreatedTime).
//		Scan(ctx, &v)
func (_q *ProductItemQuery) Select(fields ...string) *ProductItemSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &ProductItemSelect{ProductItemQuery: _q}
	sbuild.label = productitem.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ProductItemSelect configured with the given aggregations.
func (_q *ProductItemQuery) Aggregate(fns ...AggregateFunc) *ProductItemSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *ProductItemQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("db1: uninitialized interceptor (forgotten import db1/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !productitem.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *ProductItemQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ProductItem, error) {
	var (
		nodes = []*ProductItem{}
		_spec = _q.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ProductItem).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ProductItem{config: _q.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (_q *ProductItemQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *ProductItemQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(productitem.Table, productitem.Columns, sqlgraph.NewFieldSpec(productitem.FieldID, field.TypeInt64))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, productitem.FieldID)
		for i := range fields {
			if fields[i] != productitem.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *ProductItemQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(productitem.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = productitem.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WhereCreatedTime applies equality check predicate on the "created_time" field
func (_m *ProductItemQuery) WhereCreatedTime(v time.Time) *ProductItemQuery {
	_m.Where(productitem.CreatedTime(v))
	return _m
}

// WhereUpdatedTime applies equality check predicate on the "updated_time" field
func (_m *ProductItemQuery) WhereUpdatedTime(v time.Time) *ProductItemQuery {
	_m.Where(productitem.UpdatedTime(v))
	return _m
}

// WhereHash applies equality check predicate on the "hash" field
func (_m *ProductItemQuery) WhereHash(v string) *ProductItemQuery {
	_m.Where(productitem.Hash(v))
	return _m
}

// WhereItemID applies equality check predicate on the "item_id" field
func (_m *ProductItemQuery) WhereItemID(v string) *ProductItemQuery {
	_m.Where(productitem.ItemID(v))
	return _m
}

// WherePlatform applies equality check predicate on the "platform" field
func (_m *ProductItemQuery) WherePlatform(v string) *ProductItemQuery {
	_m.Where(productitem.Platform(v))
	return _m
}

// WhereData applies equality check predicate on the "data" field
func (_m *ProductItemQuery) WhereData(v string) *ProductItemQuery {
	_m.Where(productitem.Data(v))
	return _m
}

// ProductItemGroupBy is the group-by builder for ProductItem entities.
type ProductItemGroupBy struct {
	selector
	build *ProductItemQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *ProductItemGroupBy) Aggregate(fns ...AggregateFunc) *ProductItemGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *ProductItemGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ProductItemQuery, *ProductItemGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *ProductItemGroupBy) sqlScan(ctx context.Context, root *ProductItemQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ProductItemSelect is the builder for selecting fields of ProductItem entities.
type ProductItemSelect struct {
	*ProductItemQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *ProductItemSelect) Aggregate(fns ...AggregateFunc) *ProductItemSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *ProductItemSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ProductItemQuery, *ProductItemSelect](ctx, _s.ProductItemQuery, _s, _s.inters, v)
}

func (_s *ProductItemSelect) sqlScan(ctx context.Context, root *ProductItemQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
