// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/productitem"
	"omnix/genpb/msgpb"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// ProductItemUpdate is the builder for updating ProductItem entities.
type ProductItemUpdate struct {
	config
	hooks    []Hook
	mutation *ProductItemMutation
}

// Where appends a list predicates to the ProductItemUpdate builder.
func (_u *ProductItemUpdate) Where(ps ...predicate.ProductItem) *ProductItemUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *ProductItemUpdate) SetUpdatedTime(v time.Time) *ProductItemUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetHash sets the "hash" field.
func (_u *ProductItemUpdate) SetHash(v string) *ProductItemUpdate {
	_u.mutation.SetHash(v)
	return _u
}

// SetNillableHash sets the "hash" field if the given value is not nil.
func (_u *ProductItemUpdate) SetNillableHash(v *string) *ProductItemUpdate {
	if v != nil {
		_u.SetHash(*v)
	}
	return _u
}

// SetItemID sets the "item_id" field.
func (_u *ProductItemUpdate) SetItemID(v string) *ProductItemUpdate {
	_u.mutation.SetItemID(v)
	return _u
}

// SetNillableItemID sets the "item_id" field if the given value is not nil.
func (_u *ProductItemUpdate) SetNillableItemID(v *string) *ProductItemUpdate {
	if v != nil {
		_u.SetItemID(*v)
	}
	return _u
}

// SetPlatform sets the "platform" field.
func (_u *ProductItemUpdate) SetPlatform(v string) *ProductItemUpdate {
	_u.mutation.SetPlatform(v)
	return _u
}

// SetNillablePlatform sets the "platform" field if the given value is not nil.
func (_u *ProductItemUpdate) SetNillablePlatform(v *string) *ProductItemUpdate {
	if v != nil {
		_u.SetPlatform(*v)
	}
	return _u
}

// SetSpec sets the "spec" field.
func (_u *ProductItemUpdate) SetSpec(v *msgpb.ProductSpec) *ProductItemUpdate {
	_u.mutation.SetSpec(v)
	return _u
}

// SetFilter sets the "filter" field.
func (_u *ProductItemUpdate) SetFilter(v *msgpb.ProductFilter) *ProductItemUpdate {
	_u.mutation.SetFilter(v)
	return _u
}

// SetMark sets the "mark" field.
func (_u *ProductItemUpdate) SetMark(v []string) *ProductItemUpdate {
	_u.mutation.SetMark(v)
	return _u
}

// AppendMark appends value to the "mark" field.
func (_u *ProductItemUpdate) AppendMark(v []string) *ProductItemUpdate {
	_u.mutation.AppendMark(v)
	return _u
}

// ClearMark clears the value of the "mark" field.
func (_u *ProductItemUpdate) ClearMark() *ProductItemUpdate {
	_u.mutation.ClearMark()
	return _u
}

// SetData sets the "data" field.
func (_u *ProductItemUpdate) SetData(v string) *ProductItemUpdate {
	_u.mutation.SetData(v)
	return _u
}

// SetNillableData sets the "data" field if the given value is not nil.
func (_u *ProductItemUpdate) SetNillableData(v *string) *ProductItemUpdate {
	if v != nil {
		_u.SetData(*v)
	}
	return _u
}

// Mutation returns the ProductItemMutation object of the builder.
func (_u *ProductItemUpdate) Mutation() *ProductItemMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *ProductItemUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ProductItemUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *ProductItemUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ProductItemUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *ProductItemUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := productitem.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *ProductItemUpdate) check() error {
	if v, ok := _u.mutation.Hash(); ok {
		if err := productitem.HashValidator(v); err != nil {
			return &ValidationError{Name: "hash", err: fmt.Errorf(`db1: validator failed for field "ProductItem.hash": %w`, err)}
		}
	}
	if v, ok := _u.mutation.ItemID(); ok {
		if err := productitem.ItemIDValidator(v); err != nil {
			return &ValidationError{Name: "item_id", err: fmt.Errorf(`db1: validator failed for field "ProductItem.item_id": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Platform(); ok {
		if err := productitem.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`db1: validator failed for field "ProductItem.platform": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Data(); ok {
		if err := productitem.DataValidator(v); err != nil {
			return &ValidationError{Name: "data", err: fmt.Errorf(`db1: validator failed for field "ProductItem.data": %w`, err)}
		}
	}
	return nil
}

func (_u *ProductItemUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(productitem.Table, productitem.Columns, sqlgraph.NewFieldSpec(productitem.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(productitem.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Hash(); ok {
		_spec.SetField(productitem.FieldHash, field.TypeString, value)
	}
	if value, ok := _u.mutation.ItemID(); ok {
		_spec.SetField(productitem.FieldItemID, field.TypeString, value)
	}
	if value, ok := _u.mutation.Platform(); ok {
		_spec.SetField(productitem.FieldPlatform, field.TypeString, value)
	}
	if value, ok := _u.mutation.Spec(); ok {
		_spec.SetField(productitem.FieldSpec, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.Filter(); ok {
		_spec.SetField(productitem.FieldFilter, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.Mark(); ok {
		_spec.SetField(productitem.FieldMark, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.AppendedMark(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, productitem.FieldMark, value)
		})
	}
	if _u.mutation.MarkCleared() {
		_spec.ClearField(productitem.FieldMark, field.TypeJSON)
	}
	if value, ok := _u.mutation.Data(); ok {
		_spec.SetField(productitem.FieldData, field.TypeString, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{productitem.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// ProductItemUpdateOne is the builder for updating a single ProductItem entity.
type ProductItemUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ProductItemMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *ProductItemUpdateOne) SetUpdatedTime(v time.Time) *ProductItemUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetHash sets the "hash" field.
func (_u *ProductItemUpdateOne) SetHash(v string) *ProductItemUpdateOne {
	_u.mutation.SetHash(v)
	return _u
}

// SetNillableHash sets the "hash" field if the given value is not nil.
func (_u *ProductItemUpdateOne) SetNillableHash(v *string) *ProductItemUpdateOne {
	if v != nil {
		_u.SetHash(*v)
	}
	return _u
}

// SetItemID sets the "item_id" field.
func (_u *ProductItemUpdateOne) SetItemID(v string) *ProductItemUpdateOne {
	_u.mutation.SetItemID(v)
	return _u
}

// SetNillableItemID sets the "item_id" field if the given value is not nil.
func (_u *ProductItemUpdateOne) SetNillableItemID(v *string) *ProductItemUpdateOne {
	if v != nil {
		_u.SetItemID(*v)
	}
	return _u
}

// SetPlatform sets the "platform" field.
func (_u *ProductItemUpdateOne) SetPlatform(v string) *ProductItemUpdateOne {
	_u.mutation.SetPlatform(v)
	return _u
}

// SetNillablePlatform sets the "platform" field if the given value is not nil.
func (_u *ProductItemUpdateOne) SetNillablePlatform(v *string) *ProductItemUpdateOne {
	if v != nil {
		_u.SetPlatform(*v)
	}
	return _u
}

// SetSpec sets the "spec" field.
func (_u *ProductItemUpdateOne) SetSpec(v *msgpb.ProductSpec) *ProductItemUpdateOne {
	_u.mutation.SetSpec(v)
	return _u
}

// SetFilter sets the "filter" field.
func (_u *ProductItemUpdateOne) SetFilter(v *msgpb.ProductFilter) *ProductItemUpdateOne {
	_u.mutation.SetFilter(v)
	return _u
}

// SetMark sets the "mark" field.
func (_u *ProductItemUpdateOne) SetMark(v []string) *ProductItemUpdateOne {
	_u.mutation.SetMark(v)
	return _u
}

// AppendMark appends value to the "mark" field.
func (_u *ProductItemUpdateOne) AppendMark(v []string) *ProductItemUpdateOne {
	_u.mutation.AppendMark(v)
	return _u
}

// ClearMark clears the value of the "mark" field.
func (_u *ProductItemUpdateOne) ClearMark() *ProductItemUpdateOne {
	_u.mutation.ClearMark()
	return _u
}

// SetData sets the "data" field.
func (_u *ProductItemUpdateOne) SetData(v string) *ProductItemUpdateOne {
	_u.mutation.SetData(v)
	return _u
}

// SetNillableData sets the "data" field if the given value is not nil.
func (_u *ProductItemUpdateOne) SetNillableData(v *string) *ProductItemUpdateOne {
	if v != nil {
		_u.SetData(*v)
	}
	return _u
}

// Mutation returns the ProductItemMutation object of the builder.
func (_u *ProductItemUpdateOne) Mutation() *ProductItemMutation {
	return _u.mutation
}

// Where appends a list predicates to the ProductItemUpdate builder.
func (_u *ProductItemUpdateOne) Where(ps ...predicate.ProductItem) *ProductItemUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *ProductItemUpdateOne) Select(field string, fields ...string) *ProductItemUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated ProductItem entity.
func (_u *ProductItemUpdateOne) Save(ctx context.Context) (*ProductItem, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *ProductItemUpdateOne) SaveX(ctx context.Context) *ProductItem {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *ProductItemUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *ProductItemUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *ProductItemUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := productitem.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *ProductItemUpdateOne) check() error {
	if v, ok := _u.mutation.Hash(); ok {
		if err := productitem.HashValidator(v); err != nil {
			return &ValidationError{Name: "hash", err: fmt.Errorf(`db1: validator failed for field "ProductItem.hash": %w`, err)}
		}
	}
	if v, ok := _u.mutation.ItemID(); ok {
		if err := productitem.ItemIDValidator(v); err != nil {
			return &ValidationError{Name: "item_id", err: fmt.Errorf(`db1: validator failed for field "ProductItem.item_id": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Platform(); ok {
		if err := productitem.PlatformValidator(v); err != nil {
			return &ValidationError{Name: "platform", err: fmt.Errorf(`db1: validator failed for field "ProductItem.platform": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Data(); ok {
		if err := productitem.DataValidator(v); err != nil {
			return &ValidationError{Name: "data", err: fmt.Errorf(`db1: validator failed for field "ProductItem.data": %w`, err)}
		}
	}
	return nil
}

func (_u *ProductItemUpdateOne) sqlSave(ctx context.Context) (_node *ProductItem, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(productitem.Table, productitem.Columns, sqlgraph.NewFieldSpec(productitem.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "ProductItem.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, productitem.FieldID)
		for _, f := range fields {
			if !productitem.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != productitem.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(productitem.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Hash(); ok {
		_spec.SetField(productitem.FieldHash, field.TypeString, value)
	}
	if value, ok := _u.mutation.ItemID(); ok {
		_spec.SetField(productitem.FieldItemID, field.TypeString, value)
	}
	if value, ok := _u.mutation.Platform(); ok {
		_spec.SetField(productitem.FieldPlatform, field.TypeString, value)
	}
	if value, ok := _u.mutation.Spec(); ok {
		_spec.SetField(productitem.FieldSpec, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.Filter(); ok {
		_spec.SetField(productitem.FieldFilter, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.Mark(); ok {
		_spec.SetField(productitem.FieldMark, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.AppendedMark(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, productitem.FieldMark, value)
		})
	}
	if _u.mutation.MarkCleared() {
		_spec.ClearField(productitem.FieldMark, field.TypeJSON)
	}
	if value, ok := _u.mutation.Data(); ok {
		_spec.SetField(productitem.FieldData, field.TypeString, value)
	}
	_node = &ProductItem{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{productitem.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the ProductItemUpdate builder.
func (_u *ProductItemUpdate) WhereCreatedTime(v time.Time) *ProductItemUpdate {
	_u.Where(productitem.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the ProductItemUpdate builder.
func (_u *ProductItemUpdate) WhereUpdatedTime(v time.Time) *ProductItemUpdate {
	_u.Where(productitem.UpdatedTime(v))
	return _u
}

// WhereHash applies equality check predicate to the ProductItemUpdate builder.
func (_u *ProductItemUpdate) WhereHash(v string) *ProductItemUpdate {
	_u.Where(productitem.Hash(v))
	return _u
}

// WhereItemID applies equality check predicate to the ProductItemUpdate builder.
func (_u *ProductItemUpdate) WhereItemID(v string) *ProductItemUpdate {
	_u.Where(productitem.ItemID(v))
	return _u
}

// WherePlatform applies equality check predicate to the ProductItemUpdate builder.
func (_u *ProductItemUpdate) WherePlatform(v string) *ProductItemUpdate {
	_u.Where(productitem.Platform(v))
	return _u
}

// WhereData applies equality check predicate to the ProductItemUpdate builder.
func (_u *ProductItemUpdate) WhereData(v string) *ProductItemUpdate {
	_u.Where(productitem.Data(v))
	return _u
}
