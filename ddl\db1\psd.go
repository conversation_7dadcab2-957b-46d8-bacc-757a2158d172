// Code generated by ent, DO NOT EDIT.

package db1

import (
	"fmt"
	"omnix/ddl/db1/psd"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

// Psd is the model entity for the Psd schema.
type Psd struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedTime time.Time `json:"created_time,omitempty"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty"`
	// 所属模型分组id
	PsdGroupID pq.Int64Array `json:"psd_group_id,omitempty"`
	// 模型描述
	Desc string `json:"desc,omitempty"`
	// PSD文件路径
	FilePath string `json:"file_path,omitempty"`
	// PSD文件SHA1值
	FileSha1 string `json:"file_sha1,omitempty"`
	// 文件是否有效，0: 无效, 1: 有效
	FileValid int32 `json:"file_valid,omitempty"`
	// 模型权重，数值越大优先级越高
	Weight int32 `json:"weight,omitempty"`
	// 是否为封面模型，0: 否, 1: 是
	IsCover      int32 `json:"is_cover,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Psd) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case psd.FieldPsdGroupID:
			values[i] = new(pq.Int64Array)
		case psd.FieldID, psd.FieldFileValid, psd.FieldWeight, psd.FieldIsCover:
			values[i] = new(sql.NullInt64)
		case psd.FieldDesc, psd.FieldFilePath, psd.FieldFileSha1:
			values[i] = new(sql.NullString)
		case psd.FieldCreatedTime, psd.FieldUpdatedTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Psd fields.
func (_m *Psd) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case psd.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case psd.FieldCreatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_time", values[i])
			} else if value.Valid {
				_m.CreatedTime = value.Time
			}
		case psd.FieldUpdatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_time", values[i])
			} else if value.Valid {
				_m.UpdatedTime = value.Time
			}
		case psd.FieldPsdGroupID:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field psd_group_id", values[i])
			} else if value != nil {
				_m.PsdGroupID = *value
			}
		case psd.FieldDesc:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field desc", values[i])
			} else if value.Valid {
				_m.Desc = value.String
			}
		case psd.FieldFilePath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_path", values[i])
			} else if value.Valid {
				_m.FilePath = value.String
			}
		case psd.FieldFileSha1:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field file_sha1", values[i])
			} else if value.Valid {
				_m.FileSha1 = value.String
			}
		case psd.FieldFileValid:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field file_valid", values[i])
			} else if value.Valid {
				_m.FileValid = int32(value.Int64)
			}
		case psd.FieldWeight:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field weight", values[i])
			} else if value.Valid {
				_m.Weight = int32(value.Int64)
			}
		case psd.FieldIsCover:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field is_cover", values[i])
			} else if value.Valid {
				_m.IsCover = int32(value.Int64)
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Psd.
// This includes values selected through modifiers, order, etc.
func (_m *Psd) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this Psd.
// Note that you need to call Psd.Unwrap() before calling this method if this Psd
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *Psd) Update() *PsdUpdateOne {
	return NewPsdClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the Psd entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *Psd) Unwrap() *Psd {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("db1: Psd is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *Psd) String() string {
	var builder strings.Builder
	builder.WriteString("Psd(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("created_time=")
	builder.WriteString(_m.CreatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_time=")
	builder.WriteString(_m.UpdatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("psd_group_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.PsdGroupID))
	builder.WriteString(", ")
	builder.WriteString("desc=")
	builder.WriteString(_m.Desc)
	builder.WriteString(", ")
	builder.WriteString("file_path=")
	builder.WriteString(_m.FilePath)
	builder.WriteString(", ")
	builder.WriteString("file_sha1=")
	builder.WriteString(_m.FileSha1)
	builder.WriteString(", ")
	builder.WriteString("file_valid=")
	builder.WriteString(fmt.Sprintf("%v", _m.FileValid))
	builder.WriteString(", ")
	builder.WriteString("weight=")
	builder.WriteString(fmt.Sprintf("%v", _m.Weight))
	builder.WriteString(", ")
	builder.WriteString("is_cover=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsCover))
	builder.WriteByte(')')
	return builder.String()
}

// Psds is a parsable slice of Psd.
type Psds []*Psd
