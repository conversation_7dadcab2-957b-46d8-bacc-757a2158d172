// Code generated by ent, DO NOT EDIT.

package psd

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the psd type in the database.
	Label = "psd"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedTime holds the string denoting the created_time field in the database.
	FieldCreatedTime = "created_time"
	// FieldUpdatedTime holds the string denoting the updated_time field in the database.
	FieldUpdatedTime = "updated_time"
	// FieldPsdGroupID holds the string denoting the psd_group_id field in the database.
	FieldPsdGroupID = "psd_group_id"
	// FieldDesc holds the string denoting the desc field in the database.
	FieldDesc = "desc"
	// FieldFilePath holds the string denoting the file_path field in the database.
	FieldFilePath = "file_path"
	// FieldFileSha1 holds the string denoting the file_sha1 field in the database.
	FieldFileSha1 = "file_sha1"
	// FieldFileValid holds the string denoting the file_valid field in the database.
	FieldFileValid = "file_valid"
	// FieldWeight holds the string denoting the weight field in the database.
	FieldWeight = "weight"
	// FieldIsCover holds the string denoting the is_cover field in the database.
	FieldIsCover = "is_cover"
	// Table holds the table name of the psd in the database.
	Table = "psds"
)

// Columns holds all SQL columns for psd fields.
var Columns = []string{
	FieldID,
	FieldCreatedTime,
	FieldUpdatedTime,
	FieldPsdGroupID,
	FieldDesc,
	FieldFilePath,
	FieldFileSha1,
	FieldFileValid,
	FieldWeight,
	FieldIsCover,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedTime holds the default value on creation for the "created_time" field.
	DefaultCreatedTime func() time.Time
	// DefaultUpdatedTime holds the default value on creation for the "updated_time" field.
	DefaultUpdatedTime func() time.Time
	// UpdateDefaultUpdatedTime holds the default value on update for the "updated_time" field.
	UpdateDefaultUpdatedTime func() time.Time
	// FilePathValidator is a validator for the "file_path" field. It is called by the builders before save.
	FilePathValidator func(string) error
	// FileSha1Validator is a validator for the "file_sha1" field. It is called by the builders before save.
	FileSha1Validator func(string) error
	// DefaultFileValid holds the default value on creation for the "file_valid" field.
	DefaultFileValid int32
	// DefaultWeight holds the default value on creation for the "weight" field.
	DefaultWeight int32
	// DefaultIsCover holds the default value on creation for the "is_cover" field.
	DefaultIsCover int32
)

// OrderOption defines the ordering options for the Psd queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedTime orders the results by the created_time field.
func ByCreatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedTime, opts...).ToFunc()
}

// ByUpdatedTime orders the results by the updated_time field.
func ByUpdatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedTime, opts...).ToFunc()
}

// ByPsdGroupID orders the results by the psd_group_id field.
func ByPsdGroupID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPsdGroupID, opts...).ToFunc()
}

// ByDesc orders the results by the desc field.
func ByDesc(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDesc, opts...).ToFunc()
}

// ByFilePath orders the results by the file_path field.
func ByFilePath(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFilePath, opts...).ToFunc()
}

// ByFileSha1 orders the results by the file_sha1 field.
func ByFileSha1(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileSha1, opts...).ToFunc()
}

// ByFileValid orders the results by the file_valid field.
func ByFileValid(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileValid, opts...).ToFunc()
}

// ByWeight orders the results by the weight field.
func ByWeight(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWeight, opts...).ToFunc()
}

// ByIsCover orders the results by the is_cover field.
func ByIsCover(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsCover, opts...).ToFunc()
}
