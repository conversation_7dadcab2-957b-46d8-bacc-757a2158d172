// Code generated by ent, DO NOT EDIT.

package psd

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldUpdatedTime, v))
}

// PsdGroupID applies equality check predicate on the "psd_group_id" field. It's identical to PsdGroupIDEQ.
func PsdGroupID(v pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldPsdGroupID, v))
}

// Desc applies equality check predicate on the "desc" field. It's identical to DescEQ.
func Desc(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldDesc, v))
}

// FilePath applies equality check predicate on the "file_path" field. It's identical to FilePathEQ.
func FilePath(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldFilePath, v))
}

// FileSha1 applies equality check predicate on the "file_sha1" field. It's identical to FileSha1EQ.
func FileSha1(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldFileSha1, v))
}

// FileValid applies equality check predicate on the "file_valid" field. It's identical to FileValidEQ.
func FileValid(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldFileValid, v))
}

// Weight applies equality check predicate on the "weight" field. It's identical to WeightEQ.
func Weight(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldWeight, v))
}

// IsCover applies equality check predicate on the "is_cover" field. It's identical to IsCoverEQ.
func IsCover(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldIsCover, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldUpdatedTime, v))
}

// PsdGroupIDEQ applies the EQ predicate on the "psd_group_id" field.
func PsdGroupIDEQ(v pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldPsdGroupID, v))
}

// PsdGroupIDNEQ applies the NEQ predicate on the "psd_group_id" field.
func PsdGroupIDNEQ(v pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldPsdGroupID, v))
}

// PsdGroupIDIn applies the In predicate on the "psd_group_id" field.
func PsdGroupIDIn(vs ...pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldPsdGroupID, vs...))
}

// PsdGroupIDNotIn applies the NotIn predicate on the "psd_group_id" field.
func PsdGroupIDNotIn(vs ...pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldPsdGroupID, vs...))
}

// PsdGroupIDGT applies the GT predicate on the "psd_group_id" field.
func PsdGroupIDGT(v pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldPsdGroupID, v))
}

// PsdGroupIDGTE applies the GTE predicate on the "psd_group_id" field.
func PsdGroupIDGTE(v pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldPsdGroupID, v))
}

// PsdGroupIDLT applies the LT predicate on the "psd_group_id" field.
func PsdGroupIDLT(v pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldPsdGroupID, v))
}

// PsdGroupIDLTE applies the LTE predicate on the "psd_group_id" field.
func PsdGroupIDLTE(v pq.Int64Array) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldPsdGroupID, v))
}

// PsdGroupIDIsNil applies the IsNil predicate on the "psd_group_id" field.
func PsdGroupIDIsNil() predicate.Psd {
	return predicate.Psd(sql.FieldIsNull(FieldPsdGroupID))
}

// PsdGroupIDNotNil applies the NotNil predicate on the "psd_group_id" field.
func PsdGroupIDNotNil() predicate.Psd {
	return predicate.Psd(sql.FieldNotNull(FieldPsdGroupID))
}

// DescEQ applies the EQ predicate on the "desc" field.
func DescEQ(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldDesc, v))
}

// DescNEQ applies the NEQ predicate on the "desc" field.
func DescNEQ(v string) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldDesc, v))
}

// DescIn applies the In predicate on the "desc" field.
func DescIn(vs ...string) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldDesc, vs...))
}

// DescNotIn applies the NotIn predicate on the "desc" field.
func DescNotIn(vs ...string) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldDesc, vs...))
}

// DescGT applies the GT predicate on the "desc" field.
func DescGT(v string) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldDesc, v))
}

// DescGTE applies the GTE predicate on the "desc" field.
func DescGTE(v string) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldDesc, v))
}

// DescLT applies the LT predicate on the "desc" field.
func DescLT(v string) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldDesc, v))
}

// DescLTE applies the LTE predicate on the "desc" field.
func DescLTE(v string) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldDesc, v))
}

// DescContains applies the Contains predicate on the "desc" field.
func DescContains(v string) predicate.Psd {
	return predicate.Psd(sql.FieldContains(FieldDesc, v))
}

// DescHasPrefix applies the HasPrefix predicate on the "desc" field.
func DescHasPrefix(v string) predicate.Psd {
	return predicate.Psd(sql.FieldHasPrefix(FieldDesc, v))
}

// DescHasSuffix applies the HasSuffix predicate on the "desc" field.
func DescHasSuffix(v string) predicate.Psd {
	return predicate.Psd(sql.FieldHasSuffix(FieldDesc, v))
}

// DescIsNil applies the IsNil predicate on the "desc" field.
func DescIsNil() predicate.Psd {
	return predicate.Psd(sql.FieldIsNull(FieldDesc))
}

// DescNotNil applies the NotNil predicate on the "desc" field.
func DescNotNil() predicate.Psd {
	return predicate.Psd(sql.FieldNotNull(FieldDesc))
}

// DescEqualFold applies the EqualFold predicate on the "desc" field.
func DescEqualFold(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEqualFold(FieldDesc, v))
}

// DescContainsFold applies the ContainsFold predicate on the "desc" field.
func DescContainsFold(v string) predicate.Psd {
	return predicate.Psd(sql.FieldContainsFold(FieldDesc, v))
}

// FilePathEQ applies the EQ predicate on the "file_path" field.
func FilePathEQ(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldFilePath, v))
}

// FilePathNEQ applies the NEQ predicate on the "file_path" field.
func FilePathNEQ(v string) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldFilePath, v))
}

// FilePathIn applies the In predicate on the "file_path" field.
func FilePathIn(vs ...string) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldFilePath, vs...))
}

// FilePathNotIn applies the NotIn predicate on the "file_path" field.
func FilePathNotIn(vs ...string) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldFilePath, vs...))
}

// FilePathGT applies the GT predicate on the "file_path" field.
func FilePathGT(v string) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldFilePath, v))
}

// FilePathGTE applies the GTE predicate on the "file_path" field.
func FilePathGTE(v string) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldFilePath, v))
}

// FilePathLT applies the LT predicate on the "file_path" field.
func FilePathLT(v string) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldFilePath, v))
}

// FilePathLTE applies the LTE predicate on the "file_path" field.
func FilePathLTE(v string) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldFilePath, v))
}

// FilePathContains applies the Contains predicate on the "file_path" field.
func FilePathContains(v string) predicate.Psd {
	return predicate.Psd(sql.FieldContains(FieldFilePath, v))
}

// FilePathHasPrefix applies the HasPrefix predicate on the "file_path" field.
func FilePathHasPrefix(v string) predicate.Psd {
	return predicate.Psd(sql.FieldHasPrefix(FieldFilePath, v))
}

// FilePathHasSuffix applies the HasSuffix predicate on the "file_path" field.
func FilePathHasSuffix(v string) predicate.Psd {
	return predicate.Psd(sql.FieldHasSuffix(FieldFilePath, v))
}

// FilePathEqualFold applies the EqualFold predicate on the "file_path" field.
func FilePathEqualFold(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEqualFold(FieldFilePath, v))
}

// FilePathContainsFold applies the ContainsFold predicate on the "file_path" field.
func FilePathContainsFold(v string) predicate.Psd {
	return predicate.Psd(sql.FieldContainsFold(FieldFilePath, v))
}

// FileSha1EQ applies the EQ predicate on the "file_sha1" field.
func FileSha1EQ(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldFileSha1, v))
}

// FileSha1NEQ applies the NEQ predicate on the "file_sha1" field.
func FileSha1NEQ(v string) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldFileSha1, v))
}

// FileSha1In applies the In predicate on the "file_sha1" field.
func FileSha1In(vs ...string) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldFileSha1, vs...))
}

// FileSha1NotIn applies the NotIn predicate on the "file_sha1" field.
func FileSha1NotIn(vs ...string) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldFileSha1, vs...))
}

// FileSha1GT applies the GT predicate on the "file_sha1" field.
func FileSha1GT(v string) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldFileSha1, v))
}

// FileSha1GTE applies the GTE predicate on the "file_sha1" field.
func FileSha1GTE(v string) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldFileSha1, v))
}

// FileSha1LT applies the LT predicate on the "file_sha1" field.
func FileSha1LT(v string) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldFileSha1, v))
}

// FileSha1LTE applies the LTE predicate on the "file_sha1" field.
func FileSha1LTE(v string) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldFileSha1, v))
}

// FileSha1Contains applies the Contains predicate on the "file_sha1" field.
func FileSha1Contains(v string) predicate.Psd {
	return predicate.Psd(sql.FieldContains(FieldFileSha1, v))
}

// FileSha1HasPrefix applies the HasPrefix predicate on the "file_sha1" field.
func FileSha1HasPrefix(v string) predicate.Psd {
	return predicate.Psd(sql.FieldHasPrefix(FieldFileSha1, v))
}

// FileSha1HasSuffix applies the HasSuffix predicate on the "file_sha1" field.
func FileSha1HasSuffix(v string) predicate.Psd {
	return predicate.Psd(sql.FieldHasSuffix(FieldFileSha1, v))
}

// FileSha1EqualFold applies the EqualFold predicate on the "file_sha1" field.
func FileSha1EqualFold(v string) predicate.Psd {
	return predicate.Psd(sql.FieldEqualFold(FieldFileSha1, v))
}

// FileSha1ContainsFold applies the ContainsFold predicate on the "file_sha1" field.
func FileSha1ContainsFold(v string) predicate.Psd {
	return predicate.Psd(sql.FieldContainsFold(FieldFileSha1, v))
}

// FileValidEQ applies the EQ predicate on the "file_valid" field.
func FileValidEQ(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldFileValid, v))
}

// FileValidNEQ applies the NEQ predicate on the "file_valid" field.
func FileValidNEQ(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldFileValid, v))
}

// FileValidIn applies the In predicate on the "file_valid" field.
func FileValidIn(vs ...int32) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldFileValid, vs...))
}

// FileValidNotIn applies the NotIn predicate on the "file_valid" field.
func FileValidNotIn(vs ...int32) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldFileValid, vs...))
}

// FileValidGT applies the GT predicate on the "file_valid" field.
func FileValidGT(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldFileValid, v))
}

// FileValidGTE applies the GTE predicate on the "file_valid" field.
func FileValidGTE(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldFileValid, v))
}

// FileValidLT applies the LT predicate on the "file_valid" field.
func FileValidLT(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldFileValid, v))
}

// FileValidLTE applies the LTE predicate on the "file_valid" field.
func FileValidLTE(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldFileValid, v))
}

// WeightEQ applies the EQ predicate on the "weight" field.
func WeightEQ(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldWeight, v))
}

// WeightNEQ applies the NEQ predicate on the "weight" field.
func WeightNEQ(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldWeight, v))
}

// WeightIn applies the In predicate on the "weight" field.
func WeightIn(vs ...int32) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldWeight, vs...))
}

// WeightNotIn applies the NotIn predicate on the "weight" field.
func WeightNotIn(vs ...int32) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldWeight, vs...))
}

// WeightGT applies the GT predicate on the "weight" field.
func WeightGT(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldWeight, v))
}

// WeightGTE applies the GTE predicate on the "weight" field.
func WeightGTE(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldWeight, v))
}

// WeightLT applies the LT predicate on the "weight" field.
func WeightLT(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldWeight, v))
}

// WeightLTE applies the LTE predicate on the "weight" field.
func WeightLTE(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldWeight, v))
}

// IsCoverEQ applies the EQ predicate on the "is_cover" field.
func IsCoverEQ(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldEQ(FieldIsCover, v))
}

// IsCoverNEQ applies the NEQ predicate on the "is_cover" field.
func IsCoverNEQ(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldNEQ(FieldIsCover, v))
}

// IsCoverIn applies the In predicate on the "is_cover" field.
func IsCoverIn(vs ...int32) predicate.Psd {
	return predicate.Psd(sql.FieldIn(FieldIsCover, vs...))
}

// IsCoverNotIn applies the NotIn predicate on the "is_cover" field.
func IsCoverNotIn(vs ...int32) predicate.Psd {
	return predicate.Psd(sql.FieldNotIn(FieldIsCover, vs...))
}

// IsCoverGT applies the GT predicate on the "is_cover" field.
func IsCoverGT(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldGT(FieldIsCover, v))
}

// IsCoverGTE applies the GTE predicate on the "is_cover" field.
func IsCoverGTE(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldGTE(FieldIsCover, v))
}

// IsCoverLT applies the LT predicate on the "is_cover" field.
func IsCoverLT(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldLT(FieldIsCover, v))
}

// IsCoverLTE applies the LTE predicate on the "is_cover" field.
func IsCoverLTE(v int32) predicate.Psd {
	return predicate.Psd(sql.FieldLTE(FieldIsCover, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Psd) predicate.Psd {
	return predicate.Psd(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Psd) predicate.Psd {
	return predicate.Psd(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Psd) predicate.Psd {
	return predicate.Psd(sql.NotPredicates(p))
}
