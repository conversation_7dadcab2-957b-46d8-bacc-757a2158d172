// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/psd"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// PsdCreate is the builder for creating a Psd entity.
type PsdCreate struct {
	config
	mutation *PsdMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *PsdCreate) SetCreatedTime(v time.Time) *PsdCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *PsdCreate) SetNillableCreatedTime(v *time.Time) *PsdCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *PsdCreate) SetUpdatedTime(v time.Time) *PsdCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *PsdCreate) SetNillableUpdatedTime(v *time.Time) *PsdCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetPsdGroupID sets the "psd_group_id" field.
func (_c *PsdCreate) SetPsdGroupID(v pq.Int64Array) *PsdCreate {
	_c.mutation.SetPsdGroupID(v)
	return _c
}

// SetDesc sets the "desc" field.
func (_c *PsdCreate) SetDesc(v string) *PsdCreate {
	_c.mutation.SetDesc(v)
	return _c
}

// SetNillableDesc sets the "desc" field if the given value is not nil.
func (_c *PsdCreate) SetNillableDesc(v *string) *PsdCreate {
	if v != nil {
		_c.SetDesc(*v)
	}
	return _c
}

// SetFilePath sets the "file_path" field.
func (_c *PsdCreate) SetFilePath(v string) *PsdCreate {
	_c.mutation.SetFilePath(v)
	return _c
}

// SetFileSha1 sets the "file_sha1" field.
func (_c *PsdCreate) SetFileSha1(v string) *PsdCreate {
	_c.mutation.SetFileSha1(v)
	return _c
}

// SetFileValid sets the "file_valid" field.
func (_c *PsdCreate) SetFileValid(v int32) *PsdCreate {
	_c.mutation.SetFileValid(v)
	return _c
}

// SetNillableFileValid sets the "file_valid" field if the given value is not nil.
func (_c *PsdCreate) SetNillableFileValid(v *int32) *PsdCreate {
	if v != nil {
		_c.SetFileValid(*v)
	}
	return _c
}

// SetWeight sets the "weight" field.
func (_c *PsdCreate) SetWeight(v int32) *PsdCreate {
	_c.mutation.SetWeight(v)
	return _c
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (_c *PsdCreate) SetNillableWeight(v *int32) *PsdCreate {
	if v != nil {
		_c.SetWeight(*v)
	}
	return _c
}

// SetIsCover sets the "is_cover" field.
func (_c *PsdCreate) SetIsCover(v int32) *PsdCreate {
	_c.mutation.SetIsCover(v)
	return _c
}

// SetNillableIsCover sets the "is_cover" field if the given value is not nil.
func (_c *PsdCreate) SetNillableIsCover(v *int32) *PsdCreate {
	if v != nil {
		_c.SetIsCover(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *PsdCreate) SetID(v int64) *PsdCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the PsdMutation object of the builder.
func (_c *PsdCreate) Mutation() *PsdMutation {
	return _c.mutation
}

// Save creates the Psd in the database.
func (_c *PsdCreate) Save(ctx context.Context) (*Psd, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *PsdCreate) SaveX(ctx context.Context) *Psd {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *PsdCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := psd.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := psd.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
	if _, ok := _c.mutation.FileValid(); !ok {
		v := psd.DefaultFileValid
		_c.mutation.SetFileValid(v)
	}
	if _, ok := _c.mutation.Weight(); !ok {
		v := psd.DefaultWeight
		_c.mutation.SetWeight(v)
	}
	if _, ok := _c.mutation.IsCover(); !ok {
		v := psd.DefaultIsCover
		_c.mutation.SetIsCover(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *PsdCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "Psd.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "Psd.updated_time"`)}
	}
	if _, ok := _c.mutation.FilePath(); !ok {
		return &ValidationError{Name: "file_path", err: errors.New(`db1: missing required field "Psd.file_path"`)}
	}
	if v, ok := _c.mutation.FilePath(); ok {
		if err := psd.FilePathValidator(v); err != nil {
			return &ValidationError{Name: "file_path", err: fmt.Errorf(`db1: validator failed for field "Psd.file_path": %w`, err)}
		}
	}
	if _, ok := _c.mutation.FileSha1(); !ok {
		return &ValidationError{Name: "file_sha1", err: errors.New(`db1: missing required field "Psd.file_sha1"`)}
	}
	if v, ok := _c.mutation.FileSha1(); ok {
		if err := psd.FileSha1Validator(v); err != nil {
			return &ValidationError{Name: "file_sha1", err: fmt.Errorf(`db1: validator failed for field "Psd.file_sha1": %w`, err)}
		}
	}
	if _, ok := _c.mutation.FileValid(); !ok {
		return &ValidationError{Name: "file_valid", err: errors.New(`db1: missing required field "Psd.file_valid"`)}
	}
	if _, ok := _c.mutation.Weight(); !ok {
		return &ValidationError{Name: "weight", err: errors.New(`db1: missing required field "Psd.weight"`)}
	}
	if _, ok := _c.mutation.IsCover(); !ok {
		return &ValidationError{Name: "is_cover", err: errors.New(`db1: missing required field "Psd.is_cover"`)}
	}
	return nil
}

func (_c *PsdCreate) sqlSave(ctx context.Context) (*Psd, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *PsdCreate) createSpec() (*Psd, *sqlgraph.CreateSpec) {
	var (
		_node = &Psd{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(psd.Table, sqlgraph.NewFieldSpec(psd.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(psd.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(psd.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.PsdGroupID(); ok {
		_spec.SetField(psd.FieldPsdGroupID, field.TypeOther, value)
		_node.PsdGroupID = value
	}
	if value, ok := _c.mutation.Desc(); ok {
		_spec.SetField(psd.FieldDesc, field.TypeString, value)
		_node.Desc = value
	}
	if value, ok := _c.mutation.FilePath(); ok {
		_spec.SetField(psd.FieldFilePath, field.TypeString, value)
		_node.FilePath = value
	}
	if value, ok := _c.mutation.FileSha1(); ok {
		_spec.SetField(psd.FieldFileSha1, field.TypeString, value)
		_node.FileSha1 = value
	}
	if value, ok := _c.mutation.FileValid(); ok {
		_spec.SetField(psd.FieldFileValid, field.TypeInt32, value)
		_node.FileValid = value
	}
	if value, ok := _c.mutation.Weight(); ok {
		_spec.SetField(psd.FieldWeight, field.TypeInt32, value)
		_node.Weight = value
	}
	if value, ok := _c.mutation.IsCover(); ok {
		_spec.SetField(psd.FieldIsCover, field.TypeInt32, value)
		_node.IsCover = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Psd.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdCreate) OnConflict(opts ...sql.ConflictOption) *PsdUpsertOne {
	_c.conflict = opts
	return &PsdUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Psd.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdCreate) OnConflictColumns(columns ...string) *PsdUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdUpsertOne{
		create: _c,
	}
}

type (
	// PsdUpsertOne is the builder for "upsert"-ing
	//  one Psd node.
	PsdUpsertOne struct {
		create *PsdCreate
	}

	// PsdUpsert is the "OnConflict" setter.
	PsdUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdUpsert) SetUpdatedTime(v time.Time) *PsdUpsert {
	u.Set(psd.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdUpsert) UpdateUpdatedTime() *PsdUpsert {
	u.SetExcluded(psd.FieldUpdatedTime)
	return u
}

// SetPsdGroupID sets the "psd_group_id" field.
func (u *PsdUpsert) SetPsdGroupID(v pq.Int64Array) *PsdUpsert {
	u.Set(psd.FieldPsdGroupID, v)
	return u
}

// UpdatePsdGroupID sets the "psd_group_id" field to the value that was provided on create.
func (u *PsdUpsert) UpdatePsdGroupID() *PsdUpsert {
	u.SetExcluded(psd.FieldPsdGroupID)
	return u
}

// ClearPsdGroupID clears the value of the "psd_group_id" field.
func (u *PsdUpsert) ClearPsdGroupID() *PsdUpsert {
	u.SetNull(psd.FieldPsdGroupID)
	return u
}

// SetDesc sets the "desc" field.
func (u *PsdUpsert) SetDesc(v string) *PsdUpsert {
	u.Set(psd.FieldDesc, v)
	return u
}

// UpdateDesc sets the "desc" field to the value that was provided on create.
func (u *PsdUpsert) UpdateDesc() *PsdUpsert {
	u.SetExcluded(psd.FieldDesc)
	return u
}

// ClearDesc clears the value of the "desc" field.
func (u *PsdUpsert) ClearDesc() *PsdUpsert {
	u.SetNull(psd.FieldDesc)
	return u
}

// SetFilePath sets the "file_path" field.
func (u *PsdUpsert) SetFilePath(v string) *PsdUpsert {
	u.Set(psd.FieldFilePath, v)
	return u
}

// UpdateFilePath sets the "file_path" field to the value that was provided on create.
func (u *PsdUpsert) UpdateFilePath() *PsdUpsert {
	u.SetExcluded(psd.FieldFilePath)
	return u
}

// SetFileSha1 sets the "file_sha1" field.
func (u *PsdUpsert) SetFileSha1(v string) *PsdUpsert {
	u.Set(psd.FieldFileSha1, v)
	return u
}

// UpdateFileSha1 sets the "file_sha1" field to the value that was provided on create.
func (u *PsdUpsert) UpdateFileSha1() *PsdUpsert {
	u.SetExcluded(psd.FieldFileSha1)
	return u
}

// SetFileValid sets the "file_valid" field.
func (u *PsdUpsert) SetFileValid(v int32) *PsdUpsert {
	u.Set(psd.FieldFileValid, v)
	return u
}

// UpdateFileValid sets the "file_valid" field to the value that was provided on create.
func (u *PsdUpsert) UpdateFileValid() *PsdUpsert {
	u.SetExcluded(psd.FieldFileValid)
	return u
}

// AddFileValid adds v to the "file_valid" field.
func (u *PsdUpsert) AddFileValid(v int32) *PsdUpsert {
	u.Add(psd.FieldFileValid, v)
	return u
}

// SetWeight sets the "weight" field.
func (u *PsdUpsert) SetWeight(v int32) *PsdUpsert {
	u.Set(psd.FieldWeight, v)
	return u
}

// UpdateWeight sets the "weight" field to the value that was provided on create.
func (u *PsdUpsert) UpdateWeight() *PsdUpsert {
	u.SetExcluded(psd.FieldWeight)
	return u
}

// AddWeight adds v to the "weight" field.
func (u *PsdUpsert) AddWeight(v int32) *PsdUpsert {
	u.Add(psd.FieldWeight, v)
	return u
}

// SetIsCover sets the "is_cover" field.
func (u *PsdUpsert) SetIsCover(v int32) *PsdUpsert {
	u.Set(psd.FieldIsCover, v)
	return u
}

// UpdateIsCover sets the "is_cover" field to the value that was provided on create.
func (u *PsdUpsert) UpdateIsCover() *PsdUpsert {
	u.SetExcluded(psd.FieldIsCover)
	return u
}

// AddIsCover adds v to the "is_cover" field.
func (u *PsdUpsert) AddIsCover(v int32) *PsdUpsert {
	u.Add(psd.FieldIsCover, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Psd.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psd.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdUpsertOne) UpdateNewValues() *PsdUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(psd.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(psd.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Psd.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *PsdUpsertOne) Ignore() *PsdUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdUpsertOne) DoNothing() *PsdUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdCreate.OnConflict
// documentation for more info.
func (u *PsdUpsertOne) Update(set func(*PsdUpsert)) *PsdUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdUpsertOne) SetUpdatedTime(v time.Time) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdateUpdatedTime() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetPsdGroupID sets the "psd_group_id" field.
func (u *PsdUpsertOne) SetPsdGroupID(v pq.Int64Array) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetPsdGroupID(v)
	})
}

// UpdatePsdGroupID sets the "psd_group_id" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdatePsdGroupID() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdatePsdGroupID()
	})
}

// ClearPsdGroupID clears the value of the "psd_group_id" field.
func (u *PsdUpsertOne) ClearPsdGroupID() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.ClearPsdGroupID()
	})
}

// SetDesc sets the "desc" field.
func (u *PsdUpsertOne) SetDesc(v string) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetDesc(v)
	})
}

// UpdateDesc sets the "desc" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdateDesc() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateDesc()
	})
}

// ClearDesc clears the value of the "desc" field.
func (u *PsdUpsertOne) ClearDesc() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.ClearDesc()
	})
}

// SetFilePath sets the "file_path" field.
func (u *PsdUpsertOne) SetFilePath(v string) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetFilePath(v)
	})
}

// UpdateFilePath sets the "file_path" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdateFilePath() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateFilePath()
	})
}

// SetFileSha1 sets the "file_sha1" field.
func (u *PsdUpsertOne) SetFileSha1(v string) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetFileSha1(v)
	})
}

// UpdateFileSha1 sets the "file_sha1" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdateFileSha1() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateFileSha1()
	})
}

// SetFileValid sets the "file_valid" field.
func (u *PsdUpsertOne) SetFileValid(v int32) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetFileValid(v)
	})
}

// AddFileValid adds v to the "file_valid" field.
func (u *PsdUpsertOne) AddFileValid(v int32) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.AddFileValid(v)
	})
}

// UpdateFileValid sets the "file_valid" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdateFileValid() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateFileValid()
	})
}

// SetWeight sets the "weight" field.
func (u *PsdUpsertOne) SetWeight(v int32) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetWeight(v)
	})
}

// AddWeight adds v to the "weight" field.
func (u *PsdUpsertOne) AddWeight(v int32) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.AddWeight(v)
	})
}

// UpdateWeight sets the "weight" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdateWeight() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateWeight()
	})
}

// SetIsCover sets the "is_cover" field.
func (u *PsdUpsertOne) SetIsCover(v int32) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.SetIsCover(v)
	})
}

// AddIsCover adds v to the "is_cover" field.
func (u *PsdUpsertOne) AddIsCover(v int32) *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.AddIsCover(v)
	})
}

// UpdateIsCover sets the "is_cover" field to the value that was provided on create.
func (u *PsdUpsertOne) UpdateIsCover() *PsdUpsertOne {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateIsCover()
	})
}

// Exec executes the query.
func (u *PsdUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *PsdUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *PsdUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// PsdCreateBulk is the builder for creating many Psd entities in bulk.
type PsdCreateBulk struct {
	config
	err      error
	builders []*PsdCreate
	conflict []sql.ConflictOption
}

// Save creates the Psd entities in the database.
func (_c *PsdCreateBulk) Save(ctx context.Context) ([]*Psd, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*Psd, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PsdMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *PsdCreateBulk) SaveX(ctx context.Context) []*Psd {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Psd.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdCreateBulk) OnConflict(opts ...sql.ConflictOption) *PsdUpsertBulk {
	_c.conflict = opts
	return &PsdUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Psd.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdCreateBulk) OnConflictColumns(columns ...string) *PsdUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdUpsertBulk{
		create: _c,
	}
}

// PsdUpsertBulk is the builder for "upsert"-ing
// a bulk of Psd nodes.
type PsdUpsertBulk struct {
	create *PsdCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Psd.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psd.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdUpsertBulk) UpdateNewValues() *PsdUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(psd.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(psd.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Psd.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *PsdUpsertBulk) Ignore() *PsdUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdUpsertBulk) DoNothing() *PsdUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdCreateBulk.OnConflict
// documentation for more info.
func (u *PsdUpsertBulk) Update(set func(*PsdUpsert)) *PsdUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdUpsertBulk) SetUpdatedTime(v time.Time) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdateUpdatedTime() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetPsdGroupID sets the "psd_group_id" field.
func (u *PsdUpsertBulk) SetPsdGroupID(v pq.Int64Array) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetPsdGroupID(v)
	})
}

// UpdatePsdGroupID sets the "psd_group_id" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdatePsdGroupID() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdatePsdGroupID()
	})
}

// ClearPsdGroupID clears the value of the "psd_group_id" field.
func (u *PsdUpsertBulk) ClearPsdGroupID() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.ClearPsdGroupID()
	})
}

// SetDesc sets the "desc" field.
func (u *PsdUpsertBulk) SetDesc(v string) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetDesc(v)
	})
}

// UpdateDesc sets the "desc" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdateDesc() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateDesc()
	})
}

// ClearDesc clears the value of the "desc" field.
func (u *PsdUpsertBulk) ClearDesc() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.ClearDesc()
	})
}

// SetFilePath sets the "file_path" field.
func (u *PsdUpsertBulk) SetFilePath(v string) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetFilePath(v)
	})
}

// UpdateFilePath sets the "file_path" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdateFilePath() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateFilePath()
	})
}

// SetFileSha1 sets the "file_sha1" field.
func (u *PsdUpsertBulk) SetFileSha1(v string) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetFileSha1(v)
	})
}

// UpdateFileSha1 sets the "file_sha1" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdateFileSha1() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateFileSha1()
	})
}

// SetFileValid sets the "file_valid" field.
func (u *PsdUpsertBulk) SetFileValid(v int32) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetFileValid(v)
	})
}

// AddFileValid adds v to the "file_valid" field.
func (u *PsdUpsertBulk) AddFileValid(v int32) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.AddFileValid(v)
	})
}

// UpdateFileValid sets the "file_valid" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdateFileValid() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateFileValid()
	})
}

// SetWeight sets the "weight" field.
func (u *PsdUpsertBulk) SetWeight(v int32) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetWeight(v)
	})
}

// AddWeight adds v to the "weight" field.
func (u *PsdUpsertBulk) AddWeight(v int32) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.AddWeight(v)
	})
}

// UpdateWeight sets the "weight" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdateWeight() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateWeight()
	})
}

// SetIsCover sets the "is_cover" field.
func (u *PsdUpsertBulk) SetIsCover(v int32) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.SetIsCover(v)
	})
}

// AddIsCover adds v to the "is_cover" field.
func (u *PsdUpsertBulk) AddIsCover(v int32) *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.AddIsCover(v)
	})
}

// UpdateIsCover sets the "is_cover" field to the value that was provided on create.
func (u *PsdUpsertBulk) UpdateIsCover() *PsdUpsertBulk {
	return u.Update(func(s *PsdUpsert) {
		s.UpdateIsCover()
	})
}

// Exec executes the query.
func (u *PsdUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the PsdCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
