// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psd"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// PsdDelete is the builder for deleting a Psd entity.
type PsdDelete struct {
	config
	hooks    []Hook
	mutation *PsdMutation
}

// Where appends a list predicates to the PsdDelete builder.
func (_d *PsdDelete) Where(ps ...predicate.Psd) *PsdDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *PsdDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *PsdDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(psd.Table, sqlgraph.NewFieldSpec(psd.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereCreatedTime(v time.Time) *PsdDelete {
	_d.Where(psd.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereUpdatedTime(v time.Time) *PsdDelete {
	_d.Where(psd.UpdatedTime(v))
	return _d
}

// WherePsdGroupID applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WherePsdGroupID(v pq.Int64Array) *PsdDelete {
	_d.Where(psd.PsdGroupID(v))
	return _d
}

// WhereDesc applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereDesc(v string) *PsdDelete {
	_d.Where(psd.Desc(v))
	return _d
}

// WhereFilePath applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereFilePath(v string) *PsdDelete {
	_d.Where(psd.FilePath(v))
	return _d
}

// WhereFileSha1 applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereFileSha1(v string) *PsdDelete {
	_d.Where(psd.FileSha1(v))
	return _d
}

// WhereFileValid applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereFileValid(v int32) *PsdDelete {
	_d.Where(psd.FileValid(v))
	return _d
}

// WhereWeight applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereWeight(v int32) *PsdDelete {
	_d.Where(psd.Weight(v))
	return _d
}

// WhereIsCover applies equality check predicate to the PsdDelete builder.
func (_d *PsdDelete) WhereIsCover(v int32) *PsdDelete {
	_d.Where(psd.IsCover(v))
	return _d
}

// PsdDeleteOne is the builder for deleting a single Psd entity.
type PsdDeleteOne struct {
	_d *PsdDelete
}

// Where appends a list predicates to the PsdDelete builder.
func (_d *PsdDeleteOne) Where(ps ...predicate.Psd) *PsdDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *PsdDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{psd.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
