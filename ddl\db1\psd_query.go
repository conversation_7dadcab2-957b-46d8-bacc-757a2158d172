// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"fmt"
	"math"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psd"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// PsdQuery is the builder for querying Psd entities.
type PsdQuery struct {
	config
	ctx        *QueryContext
	order      []psd.OrderOption
	inters     []Interceptor
	predicates []predicate.Psd
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the PsdQuery builder.
func (_q *PsdQuery) Where(ps ...predicate.Psd) *PsdQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *PsdQuery) Limit(limit int) *PsdQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *PsdQuery) Offset(offset int) *PsdQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *PsdQuery) Unique(unique bool) *PsdQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *PsdQuery) Order(o ...psd.OrderOption) *PsdQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// First returns the first Psd entity from the query.
// Returns a *NotFoundError when no Psd was found.
func (_q *PsdQuery) First(ctx context.Context) (*Psd, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{psd.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *PsdQuery) FirstX(ctx context.Context) *Psd {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Psd ID from the query.
// Returns a *NotFoundError when no Psd ID was found.
func (_q *PsdQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{psd.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *PsdQuery) FirstIDX(ctx context.Context) int64 {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Psd entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Psd entity is found.
// Returns a *NotFoundError when no Psd entities are found.
func (_q *PsdQuery) Only(ctx context.Context) (*Psd, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{psd.Label}
	default:
		return nil, &NotSingularError{psd.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *PsdQuery) OnlyX(ctx context.Context) *Psd {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Psd ID in the query.
// Returns a *NotSingularError when more than one Psd ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *PsdQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{psd.Label}
	default:
		err = &NotSingularError{psd.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *PsdQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Psds.
func (_q *PsdQuery) All(ctx context.Context) ([]*Psd, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Psd, *PsdQuery]()
	return withInterceptors[[]*Psd](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *PsdQuery) AllX(ctx context.Context) []*Psd {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Psd IDs.
func (_q *PsdQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(psd.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *PsdQuery) IDsX(ctx context.Context) []int64 {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *PsdQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*PsdQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *PsdQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *PsdQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("db1: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *PsdQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the PsdQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *PsdQuery) Clone() *PsdQuery {
	if _q == nil {
		return nil
	}
	return &PsdQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]psd.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.Psd{}, _q.predicates...),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Psd.Query().
//		GroupBy(psd.FieldCreatedTime).
//		Aggregate(db1.Count()).
//		Scan(ctx, &v)
func (_q *PsdQuery) GroupBy(field string, fields ...string) *PsdGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &PsdGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = psd.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//	}
//
//	client.Psd.Query().
//		Select(psd.FieldCreatedTime).
//		Scan(ctx, &v)
func (_q *PsdQuery) Select(fields ...string) *PsdSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &PsdSelect{PsdQuery: _q}
	sbuild.label = psd.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a PsdSelect configured with the given aggregations.
func (_q *PsdQuery) Aggregate(fns ...AggregateFunc) *PsdSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *PsdQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("db1: uninitialized interceptor (forgotten import db1/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !psd.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *PsdQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Psd, error) {
	var (
		nodes = []*Psd{}
		_spec = _q.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Psd).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Psd{config: _q.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (_q *PsdQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *PsdQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(psd.Table, psd.Columns, sqlgraph.NewFieldSpec(psd.FieldID, field.TypeInt64))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, psd.FieldID)
		for i := range fields {
			if fields[i] != psd.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *PsdQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(psd.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = psd.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WhereCreatedTime applies equality check predicate on the "created_time" field
func (_m *PsdQuery) WhereCreatedTime(v time.Time) *PsdQuery {
	_m.Where(psd.CreatedTime(v))
	return _m
}

// WhereUpdatedTime applies equality check predicate on the "updated_time" field
func (_m *PsdQuery) WhereUpdatedTime(v time.Time) *PsdQuery {
	_m.Where(psd.UpdatedTime(v))
	return _m
}

// WherePsdGroupID applies equality check predicate on the "psd_group_id" field
func (_m *PsdQuery) WherePsdGroupID(v pq.Int64Array) *PsdQuery {
	_m.Where(psd.PsdGroupID(v))
	return _m
}

// WhereDesc applies equality check predicate on the "desc" field
func (_m *PsdQuery) WhereDesc(v string) *PsdQuery {
	_m.Where(psd.Desc(v))
	return _m
}

// WhereFilePath applies equality check predicate on the "file_path" field
func (_m *PsdQuery) WhereFilePath(v string) *PsdQuery {
	_m.Where(psd.FilePath(v))
	return _m
}

// WhereFileSha1 applies equality check predicate on the "file_sha1" field
func (_m *PsdQuery) WhereFileSha1(v string) *PsdQuery {
	_m.Where(psd.FileSha1(v))
	return _m
}

// WhereFileValid applies equality check predicate on the "file_valid" field
func (_m *PsdQuery) WhereFileValid(v int32) *PsdQuery {
	_m.Where(psd.FileValid(v))
	return _m
}

// WhereWeight applies equality check predicate on the "weight" field
func (_m *PsdQuery) WhereWeight(v int32) *PsdQuery {
	_m.Where(psd.Weight(v))
	return _m
}

// WhereIsCover applies equality check predicate on the "is_cover" field
func (_m *PsdQuery) WhereIsCover(v int32) *PsdQuery {
	_m.Where(psd.IsCover(v))
	return _m
}

// PsdGroupBy is the group-by builder for Psd entities.
type PsdGroupBy struct {
	selector
	build *PsdQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *PsdGroupBy) Aggregate(fns ...AggregateFunc) *PsdGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *PsdGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PsdQuery, *PsdGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *PsdGroupBy) sqlScan(ctx context.Context, root *PsdQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// PsdSelect is the builder for selecting fields of Psd entities.
type PsdSelect struct {
	*PsdQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *PsdSelect) Aggregate(fns ...AggregateFunc) *PsdSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *PsdSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PsdQuery, *PsdSelect](ctx, _s.PsdQuery, _s, _s.inters, v)
}

func (_s *PsdSelect) sqlScan(ctx context.Context, root *PsdQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
