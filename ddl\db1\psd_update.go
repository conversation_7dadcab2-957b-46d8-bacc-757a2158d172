// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psd"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

// PsdUpdate is the builder for updating Psd entities.
type PsdUpdate struct {
	config
	hooks    []Hook
	mutation *PsdMutation
}

// Where appends a list predicates to the PsdUpdate builder.
func (_u *PsdUpdate) Where(ps ...predicate.Psd) *PsdUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdUpdate) SetUpdatedTime(v time.Time) *PsdUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetPsdGroupID sets the "psd_group_id" field.
func (_u *PsdUpdate) SetPsdGroupID(v pq.Int64Array) *PsdUpdate {
	_u.mutation.SetPsdGroupID(v)
	return _u
}

// ClearPsdGroupID clears the value of the "psd_group_id" field.
func (_u *PsdUpdate) ClearPsdGroupID() *PsdUpdate {
	_u.mutation.ClearPsdGroupID()
	return _u
}

// SetDesc sets the "desc" field.
func (_u *PsdUpdate) SetDesc(v string) *PsdUpdate {
	_u.mutation.SetDesc(v)
	return _u
}

// SetNillableDesc sets the "desc" field if the given value is not nil.
func (_u *PsdUpdate) SetNillableDesc(v *string) *PsdUpdate {
	if v != nil {
		_u.SetDesc(*v)
	}
	return _u
}

// ClearDesc clears the value of the "desc" field.
func (_u *PsdUpdate) ClearDesc() *PsdUpdate {
	_u.mutation.ClearDesc()
	return _u
}

// SetFilePath sets the "file_path" field.
func (_u *PsdUpdate) SetFilePath(v string) *PsdUpdate {
	_u.mutation.SetFilePath(v)
	return _u
}

// SetNillableFilePath sets the "file_path" field if the given value is not nil.
func (_u *PsdUpdate) SetNillableFilePath(v *string) *PsdUpdate {
	if v != nil {
		_u.SetFilePath(*v)
	}
	return _u
}

// SetFileSha1 sets the "file_sha1" field.
func (_u *PsdUpdate) SetFileSha1(v string) *PsdUpdate {
	_u.mutation.SetFileSha1(v)
	return _u
}

// SetNillableFileSha1 sets the "file_sha1" field if the given value is not nil.
func (_u *PsdUpdate) SetNillableFileSha1(v *string) *PsdUpdate {
	if v != nil {
		_u.SetFileSha1(*v)
	}
	return _u
}

// SetFileValid sets the "file_valid" field.
func (_u *PsdUpdate) SetFileValid(v int32) *PsdUpdate {
	_u.mutation.ResetFileValid()
	_u.mutation.SetFileValid(v)
	return _u
}

// SetNillableFileValid sets the "file_valid" field if the given value is not nil.
func (_u *PsdUpdate) SetNillableFileValid(v *int32) *PsdUpdate {
	if v != nil {
		_u.SetFileValid(*v)
	}
	return _u
}

// AddFileValid adds value to the "file_valid" field.
func (_u *PsdUpdate) AddFileValid(v int32) *PsdUpdate {
	_u.mutation.AddFileValid(v)
	return _u
}

// SetWeight sets the "weight" field.
func (_u *PsdUpdate) SetWeight(v int32) *PsdUpdate {
	_u.mutation.ResetWeight()
	_u.mutation.SetWeight(v)
	return _u
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (_u *PsdUpdate) SetNillableWeight(v *int32) *PsdUpdate {
	if v != nil {
		_u.SetWeight(*v)
	}
	return _u
}

// AddWeight adds value to the "weight" field.
func (_u *PsdUpdate) AddWeight(v int32) *PsdUpdate {
	_u.mutation.AddWeight(v)
	return _u
}

// SetIsCover sets the "is_cover" field.
func (_u *PsdUpdate) SetIsCover(v int32) *PsdUpdate {
	_u.mutation.ResetIsCover()
	_u.mutation.SetIsCover(v)
	return _u
}

// SetNillableIsCover sets the "is_cover" field if the given value is not nil.
func (_u *PsdUpdate) SetNillableIsCover(v *int32) *PsdUpdate {
	if v != nil {
		_u.SetIsCover(*v)
	}
	return _u
}

// AddIsCover adds value to the "is_cover" field.
func (_u *PsdUpdate) AddIsCover(v int32) *PsdUpdate {
	_u.mutation.AddIsCover(v)
	return _u
}

// Mutation returns the PsdMutation object of the builder.
func (_u *PsdUpdate) Mutation() *PsdMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *PsdUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *PsdUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psd.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdUpdate) check() error {
	if v, ok := _u.mutation.FilePath(); ok {
		if err := psd.FilePathValidator(v); err != nil {
			return &ValidationError{Name: "file_path", err: fmt.Errorf(`db1: validator failed for field "Psd.file_path": %w`, err)}
		}
	}
	if v, ok := _u.mutation.FileSha1(); ok {
		if err := psd.FileSha1Validator(v); err != nil {
			return &ValidationError{Name: "file_sha1", err: fmt.Errorf(`db1: validator failed for field "Psd.file_sha1": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psd.Table, psd.Columns, sqlgraph.NewFieldSpec(psd.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psd.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.PsdGroupID(); ok {
		_spec.SetField(psd.FieldPsdGroupID, field.TypeOther, value)
	}
	if _u.mutation.PsdGroupIDCleared() {
		_spec.ClearField(psd.FieldPsdGroupID, field.TypeOther)
	}
	if value, ok := _u.mutation.Desc(); ok {
		_spec.SetField(psd.FieldDesc, field.TypeString, value)
	}
	if _u.mutation.DescCleared() {
		_spec.ClearField(psd.FieldDesc, field.TypeString)
	}
	if value, ok := _u.mutation.FilePath(); ok {
		_spec.SetField(psd.FieldFilePath, field.TypeString, value)
	}
	if value, ok := _u.mutation.FileSha1(); ok {
		_spec.SetField(psd.FieldFileSha1, field.TypeString, value)
	}
	if value, ok := _u.mutation.FileValid(); ok {
		_spec.SetField(psd.FieldFileValid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedFileValid(); ok {
		_spec.AddField(psd.FieldFileValid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.Weight(); ok {
		_spec.SetField(psd.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedWeight(); ok {
		_spec.AddField(psd.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.IsCover(); ok {
		_spec.SetField(psd.FieldIsCover, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedIsCover(); ok {
		_spec.AddField(psd.FieldIsCover, field.TypeInt32, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psd.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// PsdUpdateOne is the builder for updating a single Psd entity.
type PsdUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PsdMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdUpdateOne) SetUpdatedTime(v time.Time) *PsdUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetPsdGroupID sets the "psd_group_id" field.
func (_u *PsdUpdateOne) SetPsdGroupID(v pq.Int64Array) *PsdUpdateOne {
	_u.mutation.SetPsdGroupID(v)
	return _u
}

// ClearPsdGroupID clears the value of the "psd_group_id" field.
func (_u *PsdUpdateOne) ClearPsdGroupID() *PsdUpdateOne {
	_u.mutation.ClearPsdGroupID()
	return _u
}

// SetDesc sets the "desc" field.
func (_u *PsdUpdateOne) SetDesc(v string) *PsdUpdateOne {
	_u.mutation.SetDesc(v)
	return _u
}

// SetNillableDesc sets the "desc" field if the given value is not nil.
func (_u *PsdUpdateOne) SetNillableDesc(v *string) *PsdUpdateOne {
	if v != nil {
		_u.SetDesc(*v)
	}
	return _u
}

// ClearDesc clears the value of the "desc" field.
func (_u *PsdUpdateOne) ClearDesc() *PsdUpdateOne {
	_u.mutation.ClearDesc()
	return _u
}

// SetFilePath sets the "file_path" field.
func (_u *PsdUpdateOne) SetFilePath(v string) *PsdUpdateOne {
	_u.mutation.SetFilePath(v)
	return _u
}

// SetNillableFilePath sets the "file_path" field if the given value is not nil.
func (_u *PsdUpdateOne) SetNillableFilePath(v *string) *PsdUpdateOne {
	if v != nil {
		_u.SetFilePath(*v)
	}
	return _u
}

// SetFileSha1 sets the "file_sha1" field.
func (_u *PsdUpdateOne) SetFileSha1(v string) *PsdUpdateOne {
	_u.mutation.SetFileSha1(v)
	return _u
}

// SetNillableFileSha1 sets the "file_sha1" field if the given value is not nil.
func (_u *PsdUpdateOne) SetNillableFileSha1(v *string) *PsdUpdateOne {
	if v != nil {
		_u.SetFileSha1(*v)
	}
	return _u
}

// SetFileValid sets the "file_valid" field.
func (_u *PsdUpdateOne) SetFileValid(v int32) *PsdUpdateOne {
	_u.mutation.ResetFileValid()
	_u.mutation.SetFileValid(v)
	return _u
}

// SetNillableFileValid sets the "file_valid" field if the given value is not nil.
func (_u *PsdUpdateOne) SetNillableFileValid(v *int32) *PsdUpdateOne {
	if v != nil {
		_u.SetFileValid(*v)
	}
	return _u
}

// AddFileValid adds value to the "file_valid" field.
func (_u *PsdUpdateOne) AddFileValid(v int32) *PsdUpdateOne {
	_u.mutation.AddFileValid(v)
	return _u
}

// SetWeight sets the "weight" field.
func (_u *PsdUpdateOne) SetWeight(v int32) *PsdUpdateOne {
	_u.mutation.ResetWeight()
	_u.mutation.SetWeight(v)
	return _u
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (_u *PsdUpdateOne) SetNillableWeight(v *int32) *PsdUpdateOne {
	if v != nil {
		_u.SetWeight(*v)
	}
	return _u
}

// AddWeight adds value to the "weight" field.
func (_u *PsdUpdateOne) AddWeight(v int32) *PsdUpdateOne {
	_u.mutation.AddWeight(v)
	return _u
}

// SetIsCover sets the "is_cover" field.
func (_u *PsdUpdateOne) SetIsCover(v int32) *PsdUpdateOne {
	_u.mutation.ResetIsCover()
	_u.mutation.SetIsCover(v)
	return _u
}

// SetNillableIsCover sets the "is_cover" field if the given value is not nil.
func (_u *PsdUpdateOne) SetNillableIsCover(v *int32) *PsdUpdateOne {
	if v != nil {
		_u.SetIsCover(*v)
	}
	return _u
}

// AddIsCover adds value to the "is_cover" field.
func (_u *PsdUpdateOne) AddIsCover(v int32) *PsdUpdateOne {
	_u.mutation.AddIsCover(v)
	return _u
}

// Mutation returns the PsdMutation object of the builder.
func (_u *PsdUpdateOne) Mutation() *PsdMutation {
	return _u.mutation
}

// Where appends a list predicates to the PsdUpdate builder.
func (_u *PsdUpdateOne) Where(ps ...predicate.Psd) *PsdUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *PsdUpdateOne) Select(field string, fields ...string) *PsdUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated Psd entity.
func (_u *PsdUpdateOne) Save(ctx context.Context) (*Psd, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdUpdateOne) SaveX(ctx context.Context) *Psd {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *PsdUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psd.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdUpdateOne) check() error {
	if v, ok := _u.mutation.FilePath(); ok {
		if err := psd.FilePathValidator(v); err != nil {
			return &ValidationError{Name: "file_path", err: fmt.Errorf(`db1: validator failed for field "Psd.file_path": %w`, err)}
		}
	}
	if v, ok := _u.mutation.FileSha1(); ok {
		if err := psd.FileSha1Validator(v); err != nil {
			return &ValidationError{Name: "file_sha1", err: fmt.Errorf(`db1: validator failed for field "Psd.file_sha1": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdUpdateOne) sqlSave(ctx context.Context) (_node *Psd, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psd.Table, psd.Columns, sqlgraph.NewFieldSpec(psd.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "Psd.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, psd.FieldID)
		for _, f := range fields {
			if !psd.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != psd.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psd.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.PsdGroupID(); ok {
		_spec.SetField(psd.FieldPsdGroupID, field.TypeOther, value)
	}
	if _u.mutation.PsdGroupIDCleared() {
		_spec.ClearField(psd.FieldPsdGroupID, field.TypeOther)
	}
	if value, ok := _u.mutation.Desc(); ok {
		_spec.SetField(psd.FieldDesc, field.TypeString, value)
	}
	if _u.mutation.DescCleared() {
		_spec.ClearField(psd.FieldDesc, field.TypeString)
	}
	if value, ok := _u.mutation.FilePath(); ok {
		_spec.SetField(psd.FieldFilePath, field.TypeString, value)
	}
	if value, ok := _u.mutation.FileSha1(); ok {
		_spec.SetField(psd.FieldFileSha1, field.TypeString, value)
	}
	if value, ok := _u.mutation.FileValid(); ok {
		_spec.SetField(psd.FieldFileValid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedFileValid(); ok {
		_spec.AddField(psd.FieldFileValid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.Weight(); ok {
		_spec.SetField(psd.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedWeight(); ok {
		_spec.AddField(psd.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.IsCover(); ok {
		_spec.SetField(psd.FieldIsCover, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedIsCover(); ok {
		_spec.AddField(psd.FieldIsCover, field.TypeInt32, value)
	}
	_node = &Psd{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psd.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereCreatedTime(v time.Time) *PsdUpdate {
	_u.Where(psd.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereUpdatedTime(v time.Time) *PsdUpdate {
	_u.Where(psd.UpdatedTime(v))
	return _u
}

// WherePsdGroupID applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WherePsdGroupID(v pq.Int64Array) *PsdUpdate {
	_u.Where(psd.PsdGroupID(v))
	return _u
}

// WhereDesc applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereDesc(v string) *PsdUpdate {
	_u.Where(psd.Desc(v))
	return _u
}

// WhereFilePath applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereFilePath(v string) *PsdUpdate {
	_u.Where(psd.FilePath(v))
	return _u
}

// WhereFileSha1 applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereFileSha1(v string) *PsdUpdate {
	_u.Where(psd.FileSha1(v))
	return _u
}

// WhereFileValid applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereFileValid(v int32) *PsdUpdate {
	_u.Where(psd.FileValid(v))
	return _u
}

// WhereWeight applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereWeight(v int32) *PsdUpdate {
	_u.Where(psd.Weight(v))
	return _u
}

// WhereIsCover applies equality check predicate to the PsdUpdate builder.
func (_u *PsdUpdate) WhereIsCover(v int32) *PsdUpdate {
	_u.Where(psd.IsCover(v))
	return _u
}
