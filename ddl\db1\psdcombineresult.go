// Code generated by ent, DO NOT EDIT.

package db1

import (
	"fmt"
	"omnix/ddl/db1/psdcombineresult"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// PsdCombineResult is the model entity for the PsdCombineResult schema.
type PsdCombineResult struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedTime time.Time `json:"created_time,omitempty"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty"`
	// 所属任务id
	TaskID int64 `json:"task_id,omitempty"`
	// 成品URL
	ResultURL string `json:"result_url,omitempty"`
	// 成品权重，数值越大优先级越高
	Weight int32 `json:"weight,omitempty"`
	// 是否为封面成品，0: 否, 1: 是
	IsCover      int32 `json:"is_cover,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PsdCombineResult) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case psdcombineresult.FieldID, psdcombineresult.FieldTaskID, psdcombineresult.FieldWeight, psdcombineresult.FieldIsCover:
			values[i] = new(sql.NullInt64)
		case psdcombineresult.FieldResultURL:
			values[i] = new(sql.NullString)
		case psdcombineresult.FieldCreatedTime, psdcombineresult.FieldUpdatedTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PsdCombineResult fields.
func (_m *PsdCombineResult) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case psdcombineresult.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case psdcombineresult.FieldCreatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_time", values[i])
			} else if value.Valid {
				_m.CreatedTime = value.Time
			}
		case psdcombineresult.FieldUpdatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_time", values[i])
			} else if value.Valid {
				_m.UpdatedTime = value.Time
			}
		case psdcombineresult.FieldTaskID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field task_id", values[i])
			} else if value.Valid {
				_m.TaskID = value.Int64
			}
		case psdcombineresult.FieldResultURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field result_url", values[i])
			} else if value.Valid {
				_m.ResultURL = value.String
			}
		case psdcombineresult.FieldWeight:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field weight", values[i])
			} else if value.Valid {
				_m.Weight = int32(value.Int64)
			}
		case psdcombineresult.FieldIsCover:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field is_cover", values[i])
			} else if value.Valid {
				_m.IsCover = int32(value.Int64)
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PsdCombineResult.
// This includes values selected through modifiers, order, etc.
func (_m *PsdCombineResult) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this PsdCombineResult.
// Note that you need to call PsdCombineResult.Unwrap() before calling this method if this PsdCombineResult
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *PsdCombineResult) Update() *PsdCombineResultUpdateOne {
	return NewPsdCombineResultClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the PsdCombineResult entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *PsdCombineResult) Unwrap() *PsdCombineResult {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("db1: PsdCombineResult is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *PsdCombineResult) String() string {
	var builder strings.Builder
	builder.WriteString("PsdCombineResult(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("created_time=")
	builder.WriteString(_m.CreatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_time=")
	builder.WriteString(_m.UpdatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("task_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.TaskID))
	builder.WriteString(", ")
	builder.WriteString("result_url=")
	builder.WriteString(_m.ResultURL)
	builder.WriteString(", ")
	builder.WriteString("weight=")
	builder.WriteString(fmt.Sprintf("%v", _m.Weight))
	builder.WriteString(", ")
	builder.WriteString("is_cover=")
	builder.WriteString(fmt.Sprintf("%v", _m.IsCover))
	builder.WriteByte(')')
	return builder.String()
}

// PsdCombineResults is a parsable slice of PsdCombineResult.
type PsdCombineResults []*PsdCombineResult
