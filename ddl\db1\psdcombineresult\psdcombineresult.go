// Code generated by ent, DO NOT EDIT.

package psdcombineresult

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the psdcombineresult type in the database.
	Label = "psd_combine_result"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedTime holds the string denoting the created_time field in the database.
	FieldCreatedTime = "created_time"
	// FieldUpdatedTime holds the string denoting the updated_time field in the database.
	FieldUpdatedTime = "updated_time"
	// FieldTaskID holds the string denoting the task_id field in the database.
	FieldTaskID = "task_id"
	// FieldResultURL holds the string denoting the result_url field in the database.
	FieldResultURL = "result_url"
	// FieldWeight holds the string denoting the weight field in the database.
	FieldWeight = "weight"
	// FieldIsCover holds the string denoting the is_cover field in the database.
	FieldIsCover = "is_cover"
	// Table holds the table name of the psdcombineresult in the database.
	Table = "psd_combine_results"
)

// Columns holds all SQL columns for psdcombineresult fields.
var Columns = []string{
	FieldID,
	FieldCreatedTime,
	FieldUpdatedTime,
	FieldTaskID,
	FieldResultURL,
	FieldWeight,
	FieldIsCover,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedTime holds the default value on creation for the "created_time" field.
	DefaultCreatedTime func() time.Time
	// DefaultUpdatedTime holds the default value on creation for the "updated_time" field.
	DefaultUpdatedTime func() time.Time
	// UpdateDefaultUpdatedTime holds the default value on update for the "updated_time" field.
	UpdateDefaultUpdatedTime func() time.Time
	// ResultURLValidator is a validator for the "result_url" field. It is called by the builders before save.
	ResultURLValidator func(string) error
	// DefaultWeight holds the default value on creation for the "weight" field.
	DefaultWeight int32
	// DefaultIsCover holds the default value on creation for the "is_cover" field.
	DefaultIsCover int32
)

// OrderOption defines the ordering options for the PsdCombineResult queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedTime orders the results by the created_time field.
func ByCreatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedTime, opts...).ToFunc()
}

// ByUpdatedTime orders the results by the updated_time field.
func ByUpdatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedTime, opts...).ToFunc()
}

// ByTaskID orders the results by the task_id field.
func ByTaskID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTaskID, opts...).ToFunc()
}

// ByResultURL orders the results by the result_url field.
func ByResultURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldResultURL, opts...).ToFunc()
}

// ByWeight orders the results by the weight field.
func ByWeight(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWeight, opts...).ToFunc()
}

// ByIsCover orders the results by the is_cover field.
func ByIsCover(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsCover, opts...).ToFunc()
}
