// Code generated by ent, DO NOT EDIT.

package psdcombineresult

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldUpdatedTime, v))
}

// TaskID applies equality check predicate on the "task_id" field. It's identical to TaskIDEQ.
func TaskID(v int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldTaskID, v))
}

// ResultURL applies equality check predicate on the "result_url" field. It's identical to ResultURLEQ.
func ResultURL(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldResultURL, v))
}

// Weight applies equality check predicate on the "weight" field. It's identical to WeightEQ.
func Weight(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldWeight, v))
}

// IsCover applies equality check predicate on the "is_cover" field. It's identical to IsCoverEQ.
func IsCover(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldIsCover, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLTE(FieldUpdatedTime, v))
}

// TaskIDEQ applies the EQ predicate on the "task_id" field.
func TaskIDEQ(v int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldTaskID, v))
}

// TaskIDNEQ applies the NEQ predicate on the "task_id" field.
func TaskIDNEQ(v int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNEQ(FieldTaskID, v))
}

// TaskIDIn applies the In predicate on the "task_id" field.
func TaskIDIn(vs ...int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldIn(FieldTaskID, vs...))
}

// TaskIDNotIn applies the NotIn predicate on the "task_id" field.
func TaskIDNotIn(vs ...int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNotIn(FieldTaskID, vs...))
}

// TaskIDGT applies the GT predicate on the "task_id" field.
func TaskIDGT(v int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGT(FieldTaskID, v))
}

// TaskIDGTE applies the GTE predicate on the "task_id" field.
func TaskIDGTE(v int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGTE(FieldTaskID, v))
}

// TaskIDLT applies the LT predicate on the "task_id" field.
func TaskIDLT(v int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLT(FieldTaskID, v))
}

// TaskIDLTE applies the LTE predicate on the "task_id" field.
func TaskIDLTE(v int64) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLTE(FieldTaskID, v))
}

// ResultURLEQ applies the EQ predicate on the "result_url" field.
func ResultURLEQ(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldResultURL, v))
}

// ResultURLNEQ applies the NEQ predicate on the "result_url" field.
func ResultURLNEQ(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNEQ(FieldResultURL, v))
}

// ResultURLIn applies the In predicate on the "result_url" field.
func ResultURLIn(vs ...string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldIn(FieldResultURL, vs...))
}

// ResultURLNotIn applies the NotIn predicate on the "result_url" field.
func ResultURLNotIn(vs ...string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNotIn(FieldResultURL, vs...))
}

// ResultURLGT applies the GT predicate on the "result_url" field.
func ResultURLGT(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGT(FieldResultURL, v))
}

// ResultURLGTE applies the GTE predicate on the "result_url" field.
func ResultURLGTE(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGTE(FieldResultURL, v))
}

// ResultURLLT applies the LT predicate on the "result_url" field.
func ResultURLLT(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLT(FieldResultURL, v))
}

// ResultURLLTE applies the LTE predicate on the "result_url" field.
func ResultURLLTE(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLTE(FieldResultURL, v))
}

// ResultURLContains applies the Contains predicate on the "result_url" field.
func ResultURLContains(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldContains(FieldResultURL, v))
}

// ResultURLHasPrefix applies the HasPrefix predicate on the "result_url" field.
func ResultURLHasPrefix(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldHasPrefix(FieldResultURL, v))
}

// ResultURLHasSuffix applies the HasSuffix predicate on the "result_url" field.
func ResultURLHasSuffix(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldHasSuffix(FieldResultURL, v))
}

// ResultURLEqualFold applies the EqualFold predicate on the "result_url" field.
func ResultURLEqualFold(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEqualFold(FieldResultURL, v))
}

// ResultURLContainsFold applies the ContainsFold predicate on the "result_url" field.
func ResultURLContainsFold(v string) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldContainsFold(FieldResultURL, v))
}

// WeightEQ applies the EQ predicate on the "weight" field.
func WeightEQ(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldWeight, v))
}

// WeightNEQ applies the NEQ predicate on the "weight" field.
func WeightNEQ(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNEQ(FieldWeight, v))
}

// WeightIn applies the In predicate on the "weight" field.
func WeightIn(vs ...int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldIn(FieldWeight, vs...))
}

// WeightNotIn applies the NotIn predicate on the "weight" field.
func WeightNotIn(vs ...int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNotIn(FieldWeight, vs...))
}

// WeightGT applies the GT predicate on the "weight" field.
func WeightGT(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGT(FieldWeight, v))
}

// WeightGTE applies the GTE predicate on the "weight" field.
func WeightGTE(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGTE(FieldWeight, v))
}

// WeightLT applies the LT predicate on the "weight" field.
func WeightLT(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLT(FieldWeight, v))
}

// WeightLTE applies the LTE predicate on the "weight" field.
func WeightLTE(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLTE(FieldWeight, v))
}

// IsCoverEQ applies the EQ predicate on the "is_cover" field.
func IsCoverEQ(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldEQ(FieldIsCover, v))
}

// IsCoverNEQ applies the NEQ predicate on the "is_cover" field.
func IsCoverNEQ(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNEQ(FieldIsCover, v))
}

// IsCoverIn applies the In predicate on the "is_cover" field.
func IsCoverIn(vs ...int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldIn(FieldIsCover, vs...))
}

// IsCoverNotIn applies the NotIn predicate on the "is_cover" field.
func IsCoverNotIn(vs ...int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldNotIn(FieldIsCover, vs...))
}

// IsCoverGT applies the GT predicate on the "is_cover" field.
func IsCoverGT(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGT(FieldIsCover, v))
}

// IsCoverGTE applies the GTE predicate on the "is_cover" field.
func IsCoverGTE(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldGTE(FieldIsCover, v))
}

// IsCoverLT applies the LT predicate on the "is_cover" field.
func IsCoverLT(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLT(FieldIsCover, v))
}

// IsCoverLTE applies the LTE predicate on the "is_cover" field.
func IsCoverLTE(v int32) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.FieldLTE(FieldIsCover, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PsdCombineResult) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PsdCombineResult) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PsdCombineResult) predicate.PsdCombineResult {
	return predicate.PsdCombineResult(sql.NotPredicates(p))
}
