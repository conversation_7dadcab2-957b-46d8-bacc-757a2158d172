// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/psdcombineresult"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdCombineResultCreate is the builder for creating a PsdCombineResult entity.
type PsdCombineResultCreate struct {
	config
	mutation *PsdCombineResultMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *PsdCombineResultCreate) SetCreatedTime(v time.Time) *PsdCombineResultCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *PsdCombineResultCreate) SetNillableCreatedTime(v *time.Time) *PsdCombineResultCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *PsdCombineResultCreate) SetUpdatedTime(v time.Time) *PsdCombineResultCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *PsdCombineResultCreate) SetNillableUpdatedTime(v *time.Time) *PsdCombineResultCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetTaskID sets the "task_id" field.
func (_c *PsdCombineResultCreate) SetTaskID(v int64) *PsdCombineResultCreate {
	_c.mutation.SetTaskID(v)
	return _c
}

// SetResultURL sets the "result_url" field.
func (_c *PsdCombineResultCreate) SetResultURL(v string) *PsdCombineResultCreate {
	_c.mutation.SetResultURL(v)
	return _c
}

// SetWeight sets the "weight" field.
func (_c *PsdCombineResultCreate) SetWeight(v int32) *PsdCombineResultCreate {
	_c.mutation.SetWeight(v)
	return _c
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (_c *PsdCombineResultCreate) SetNillableWeight(v *int32) *PsdCombineResultCreate {
	if v != nil {
		_c.SetWeight(*v)
	}
	return _c
}

// SetIsCover sets the "is_cover" field.
func (_c *PsdCombineResultCreate) SetIsCover(v int32) *PsdCombineResultCreate {
	_c.mutation.SetIsCover(v)
	return _c
}

// SetNillableIsCover sets the "is_cover" field if the given value is not nil.
func (_c *PsdCombineResultCreate) SetNillableIsCover(v *int32) *PsdCombineResultCreate {
	if v != nil {
		_c.SetIsCover(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *PsdCombineResultCreate) SetID(v int64) *PsdCombineResultCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the PsdCombineResultMutation object of the builder.
func (_c *PsdCombineResultCreate) Mutation() *PsdCombineResultMutation {
	return _c.mutation
}

// Save creates the PsdCombineResult in the database.
func (_c *PsdCombineResultCreate) Save(ctx context.Context) (*PsdCombineResult, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *PsdCombineResultCreate) SaveX(ctx context.Context) *PsdCombineResult {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdCombineResultCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdCombineResultCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *PsdCombineResultCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := psdcombineresult.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := psdcombineresult.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
	if _, ok := _c.mutation.Weight(); !ok {
		v := psdcombineresult.DefaultWeight
		_c.mutation.SetWeight(v)
	}
	if _, ok := _c.mutation.IsCover(); !ok {
		v := psdcombineresult.DefaultIsCover
		_c.mutation.SetIsCover(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *PsdCombineResultCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "PsdCombineResult.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "PsdCombineResult.updated_time"`)}
	}
	if _, ok := _c.mutation.TaskID(); !ok {
		return &ValidationError{Name: "task_id", err: errors.New(`db1: missing required field "PsdCombineResult.task_id"`)}
	}
	if _, ok := _c.mutation.ResultURL(); !ok {
		return &ValidationError{Name: "result_url", err: errors.New(`db1: missing required field "PsdCombineResult.result_url"`)}
	}
	if v, ok := _c.mutation.ResultURL(); ok {
		if err := psdcombineresult.ResultURLValidator(v); err != nil {
			return &ValidationError{Name: "result_url", err: fmt.Errorf(`db1: validator failed for field "PsdCombineResult.result_url": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Weight(); !ok {
		return &ValidationError{Name: "weight", err: errors.New(`db1: missing required field "PsdCombineResult.weight"`)}
	}
	if _, ok := _c.mutation.IsCover(); !ok {
		return &ValidationError{Name: "is_cover", err: errors.New(`db1: missing required field "PsdCombineResult.is_cover"`)}
	}
	return nil
}

func (_c *PsdCombineResultCreate) sqlSave(ctx context.Context) (*PsdCombineResult, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *PsdCombineResultCreate) createSpec() (*PsdCombineResult, *sqlgraph.CreateSpec) {
	var (
		_node = &PsdCombineResult{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(psdcombineresult.Table, sqlgraph.NewFieldSpec(psdcombineresult.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(psdcombineresult.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(psdcombineresult.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.TaskID(); ok {
		_spec.SetField(psdcombineresult.FieldTaskID, field.TypeInt64, value)
		_node.TaskID = value
	}
	if value, ok := _c.mutation.ResultURL(); ok {
		_spec.SetField(psdcombineresult.FieldResultURL, field.TypeString, value)
		_node.ResultURL = value
	}
	if value, ok := _c.mutation.Weight(); ok {
		_spec.SetField(psdcombineresult.FieldWeight, field.TypeInt32, value)
		_node.Weight = value
	}
	if value, ok := _c.mutation.IsCover(); ok {
		_spec.SetField(psdcombineresult.FieldIsCover, field.TypeInt32, value)
		_node.IsCover = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PsdCombineResult.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdCombineResultUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdCombineResultCreate) OnConflict(opts ...sql.ConflictOption) *PsdCombineResultUpsertOne {
	_c.conflict = opts
	return &PsdCombineResultUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PsdCombineResult.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdCombineResultCreate) OnConflictColumns(columns ...string) *PsdCombineResultUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdCombineResultUpsertOne{
		create: _c,
	}
}

type (
	// PsdCombineResultUpsertOne is the builder for "upsert"-ing
	//  one PsdCombineResult node.
	PsdCombineResultUpsertOne struct {
		create *PsdCombineResultCreate
	}

	// PsdCombineResultUpsert is the "OnConflict" setter.
	PsdCombineResultUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdCombineResultUpsert) SetUpdatedTime(v time.Time) *PsdCombineResultUpsert {
	u.Set(psdcombineresult.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdCombineResultUpsert) UpdateUpdatedTime() *PsdCombineResultUpsert {
	u.SetExcluded(psdcombineresult.FieldUpdatedTime)
	return u
}

// SetTaskID sets the "task_id" field.
func (u *PsdCombineResultUpsert) SetTaskID(v int64) *PsdCombineResultUpsert {
	u.Set(psdcombineresult.FieldTaskID, v)
	return u
}

// UpdateTaskID sets the "task_id" field to the value that was provided on create.
func (u *PsdCombineResultUpsert) UpdateTaskID() *PsdCombineResultUpsert {
	u.SetExcluded(psdcombineresult.FieldTaskID)
	return u
}

// AddTaskID adds v to the "task_id" field.
func (u *PsdCombineResultUpsert) AddTaskID(v int64) *PsdCombineResultUpsert {
	u.Add(psdcombineresult.FieldTaskID, v)
	return u
}

// SetResultURL sets the "result_url" field.
func (u *PsdCombineResultUpsert) SetResultURL(v string) *PsdCombineResultUpsert {
	u.Set(psdcombineresult.FieldResultURL, v)
	return u
}

// UpdateResultURL sets the "result_url" field to the value that was provided on create.
func (u *PsdCombineResultUpsert) UpdateResultURL() *PsdCombineResultUpsert {
	u.SetExcluded(psdcombineresult.FieldResultURL)
	return u
}

// SetWeight sets the "weight" field.
func (u *PsdCombineResultUpsert) SetWeight(v int32) *PsdCombineResultUpsert {
	u.Set(psdcombineresult.FieldWeight, v)
	return u
}

// UpdateWeight sets the "weight" field to the value that was provided on create.
func (u *PsdCombineResultUpsert) UpdateWeight() *PsdCombineResultUpsert {
	u.SetExcluded(psdcombineresult.FieldWeight)
	return u
}

// AddWeight adds v to the "weight" field.
func (u *PsdCombineResultUpsert) AddWeight(v int32) *PsdCombineResultUpsert {
	u.Add(psdcombineresult.FieldWeight, v)
	return u
}

// SetIsCover sets the "is_cover" field.
func (u *PsdCombineResultUpsert) SetIsCover(v int32) *PsdCombineResultUpsert {
	u.Set(psdcombineresult.FieldIsCover, v)
	return u
}

// UpdateIsCover sets the "is_cover" field to the value that was provided on create.
func (u *PsdCombineResultUpsert) UpdateIsCover() *PsdCombineResultUpsert {
	u.SetExcluded(psdcombineresult.FieldIsCover)
	return u
}

// AddIsCover adds v to the "is_cover" field.
func (u *PsdCombineResultUpsert) AddIsCover(v int32) *PsdCombineResultUpsert {
	u.Add(psdcombineresult.FieldIsCover, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.PsdCombineResult.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psdcombineresult.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdCombineResultUpsertOne) UpdateNewValues() *PsdCombineResultUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(psdcombineresult.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(psdcombineresult.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PsdCombineResult.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *PsdCombineResultUpsertOne) Ignore() *PsdCombineResultUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdCombineResultUpsertOne) DoNothing() *PsdCombineResultUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdCombineResultCreate.OnConflict
// documentation for more info.
func (u *PsdCombineResultUpsertOne) Update(set func(*PsdCombineResultUpsert)) *PsdCombineResultUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdCombineResultUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdCombineResultUpsertOne) SetUpdatedTime(v time.Time) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdCombineResultUpsertOne) UpdateUpdatedTime() *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetTaskID sets the "task_id" field.
func (u *PsdCombineResultUpsertOne) SetTaskID(v int64) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetTaskID(v)
	})
}

// AddTaskID adds v to the "task_id" field.
func (u *PsdCombineResultUpsertOne) AddTaskID(v int64) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.AddTaskID(v)
	})
}

// UpdateTaskID sets the "task_id" field to the value that was provided on create.
func (u *PsdCombineResultUpsertOne) UpdateTaskID() *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateTaskID()
	})
}

// SetResultURL sets the "result_url" field.
func (u *PsdCombineResultUpsertOne) SetResultURL(v string) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetResultURL(v)
	})
}

// UpdateResultURL sets the "result_url" field to the value that was provided on create.
func (u *PsdCombineResultUpsertOne) UpdateResultURL() *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateResultURL()
	})
}

// SetWeight sets the "weight" field.
func (u *PsdCombineResultUpsertOne) SetWeight(v int32) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetWeight(v)
	})
}

// AddWeight adds v to the "weight" field.
func (u *PsdCombineResultUpsertOne) AddWeight(v int32) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.AddWeight(v)
	})
}

// UpdateWeight sets the "weight" field to the value that was provided on create.
func (u *PsdCombineResultUpsertOne) UpdateWeight() *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateWeight()
	})
}

// SetIsCover sets the "is_cover" field.
func (u *PsdCombineResultUpsertOne) SetIsCover(v int32) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetIsCover(v)
	})
}

// AddIsCover adds v to the "is_cover" field.
func (u *PsdCombineResultUpsertOne) AddIsCover(v int32) *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.AddIsCover(v)
	})
}

// UpdateIsCover sets the "is_cover" field to the value that was provided on create.
func (u *PsdCombineResultUpsertOne) UpdateIsCover() *PsdCombineResultUpsertOne {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateIsCover()
	})
}

// Exec executes the query.
func (u *PsdCombineResultUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdCombineResultCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdCombineResultUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *PsdCombineResultUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *PsdCombineResultUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// PsdCombineResultCreateBulk is the builder for creating many PsdCombineResult entities in bulk.
type PsdCombineResultCreateBulk struct {
	config
	err      error
	builders []*PsdCombineResultCreate
	conflict []sql.ConflictOption
}

// Save creates the PsdCombineResult entities in the database.
func (_c *PsdCombineResultCreateBulk) Save(ctx context.Context) ([]*PsdCombineResult, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*PsdCombineResult, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PsdCombineResultMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *PsdCombineResultCreateBulk) SaveX(ctx context.Context) []*PsdCombineResult {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdCombineResultCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdCombineResultCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PsdCombineResult.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdCombineResultUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdCombineResultCreateBulk) OnConflict(opts ...sql.ConflictOption) *PsdCombineResultUpsertBulk {
	_c.conflict = opts
	return &PsdCombineResultUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PsdCombineResult.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdCombineResultCreateBulk) OnConflictColumns(columns ...string) *PsdCombineResultUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdCombineResultUpsertBulk{
		create: _c,
	}
}

// PsdCombineResultUpsertBulk is the builder for "upsert"-ing
// a bulk of PsdCombineResult nodes.
type PsdCombineResultUpsertBulk struct {
	create *PsdCombineResultCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.PsdCombineResult.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psdcombineresult.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdCombineResultUpsertBulk) UpdateNewValues() *PsdCombineResultUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(psdcombineresult.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(psdcombineresult.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PsdCombineResult.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *PsdCombineResultUpsertBulk) Ignore() *PsdCombineResultUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdCombineResultUpsertBulk) DoNothing() *PsdCombineResultUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdCombineResultCreateBulk.OnConflict
// documentation for more info.
func (u *PsdCombineResultUpsertBulk) Update(set func(*PsdCombineResultUpsert)) *PsdCombineResultUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdCombineResultUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdCombineResultUpsertBulk) SetUpdatedTime(v time.Time) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdCombineResultUpsertBulk) UpdateUpdatedTime() *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetTaskID sets the "task_id" field.
func (u *PsdCombineResultUpsertBulk) SetTaskID(v int64) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetTaskID(v)
	})
}

// AddTaskID adds v to the "task_id" field.
func (u *PsdCombineResultUpsertBulk) AddTaskID(v int64) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.AddTaskID(v)
	})
}

// UpdateTaskID sets the "task_id" field to the value that was provided on create.
func (u *PsdCombineResultUpsertBulk) UpdateTaskID() *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateTaskID()
	})
}

// SetResultURL sets the "result_url" field.
func (u *PsdCombineResultUpsertBulk) SetResultURL(v string) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetResultURL(v)
	})
}

// UpdateResultURL sets the "result_url" field to the value that was provided on create.
func (u *PsdCombineResultUpsertBulk) UpdateResultURL() *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateResultURL()
	})
}

// SetWeight sets the "weight" field.
func (u *PsdCombineResultUpsertBulk) SetWeight(v int32) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetWeight(v)
	})
}

// AddWeight adds v to the "weight" field.
func (u *PsdCombineResultUpsertBulk) AddWeight(v int32) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.AddWeight(v)
	})
}

// UpdateWeight sets the "weight" field to the value that was provided on create.
func (u *PsdCombineResultUpsertBulk) UpdateWeight() *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateWeight()
	})
}

// SetIsCover sets the "is_cover" field.
func (u *PsdCombineResultUpsertBulk) SetIsCover(v int32) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.SetIsCover(v)
	})
}

// AddIsCover adds v to the "is_cover" field.
func (u *PsdCombineResultUpsertBulk) AddIsCover(v int32) *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.AddIsCover(v)
	})
}

// UpdateIsCover sets the "is_cover" field to the value that was provided on create.
func (u *PsdCombineResultUpsertBulk) UpdateIsCover() *PsdCombineResultUpsertBulk {
	return u.Update(func(s *PsdCombineResultUpsert) {
		s.UpdateIsCover()
	})
}

// Exec executes the query.
func (u *PsdCombineResultUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the PsdCombineResultCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdCombineResultCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdCombineResultUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
