// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psdcombineresult"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdCombineResultDelete is the builder for deleting a PsdCombineResult entity.
type PsdCombineResultDelete struct {
	config
	hooks    []Hook
	mutation *PsdCombineResultMutation
}

// Where appends a list predicates to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDelete) Where(ps ...predicate.PsdCombineResult) *PsdCombineResultDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *PsdCombineResultDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdCombineResultDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *PsdCombineResultDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(psdcombineresult.Table, sqlgraph.NewFieldSpec(psdcombineresult.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDelete) WhereCreatedTime(v time.Time) *PsdCombineResultDelete {
	_d.Where(psdcombineresult.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDelete) WhereUpdatedTime(v time.Time) *PsdCombineResultDelete {
	_d.Where(psdcombineresult.UpdatedTime(v))
	return _d
}

// WhereTaskID applies equality check predicate to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDelete) WhereTaskID(v int64) *PsdCombineResultDelete {
	_d.Where(psdcombineresult.TaskID(v))
	return _d
}

// WhereResultURL applies equality check predicate to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDelete) WhereResultURL(v string) *PsdCombineResultDelete {
	_d.Where(psdcombineresult.ResultURL(v))
	return _d
}

// WhereWeight applies equality check predicate to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDelete) WhereWeight(v int32) *PsdCombineResultDelete {
	_d.Where(psdcombineresult.Weight(v))
	return _d
}

// WhereIsCover applies equality check predicate to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDelete) WhereIsCover(v int32) *PsdCombineResultDelete {
	_d.Where(psdcombineresult.IsCover(v))
	return _d
}

// PsdCombineResultDeleteOne is the builder for deleting a single PsdCombineResult entity.
type PsdCombineResultDeleteOne struct {
	_d *PsdCombineResultDelete
}

// Where appends a list predicates to the PsdCombineResultDelete builder.
func (_d *PsdCombineResultDeleteOne) Where(ps ...predicate.PsdCombineResult) *PsdCombineResultDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *PsdCombineResultDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{psdcombineresult.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdCombineResultDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
