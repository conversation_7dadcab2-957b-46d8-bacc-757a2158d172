// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psdcombineresult"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdCombineResultUpdate is the builder for updating PsdCombineResult entities.
type PsdCombineResultUpdate struct {
	config
	hooks    []Hook
	mutation *PsdCombineResultMutation
}

// Where appends a list predicates to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdate) Where(ps ...predicate.PsdCombineResult) *PsdCombineResultUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdCombineResultUpdate) SetUpdatedTime(v time.Time) *PsdCombineResultUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetTaskID sets the "task_id" field.
func (_u *PsdCombineResultUpdate) SetTaskID(v int64) *PsdCombineResultUpdate {
	_u.mutation.ResetTaskID()
	_u.mutation.SetTaskID(v)
	return _u
}

// SetNillableTaskID sets the "task_id" field if the given value is not nil.
func (_u *PsdCombineResultUpdate) SetNillableTaskID(v *int64) *PsdCombineResultUpdate {
	if v != nil {
		_u.SetTaskID(*v)
	}
	return _u
}

// AddTaskID adds value to the "task_id" field.
func (_u *PsdCombineResultUpdate) AddTaskID(v int64) *PsdCombineResultUpdate {
	_u.mutation.AddTaskID(v)
	return _u
}

// SetResultURL sets the "result_url" field.
func (_u *PsdCombineResultUpdate) SetResultURL(v string) *PsdCombineResultUpdate {
	_u.mutation.SetResultURL(v)
	return _u
}

// SetNillableResultURL sets the "result_url" field if the given value is not nil.
func (_u *PsdCombineResultUpdate) SetNillableResultURL(v *string) *PsdCombineResultUpdate {
	if v != nil {
		_u.SetResultURL(*v)
	}
	return _u
}

// SetWeight sets the "weight" field.
func (_u *PsdCombineResultUpdate) SetWeight(v int32) *PsdCombineResultUpdate {
	_u.mutation.ResetWeight()
	_u.mutation.SetWeight(v)
	return _u
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (_u *PsdCombineResultUpdate) SetNillableWeight(v *int32) *PsdCombineResultUpdate {
	if v != nil {
		_u.SetWeight(*v)
	}
	return _u
}

// AddWeight adds value to the "weight" field.
func (_u *PsdCombineResultUpdate) AddWeight(v int32) *PsdCombineResultUpdate {
	_u.mutation.AddWeight(v)
	return _u
}

// SetIsCover sets the "is_cover" field.
func (_u *PsdCombineResultUpdate) SetIsCover(v int32) *PsdCombineResultUpdate {
	_u.mutation.ResetIsCover()
	_u.mutation.SetIsCover(v)
	return _u
}

// SetNillableIsCover sets the "is_cover" field if the given value is not nil.
func (_u *PsdCombineResultUpdate) SetNillableIsCover(v *int32) *PsdCombineResultUpdate {
	if v != nil {
		_u.SetIsCover(*v)
	}
	return _u
}

// AddIsCover adds value to the "is_cover" field.
func (_u *PsdCombineResultUpdate) AddIsCover(v int32) *PsdCombineResultUpdate {
	_u.mutation.AddIsCover(v)
	return _u
}

// Mutation returns the PsdCombineResultMutation object of the builder.
func (_u *PsdCombineResultUpdate) Mutation() *PsdCombineResultMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *PsdCombineResultUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdCombineResultUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *PsdCombineResultUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdCombineResultUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdCombineResultUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psdcombineresult.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdCombineResultUpdate) check() error {
	if v, ok := _u.mutation.ResultURL(); ok {
		if err := psdcombineresult.ResultURLValidator(v); err != nil {
			return &ValidationError{Name: "result_url", err: fmt.Errorf(`db1: validator failed for field "PsdCombineResult.result_url": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdCombineResultUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psdcombineresult.Table, psdcombineresult.Columns, sqlgraph.NewFieldSpec(psdcombineresult.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psdcombineresult.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.TaskID(); ok {
		_spec.SetField(psdcombineresult.FieldTaskID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedTaskID(); ok {
		_spec.AddField(psdcombineresult.FieldTaskID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.ResultURL(); ok {
		_spec.SetField(psdcombineresult.FieldResultURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.Weight(); ok {
		_spec.SetField(psdcombineresult.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedWeight(); ok {
		_spec.AddField(psdcombineresult.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.IsCover(); ok {
		_spec.SetField(psdcombineresult.FieldIsCover, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedIsCover(); ok {
		_spec.AddField(psdcombineresult.FieldIsCover, field.TypeInt32, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psdcombineresult.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// PsdCombineResultUpdateOne is the builder for updating a single PsdCombineResult entity.
type PsdCombineResultUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PsdCombineResultMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdCombineResultUpdateOne) SetUpdatedTime(v time.Time) *PsdCombineResultUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetTaskID sets the "task_id" field.
func (_u *PsdCombineResultUpdateOne) SetTaskID(v int64) *PsdCombineResultUpdateOne {
	_u.mutation.ResetTaskID()
	_u.mutation.SetTaskID(v)
	return _u
}

// SetNillableTaskID sets the "task_id" field if the given value is not nil.
func (_u *PsdCombineResultUpdateOne) SetNillableTaskID(v *int64) *PsdCombineResultUpdateOne {
	if v != nil {
		_u.SetTaskID(*v)
	}
	return _u
}

// AddTaskID adds value to the "task_id" field.
func (_u *PsdCombineResultUpdateOne) AddTaskID(v int64) *PsdCombineResultUpdateOne {
	_u.mutation.AddTaskID(v)
	return _u
}

// SetResultURL sets the "result_url" field.
func (_u *PsdCombineResultUpdateOne) SetResultURL(v string) *PsdCombineResultUpdateOne {
	_u.mutation.SetResultURL(v)
	return _u
}

// SetNillableResultURL sets the "result_url" field if the given value is not nil.
func (_u *PsdCombineResultUpdateOne) SetNillableResultURL(v *string) *PsdCombineResultUpdateOne {
	if v != nil {
		_u.SetResultURL(*v)
	}
	return _u
}

// SetWeight sets the "weight" field.
func (_u *PsdCombineResultUpdateOne) SetWeight(v int32) *PsdCombineResultUpdateOne {
	_u.mutation.ResetWeight()
	_u.mutation.SetWeight(v)
	return _u
}

// SetNillableWeight sets the "weight" field if the given value is not nil.
func (_u *PsdCombineResultUpdateOne) SetNillableWeight(v *int32) *PsdCombineResultUpdateOne {
	if v != nil {
		_u.SetWeight(*v)
	}
	return _u
}

// AddWeight adds value to the "weight" field.
func (_u *PsdCombineResultUpdateOne) AddWeight(v int32) *PsdCombineResultUpdateOne {
	_u.mutation.AddWeight(v)
	return _u
}

// SetIsCover sets the "is_cover" field.
func (_u *PsdCombineResultUpdateOne) SetIsCover(v int32) *PsdCombineResultUpdateOne {
	_u.mutation.ResetIsCover()
	_u.mutation.SetIsCover(v)
	return _u
}

// SetNillableIsCover sets the "is_cover" field if the given value is not nil.
func (_u *PsdCombineResultUpdateOne) SetNillableIsCover(v *int32) *PsdCombineResultUpdateOne {
	if v != nil {
		_u.SetIsCover(*v)
	}
	return _u
}

// AddIsCover adds value to the "is_cover" field.
func (_u *PsdCombineResultUpdateOne) AddIsCover(v int32) *PsdCombineResultUpdateOne {
	_u.mutation.AddIsCover(v)
	return _u
}

// Mutation returns the PsdCombineResultMutation object of the builder.
func (_u *PsdCombineResultUpdateOne) Mutation() *PsdCombineResultMutation {
	return _u.mutation
}

// Where appends a list predicates to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdateOne) Where(ps ...predicate.PsdCombineResult) *PsdCombineResultUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *PsdCombineResultUpdateOne) Select(field string, fields ...string) *PsdCombineResultUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated PsdCombineResult entity.
func (_u *PsdCombineResultUpdateOne) Save(ctx context.Context) (*PsdCombineResult, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdCombineResultUpdateOne) SaveX(ctx context.Context) *PsdCombineResult {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *PsdCombineResultUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdCombineResultUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdCombineResultUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psdcombineresult.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdCombineResultUpdateOne) check() error {
	if v, ok := _u.mutation.ResultURL(); ok {
		if err := psdcombineresult.ResultURLValidator(v); err != nil {
			return &ValidationError{Name: "result_url", err: fmt.Errorf(`db1: validator failed for field "PsdCombineResult.result_url": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdCombineResultUpdateOne) sqlSave(ctx context.Context) (_node *PsdCombineResult, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psdcombineresult.Table, psdcombineresult.Columns, sqlgraph.NewFieldSpec(psdcombineresult.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "PsdCombineResult.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, psdcombineresult.FieldID)
		for _, f := range fields {
			if !psdcombineresult.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != psdcombineresult.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psdcombineresult.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.TaskID(); ok {
		_spec.SetField(psdcombineresult.FieldTaskID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedTaskID(); ok {
		_spec.AddField(psdcombineresult.FieldTaskID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.ResultURL(); ok {
		_spec.SetField(psdcombineresult.FieldResultURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.Weight(); ok {
		_spec.SetField(psdcombineresult.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedWeight(); ok {
		_spec.AddField(psdcombineresult.FieldWeight, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.IsCover(); ok {
		_spec.SetField(psdcombineresult.FieldIsCover, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedIsCover(); ok {
		_spec.AddField(psdcombineresult.FieldIsCover, field.TypeInt32, value)
	}
	_node = &PsdCombineResult{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psdcombineresult.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdate) WhereCreatedTime(v time.Time) *PsdCombineResultUpdate {
	_u.Where(psdcombineresult.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdate) WhereUpdatedTime(v time.Time) *PsdCombineResultUpdate {
	_u.Where(psdcombineresult.UpdatedTime(v))
	return _u
}

// WhereTaskID applies equality check predicate to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdate) WhereTaskID(v int64) *PsdCombineResultUpdate {
	_u.Where(psdcombineresult.TaskID(v))
	return _u
}

// WhereResultURL applies equality check predicate to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdate) WhereResultURL(v string) *PsdCombineResultUpdate {
	_u.Where(psdcombineresult.ResultURL(v))
	return _u
}

// WhereWeight applies equality check predicate to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdate) WhereWeight(v int32) *PsdCombineResultUpdate {
	_u.Where(psdcombineresult.Weight(v))
	return _u
}

// WhereIsCover applies equality check predicate to the PsdCombineResultUpdate builder.
func (_u *PsdCombineResultUpdate) WhereIsCover(v int32) *PsdCombineResultUpdate {
	_u.Where(psdcombineresult.IsCover(v))
	return _u
}
