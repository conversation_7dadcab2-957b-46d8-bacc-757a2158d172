// Code generated by ent, DO NOT EDIT.

package db1

import (
	"fmt"
	"omnix/ddl/db1/psdcombinetask"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// PsdCombineTask is the model entity for the PsdCombineTask schema.
type PsdCombineTask struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedTime time.Time `json:"created_time,omitempty"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty"`
	// 所属模型分组id
	PsdGroupID int64 `json:"psd_group_id,omitempty"`
	// 素材URL
	MaterialURL string `json:"material_url,omitempty"`
	// 素材id
	ReferenceID int64 `json:"reference_id,omitempty"`
	// 额外参数，JSON格式
	ExtraParams string `json:"extra_params,omitempty"`
	// 0: pending, 1: processing, 2: completed, 3: failed
	Status string `json:"status,omitempty"`
	// 错误信息
	ErrorMsg     string `json:"error_msg,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*PsdCombineTask) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case psdcombinetask.FieldID, psdcombinetask.FieldPsdGroupID, psdcombinetask.FieldReferenceID:
			values[i] = new(sql.NullInt64)
		case psdcombinetask.FieldMaterialURL, psdcombinetask.FieldExtraParams, psdcombinetask.FieldStatus, psdcombinetask.FieldErrorMsg:
			values[i] = new(sql.NullString)
		case psdcombinetask.FieldCreatedTime, psdcombinetask.FieldUpdatedTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the PsdCombineTask fields.
func (_m *PsdCombineTask) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case psdcombinetask.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case psdcombinetask.FieldCreatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_time", values[i])
			} else if value.Valid {
				_m.CreatedTime = value.Time
			}
		case psdcombinetask.FieldUpdatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_time", values[i])
			} else if value.Valid {
				_m.UpdatedTime = value.Time
			}
		case psdcombinetask.FieldPsdGroupID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field psd_group_id", values[i])
			} else if value.Valid {
				_m.PsdGroupID = value.Int64
			}
		case psdcombinetask.FieldMaterialURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field material_url", values[i])
			} else if value.Valid {
				_m.MaterialURL = value.String
			}
		case psdcombinetask.FieldReferenceID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field reference_id", values[i])
			} else if value.Valid {
				_m.ReferenceID = value.Int64
			}
		case psdcombinetask.FieldExtraParams:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field extra_params", values[i])
			} else if value.Valid {
				_m.ExtraParams = value.String
			}
		case psdcombinetask.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				_m.Status = value.String
			}
		case psdcombinetask.FieldErrorMsg:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field error_msg", values[i])
			} else if value.Valid {
				_m.ErrorMsg = value.String
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the PsdCombineTask.
// This includes values selected through modifiers, order, etc.
func (_m *PsdCombineTask) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this PsdCombineTask.
// Note that you need to call PsdCombineTask.Unwrap() before calling this method if this PsdCombineTask
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *PsdCombineTask) Update() *PsdCombineTaskUpdateOne {
	return NewPsdCombineTaskClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the PsdCombineTask entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *PsdCombineTask) Unwrap() *PsdCombineTask {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("db1: PsdCombineTask is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *PsdCombineTask) String() string {
	var builder strings.Builder
	builder.WriteString("PsdCombineTask(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("created_time=")
	builder.WriteString(_m.CreatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_time=")
	builder.WriteString(_m.UpdatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("psd_group_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.PsdGroupID))
	builder.WriteString(", ")
	builder.WriteString("material_url=")
	builder.WriteString(_m.MaterialURL)
	builder.WriteString(", ")
	builder.WriteString("reference_id=")
	builder.WriteString(fmt.Sprintf("%v", _m.ReferenceID))
	builder.WriteString(", ")
	builder.WriteString("extra_params=")
	builder.WriteString(_m.ExtraParams)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(_m.Status)
	builder.WriteString(", ")
	builder.WriteString("error_msg=")
	builder.WriteString(_m.ErrorMsg)
	builder.WriteByte(')')
	return builder.String()
}

// PsdCombineTasks is a parsable slice of PsdCombineTask.
type PsdCombineTasks []*PsdCombineTask
