// Code generated by ent, DO NOT EDIT.

package psdcombinetask

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the psdcombinetask type in the database.
	Label = "psd_combine_task"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedTime holds the string denoting the created_time field in the database.
	FieldCreatedTime = "created_time"
	// FieldUpdatedTime holds the string denoting the updated_time field in the database.
	FieldUpdatedTime = "updated_time"
	// FieldPsdGroupID holds the string denoting the psd_group_id field in the database.
	FieldPsdGroupID = "psd_group_id"
	// FieldMaterialURL holds the string denoting the material_url field in the database.
	FieldMaterialURL = "material_url"
	// FieldReferenceID holds the string denoting the reference_id field in the database.
	FieldReferenceID = "reference_id"
	// FieldExtraParams holds the string denoting the extra_params field in the database.
	FieldExtraParams = "extra_params"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldErrorMsg holds the string denoting the error_msg field in the database.
	FieldErrorMsg = "error_msg"
	// Table holds the table name of the psdcombinetask in the database.
	Table = "psd_combine_tasks"
)

// Columns holds all SQL columns for psdcombinetask fields.
var Columns = []string{
	FieldID,
	FieldCreatedTime,
	FieldUpdatedTime,
	FieldPsdGroupID,
	FieldMaterialURL,
	FieldReferenceID,
	FieldExtraParams,
	FieldStatus,
	FieldErrorMsg,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedTime holds the default value on creation for the "created_time" field.
	DefaultCreatedTime func() time.Time
	// DefaultUpdatedTime holds the default value on creation for the "updated_time" field.
	DefaultUpdatedTime func() time.Time
	// UpdateDefaultUpdatedTime holds the default value on update for the "updated_time" field.
	UpdateDefaultUpdatedTime func() time.Time
	// MaterialURLValidator is a validator for the "material_url" field. It is called by the builders before save.
	MaterialURLValidator func(string) error
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus string
)

// OrderOption defines the ordering options for the PsdCombineTask queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedTime orders the results by the created_time field.
func ByCreatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedTime, opts...).ToFunc()
}

// ByUpdatedTime orders the results by the updated_time field.
func ByUpdatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedTime, opts...).ToFunc()
}

// ByPsdGroupID orders the results by the psd_group_id field.
func ByPsdGroupID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPsdGroupID, opts...).ToFunc()
}

// ByMaterialURL orders the results by the material_url field.
func ByMaterialURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaterialURL, opts...).ToFunc()
}

// ByReferenceID orders the results by the reference_id field.
func ByReferenceID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReferenceID, opts...).ToFunc()
}

// ByExtraParams orders the results by the extra_params field.
func ByExtraParams(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExtraParams, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByErrorMsg orders the results by the error_msg field.
func ByErrorMsg(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorMsg, opts...).ToFunc()
}
