// Code generated by ent, DO NOT EDIT.

package psdcombinetask

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldUpdatedTime, v))
}

// PsdGroupID applies equality check predicate on the "psd_group_id" field. It's identical to PsdGroupIDEQ.
func PsdGroupID(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldPsdGroupID, v))
}

// MaterialURL applies equality check predicate on the "material_url" field. It's identical to MaterialURLEQ.
func MaterialURL(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldMaterialURL, v))
}

// ReferenceID applies equality check predicate on the "reference_id" field. It's identical to ReferenceIDEQ.
func ReferenceID(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldReferenceID, v))
}

// ExtraParams applies equality check predicate on the "extra_params" field. It's identical to ExtraParamsEQ.
func ExtraParams(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldExtraParams, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldStatus, v))
}

// ErrorMsg applies equality check predicate on the "error_msg" field. It's identical to ErrorMsgEQ.
func ErrorMsg(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldErrorMsg, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldUpdatedTime, v))
}

// PsdGroupIDEQ applies the EQ predicate on the "psd_group_id" field.
func PsdGroupIDEQ(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldPsdGroupID, v))
}

// PsdGroupIDNEQ applies the NEQ predicate on the "psd_group_id" field.
func PsdGroupIDNEQ(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldPsdGroupID, v))
}

// PsdGroupIDIn applies the In predicate on the "psd_group_id" field.
func PsdGroupIDIn(vs ...int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldPsdGroupID, vs...))
}

// PsdGroupIDNotIn applies the NotIn predicate on the "psd_group_id" field.
func PsdGroupIDNotIn(vs ...int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldPsdGroupID, vs...))
}

// PsdGroupIDGT applies the GT predicate on the "psd_group_id" field.
func PsdGroupIDGT(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldPsdGroupID, v))
}

// PsdGroupIDGTE applies the GTE predicate on the "psd_group_id" field.
func PsdGroupIDGTE(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldPsdGroupID, v))
}

// PsdGroupIDLT applies the LT predicate on the "psd_group_id" field.
func PsdGroupIDLT(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldPsdGroupID, v))
}

// PsdGroupIDLTE applies the LTE predicate on the "psd_group_id" field.
func PsdGroupIDLTE(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldPsdGroupID, v))
}

// MaterialURLEQ applies the EQ predicate on the "material_url" field.
func MaterialURLEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldMaterialURL, v))
}

// MaterialURLNEQ applies the NEQ predicate on the "material_url" field.
func MaterialURLNEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldMaterialURL, v))
}

// MaterialURLIn applies the In predicate on the "material_url" field.
func MaterialURLIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldMaterialURL, vs...))
}

// MaterialURLNotIn applies the NotIn predicate on the "material_url" field.
func MaterialURLNotIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldMaterialURL, vs...))
}

// MaterialURLGT applies the GT predicate on the "material_url" field.
func MaterialURLGT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldMaterialURL, v))
}

// MaterialURLGTE applies the GTE predicate on the "material_url" field.
func MaterialURLGTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldMaterialURL, v))
}

// MaterialURLLT applies the LT predicate on the "material_url" field.
func MaterialURLLT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldMaterialURL, v))
}

// MaterialURLLTE applies the LTE predicate on the "material_url" field.
func MaterialURLLTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldMaterialURL, v))
}

// MaterialURLContains applies the Contains predicate on the "material_url" field.
func MaterialURLContains(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContains(FieldMaterialURL, v))
}

// MaterialURLHasPrefix applies the HasPrefix predicate on the "material_url" field.
func MaterialURLHasPrefix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasPrefix(FieldMaterialURL, v))
}

// MaterialURLHasSuffix applies the HasSuffix predicate on the "material_url" field.
func MaterialURLHasSuffix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasSuffix(FieldMaterialURL, v))
}

// MaterialURLEqualFold applies the EqualFold predicate on the "material_url" field.
func MaterialURLEqualFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEqualFold(FieldMaterialURL, v))
}

// MaterialURLContainsFold applies the ContainsFold predicate on the "material_url" field.
func MaterialURLContainsFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContainsFold(FieldMaterialURL, v))
}

// ReferenceIDEQ applies the EQ predicate on the "reference_id" field.
func ReferenceIDEQ(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldReferenceID, v))
}

// ReferenceIDNEQ applies the NEQ predicate on the "reference_id" field.
func ReferenceIDNEQ(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldReferenceID, v))
}

// ReferenceIDIn applies the In predicate on the "reference_id" field.
func ReferenceIDIn(vs ...int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldReferenceID, vs...))
}

// ReferenceIDNotIn applies the NotIn predicate on the "reference_id" field.
func ReferenceIDNotIn(vs ...int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldReferenceID, vs...))
}

// ReferenceIDGT applies the GT predicate on the "reference_id" field.
func ReferenceIDGT(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldReferenceID, v))
}

// ReferenceIDGTE applies the GTE predicate on the "reference_id" field.
func ReferenceIDGTE(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldReferenceID, v))
}

// ReferenceIDLT applies the LT predicate on the "reference_id" field.
func ReferenceIDLT(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldReferenceID, v))
}

// ReferenceIDLTE applies the LTE predicate on the "reference_id" field.
func ReferenceIDLTE(v int64) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldReferenceID, v))
}

// ReferenceIDIsNil applies the IsNil predicate on the "reference_id" field.
func ReferenceIDIsNil() predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIsNull(FieldReferenceID))
}

// ReferenceIDNotNil applies the NotNil predicate on the "reference_id" field.
func ReferenceIDNotNil() predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotNull(FieldReferenceID))
}

// ExtraParamsEQ applies the EQ predicate on the "extra_params" field.
func ExtraParamsEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldExtraParams, v))
}

// ExtraParamsNEQ applies the NEQ predicate on the "extra_params" field.
func ExtraParamsNEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldExtraParams, v))
}

// ExtraParamsIn applies the In predicate on the "extra_params" field.
func ExtraParamsIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldExtraParams, vs...))
}

// ExtraParamsNotIn applies the NotIn predicate on the "extra_params" field.
func ExtraParamsNotIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldExtraParams, vs...))
}

// ExtraParamsGT applies the GT predicate on the "extra_params" field.
func ExtraParamsGT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldExtraParams, v))
}

// ExtraParamsGTE applies the GTE predicate on the "extra_params" field.
func ExtraParamsGTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldExtraParams, v))
}

// ExtraParamsLT applies the LT predicate on the "extra_params" field.
func ExtraParamsLT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldExtraParams, v))
}

// ExtraParamsLTE applies the LTE predicate on the "extra_params" field.
func ExtraParamsLTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldExtraParams, v))
}

// ExtraParamsContains applies the Contains predicate on the "extra_params" field.
func ExtraParamsContains(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContains(FieldExtraParams, v))
}

// ExtraParamsHasPrefix applies the HasPrefix predicate on the "extra_params" field.
func ExtraParamsHasPrefix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasPrefix(FieldExtraParams, v))
}

// ExtraParamsHasSuffix applies the HasSuffix predicate on the "extra_params" field.
func ExtraParamsHasSuffix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasSuffix(FieldExtraParams, v))
}

// ExtraParamsIsNil applies the IsNil predicate on the "extra_params" field.
func ExtraParamsIsNil() predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIsNull(FieldExtraParams))
}

// ExtraParamsNotNil applies the NotNil predicate on the "extra_params" field.
func ExtraParamsNotNil() predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotNull(FieldExtraParams))
}

// ExtraParamsEqualFold applies the EqualFold predicate on the "extra_params" field.
func ExtraParamsEqualFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEqualFold(FieldExtraParams, v))
}

// ExtraParamsContainsFold applies the ContainsFold predicate on the "extra_params" field.
func ExtraParamsContainsFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContainsFold(FieldExtraParams, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldStatus, v))
}

// StatusContains applies the Contains predicate on the "status" field.
func StatusContains(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContains(FieldStatus, v))
}

// StatusHasPrefix applies the HasPrefix predicate on the "status" field.
func StatusHasPrefix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasPrefix(FieldStatus, v))
}

// StatusHasSuffix applies the HasSuffix predicate on the "status" field.
func StatusHasSuffix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasSuffix(FieldStatus, v))
}

// StatusEqualFold applies the EqualFold predicate on the "status" field.
func StatusEqualFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEqualFold(FieldStatus, v))
}

// StatusContainsFold applies the ContainsFold predicate on the "status" field.
func StatusContainsFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContainsFold(FieldStatus, v))
}

// ErrorMsgEQ applies the EQ predicate on the "error_msg" field.
func ErrorMsgEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEQ(FieldErrorMsg, v))
}

// ErrorMsgNEQ applies the NEQ predicate on the "error_msg" field.
func ErrorMsgNEQ(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNEQ(FieldErrorMsg, v))
}

// ErrorMsgIn applies the In predicate on the "error_msg" field.
func ErrorMsgIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIn(FieldErrorMsg, vs...))
}

// ErrorMsgNotIn applies the NotIn predicate on the "error_msg" field.
func ErrorMsgNotIn(vs ...string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotIn(FieldErrorMsg, vs...))
}

// ErrorMsgGT applies the GT predicate on the "error_msg" field.
func ErrorMsgGT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGT(FieldErrorMsg, v))
}

// ErrorMsgGTE applies the GTE predicate on the "error_msg" field.
func ErrorMsgGTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldGTE(FieldErrorMsg, v))
}

// ErrorMsgLT applies the LT predicate on the "error_msg" field.
func ErrorMsgLT(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLT(FieldErrorMsg, v))
}

// ErrorMsgLTE applies the LTE predicate on the "error_msg" field.
func ErrorMsgLTE(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldLTE(FieldErrorMsg, v))
}

// ErrorMsgContains applies the Contains predicate on the "error_msg" field.
func ErrorMsgContains(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContains(FieldErrorMsg, v))
}

// ErrorMsgHasPrefix applies the HasPrefix predicate on the "error_msg" field.
func ErrorMsgHasPrefix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasPrefix(FieldErrorMsg, v))
}

// ErrorMsgHasSuffix applies the HasSuffix predicate on the "error_msg" field.
func ErrorMsgHasSuffix(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldHasSuffix(FieldErrorMsg, v))
}

// ErrorMsgIsNil applies the IsNil predicate on the "error_msg" field.
func ErrorMsgIsNil() predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldIsNull(FieldErrorMsg))
}

// ErrorMsgNotNil applies the NotNil predicate on the "error_msg" field.
func ErrorMsgNotNil() predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldNotNull(FieldErrorMsg))
}

// ErrorMsgEqualFold applies the EqualFold predicate on the "error_msg" field.
func ErrorMsgEqualFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldEqualFold(FieldErrorMsg, v))
}

// ErrorMsgContainsFold applies the ContainsFold predicate on the "error_msg" field.
func ErrorMsgContainsFold(v string) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.FieldContainsFold(FieldErrorMsg, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.PsdCombineTask) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.PsdCombineTask) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.PsdCombineTask) predicate.PsdCombineTask {
	return predicate.PsdCombineTask(sql.NotPredicates(p))
}
