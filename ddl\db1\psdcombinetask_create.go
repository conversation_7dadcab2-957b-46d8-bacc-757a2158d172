// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/psdcombinetask"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdCombineTaskCreate is the builder for creating a PsdCombineTask entity.
type PsdCombineTaskCreate struct {
	config
	mutation *PsdCombineTaskMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *PsdCombineTaskCreate) SetCreatedTime(v time.Time) *PsdCombineTaskCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *PsdCombineTaskCreate) SetNillableCreatedTime(v *time.Time) *PsdCombineTaskCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *PsdCombineTaskCreate) SetUpdatedTime(v time.Time) *PsdCombineTaskCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *PsdCombineTaskCreate) SetNillableUpdatedTime(v *time.Time) *PsdCombineTaskCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetPsdGroupID sets the "psd_group_id" field.
func (_c *PsdCombineTaskCreate) SetPsdGroupID(v int64) *PsdCombineTaskCreate {
	_c.mutation.SetPsdGroupID(v)
	return _c
}

// SetMaterialURL sets the "material_url" field.
func (_c *PsdCombineTaskCreate) SetMaterialURL(v string) *PsdCombineTaskCreate {
	_c.mutation.SetMaterialURL(v)
	return _c
}

// SetReferenceID sets the "reference_id" field.
func (_c *PsdCombineTaskCreate) SetReferenceID(v int64) *PsdCombineTaskCreate {
	_c.mutation.SetReferenceID(v)
	return _c
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (_c *PsdCombineTaskCreate) SetNillableReferenceID(v *int64) *PsdCombineTaskCreate {
	if v != nil {
		_c.SetReferenceID(*v)
	}
	return _c
}

// SetExtraParams sets the "extra_params" field.
func (_c *PsdCombineTaskCreate) SetExtraParams(v string) *PsdCombineTaskCreate {
	_c.mutation.SetExtraParams(v)
	return _c
}

// SetNillableExtraParams sets the "extra_params" field if the given value is not nil.
func (_c *PsdCombineTaskCreate) SetNillableExtraParams(v *string) *PsdCombineTaskCreate {
	if v != nil {
		_c.SetExtraParams(*v)
	}
	return _c
}

// SetStatus sets the "status" field.
func (_c *PsdCombineTaskCreate) SetStatus(v string) *PsdCombineTaskCreate {
	_c.mutation.SetStatus(v)
	return _c
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_c *PsdCombineTaskCreate) SetNillableStatus(v *string) *PsdCombineTaskCreate {
	if v != nil {
		_c.SetStatus(*v)
	}
	return _c
}

// SetErrorMsg sets the "error_msg" field.
func (_c *PsdCombineTaskCreate) SetErrorMsg(v string) *PsdCombineTaskCreate {
	_c.mutation.SetErrorMsg(v)
	return _c
}

// SetNillableErrorMsg sets the "error_msg" field if the given value is not nil.
func (_c *PsdCombineTaskCreate) SetNillableErrorMsg(v *string) *PsdCombineTaskCreate {
	if v != nil {
		_c.SetErrorMsg(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *PsdCombineTaskCreate) SetID(v int64) *PsdCombineTaskCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the PsdCombineTaskMutation object of the builder.
func (_c *PsdCombineTaskCreate) Mutation() *PsdCombineTaskMutation {
	return _c.mutation
}

// Save creates the PsdCombineTask in the database.
func (_c *PsdCombineTaskCreate) Save(ctx context.Context) (*PsdCombineTask, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *PsdCombineTaskCreate) SaveX(ctx context.Context) *PsdCombineTask {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdCombineTaskCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdCombineTaskCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *PsdCombineTaskCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := psdcombinetask.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := psdcombinetask.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
	if _, ok := _c.mutation.Status(); !ok {
		v := psdcombinetask.DefaultStatus
		_c.mutation.SetStatus(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *PsdCombineTaskCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "PsdCombineTask.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "PsdCombineTask.updated_time"`)}
	}
	if _, ok := _c.mutation.PsdGroupID(); !ok {
		return &ValidationError{Name: "psd_group_id", err: errors.New(`db1: missing required field "PsdCombineTask.psd_group_id"`)}
	}
	if _, ok := _c.mutation.MaterialURL(); !ok {
		return &ValidationError{Name: "material_url", err: errors.New(`db1: missing required field "PsdCombineTask.material_url"`)}
	}
	if v, ok := _c.mutation.MaterialURL(); ok {
		if err := psdcombinetask.MaterialURLValidator(v); err != nil {
			return &ValidationError{Name: "material_url", err: fmt.Errorf(`db1: validator failed for field "PsdCombineTask.material_url": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`db1: missing required field "PsdCombineTask.status"`)}
	}
	return nil
}

func (_c *PsdCombineTaskCreate) sqlSave(ctx context.Context) (*PsdCombineTask, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *PsdCombineTaskCreate) createSpec() (*PsdCombineTask, *sqlgraph.CreateSpec) {
	var (
		_node = &PsdCombineTask{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(psdcombinetask.Table, sqlgraph.NewFieldSpec(psdcombinetask.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(psdcombinetask.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(psdcombinetask.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.PsdGroupID(); ok {
		_spec.SetField(psdcombinetask.FieldPsdGroupID, field.TypeInt64, value)
		_node.PsdGroupID = value
	}
	if value, ok := _c.mutation.MaterialURL(); ok {
		_spec.SetField(psdcombinetask.FieldMaterialURL, field.TypeString, value)
		_node.MaterialURL = value
	}
	if value, ok := _c.mutation.ReferenceID(); ok {
		_spec.SetField(psdcombinetask.FieldReferenceID, field.TypeInt64, value)
		_node.ReferenceID = value
	}
	if value, ok := _c.mutation.ExtraParams(); ok {
		_spec.SetField(psdcombinetask.FieldExtraParams, field.TypeString, value)
		_node.ExtraParams = value
	}
	if value, ok := _c.mutation.Status(); ok {
		_spec.SetField(psdcombinetask.FieldStatus, field.TypeString, value)
		_node.Status = value
	}
	if value, ok := _c.mutation.ErrorMsg(); ok {
		_spec.SetField(psdcombinetask.FieldErrorMsg, field.TypeString, value)
		_node.ErrorMsg = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PsdCombineTask.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdCombineTaskUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdCombineTaskCreate) OnConflict(opts ...sql.ConflictOption) *PsdCombineTaskUpsertOne {
	_c.conflict = opts
	return &PsdCombineTaskUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PsdCombineTask.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdCombineTaskCreate) OnConflictColumns(columns ...string) *PsdCombineTaskUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdCombineTaskUpsertOne{
		create: _c,
	}
}

type (
	// PsdCombineTaskUpsertOne is the builder for "upsert"-ing
	//  one PsdCombineTask node.
	PsdCombineTaskUpsertOne struct {
		create *PsdCombineTaskCreate
	}

	// PsdCombineTaskUpsert is the "OnConflict" setter.
	PsdCombineTaskUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdCombineTaskUpsert) SetUpdatedTime(v time.Time) *PsdCombineTaskUpsert {
	u.Set(psdcombinetask.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdCombineTaskUpsert) UpdateUpdatedTime() *PsdCombineTaskUpsert {
	u.SetExcluded(psdcombinetask.FieldUpdatedTime)
	return u
}

// SetPsdGroupID sets the "psd_group_id" field.
func (u *PsdCombineTaskUpsert) SetPsdGroupID(v int64) *PsdCombineTaskUpsert {
	u.Set(psdcombinetask.FieldPsdGroupID, v)
	return u
}

// UpdatePsdGroupID sets the "psd_group_id" field to the value that was provided on create.
func (u *PsdCombineTaskUpsert) UpdatePsdGroupID() *PsdCombineTaskUpsert {
	u.SetExcluded(psdcombinetask.FieldPsdGroupID)
	return u
}

// AddPsdGroupID adds v to the "psd_group_id" field.
func (u *PsdCombineTaskUpsert) AddPsdGroupID(v int64) *PsdCombineTaskUpsert {
	u.Add(psdcombinetask.FieldPsdGroupID, v)
	return u
}

// SetMaterialURL sets the "material_url" field.
func (u *PsdCombineTaskUpsert) SetMaterialURL(v string) *PsdCombineTaskUpsert {
	u.Set(psdcombinetask.FieldMaterialURL, v)
	return u
}

// UpdateMaterialURL sets the "material_url" field to the value that was provided on create.
func (u *PsdCombineTaskUpsert) UpdateMaterialURL() *PsdCombineTaskUpsert {
	u.SetExcluded(psdcombinetask.FieldMaterialURL)
	return u
}

// SetReferenceID sets the "reference_id" field.
func (u *PsdCombineTaskUpsert) SetReferenceID(v int64) *PsdCombineTaskUpsert {
	u.Set(psdcombinetask.FieldReferenceID, v)
	return u
}

// UpdateReferenceID sets the "reference_id" field to the value that was provided on create.
func (u *PsdCombineTaskUpsert) UpdateReferenceID() *PsdCombineTaskUpsert {
	u.SetExcluded(psdcombinetask.FieldReferenceID)
	return u
}

// AddReferenceID adds v to the "reference_id" field.
func (u *PsdCombineTaskUpsert) AddReferenceID(v int64) *PsdCombineTaskUpsert {
	u.Add(psdcombinetask.FieldReferenceID, v)
	return u
}

// ClearReferenceID clears the value of the "reference_id" field.
func (u *PsdCombineTaskUpsert) ClearReferenceID() *PsdCombineTaskUpsert {
	u.SetNull(psdcombinetask.FieldReferenceID)
	return u
}

// SetExtraParams sets the "extra_params" field.
func (u *PsdCombineTaskUpsert) SetExtraParams(v string) *PsdCombineTaskUpsert {
	u.Set(psdcombinetask.FieldExtraParams, v)
	return u
}

// UpdateExtraParams sets the "extra_params" field to the value that was provided on create.
func (u *PsdCombineTaskUpsert) UpdateExtraParams() *PsdCombineTaskUpsert {
	u.SetExcluded(psdcombinetask.FieldExtraParams)
	return u
}

// ClearExtraParams clears the value of the "extra_params" field.
func (u *PsdCombineTaskUpsert) ClearExtraParams() *PsdCombineTaskUpsert {
	u.SetNull(psdcombinetask.FieldExtraParams)
	return u
}

// SetStatus sets the "status" field.
func (u *PsdCombineTaskUpsert) SetStatus(v string) *PsdCombineTaskUpsert {
	u.Set(psdcombinetask.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PsdCombineTaskUpsert) UpdateStatus() *PsdCombineTaskUpsert {
	u.SetExcluded(psdcombinetask.FieldStatus)
	return u
}

// SetErrorMsg sets the "error_msg" field.
func (u *PsdCombineTaskUpsert) SetErrorMsg(v string) *PsdCombineTaskUpsert {
	u.Set(psdcombinetask.FieldErrorMsg, v)
	return u
}

// UpdateErrorMsg sets the "error_msg" field to the value that was provided on create.
func (u *PsdCombineTaskUpsert) UpdateErrorMsg() *PsdCombineTaskUpsert {
	u.SetExcluded(psdcombinetask.FieldErrorMsg)
	return u
}

// ClearErrorMsg clears the value of the "error_msg" field.
func (u *PsdCombineTaskUpsert) ClearErrorMsg() *PsdCombineTaskUpsert {
	u.SetNull(psdcombinetask.FieldErrorMsg)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.PsdCombineTask.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psdcombinetask.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdCombineTaskUpsertOne) UpdateNewValues() *PsdCombineTaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(psdcombinetask.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(psdcombinetask.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PsdCombineTask.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *PsdCombineTaskUpsertOne) Ignore() *PsdCombineTaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdCombineTaskUpsertOne) DoNothing() *PsdCombineTaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdCombineTaskCreate.OnConflict
// documentation for more info.
func (u *PsdCombineTaskUpsertOne) Update(set func(*PsdCombineTaskUpsert)) *PsdCombineTaskUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdCombineTaskUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdCombineTaskUpsertOne) SetUpdatedTime(v time.Time) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertOne) UpdateUpdatedTime() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetPsdGroupID sets the "psd_group_id" field.
func (u *PsdCombineTaskUpsertOne) SetPsdGroupID(v int64) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetPsdGroupID(v)
	})
}

// AddPsdGroupID adds v to the "psd_group_id" field.
func (u *PsdCombineTaskUpsertOne) AddPsdGroupID(v int64) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.AddPsdGroupID(v)
	})
}

// UpdatePsdGroupID sets the "psd_group_id" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertOne) UpdatePsdGroupID() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdatePsdGroupID()
	})
}

// SetMaterialURL sets the "material_url" field.
func (u *PsdCombineTaskUpsertOne) SetMaterialURL(v string) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetMaterialURL(v)
	})
}

// UpdateMaterialURL sets the "material_url" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertOne) UpdateMaterialURL() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateMaterialURL()
	})
}

// SetReferenceID sets the "reference_id" field.
func (u *PsdCombineTaskUpsertOne) SetReferenceID(v int64) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetReferenceID(v)
	})
}

// AddReferenceID adds v to the "reference_id" field.
func (u *PsdCombineTaskUpsertOne) AddReferenceID(v int64) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.AddReferenceID(v)
	})
}

// UpdateReferenceID sets the "reference_id" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertOne) UpdateReferenceID() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateReferenceID()
	})
}

// ClearReferenceID clears the value of the "reference_id" field.
func (u *PsdCombineTaskUpsertOne) ClearReferenceID() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.ClearReferenceID()
	})
}

// SetExtraParams sets the "extra_params" field.
func (u *PsdCombineTaskUpsertOne) SetExtraParams(v string) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetExtraParams(v)
	})
}

// UpdateExtraParams sets the "extra_params" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertOne) UpdateExtraParams() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateExtraParams()
	})
}

// ClearExtraParams clears the value of the "extra_params" field.
func (u *PsdCombineTaskUpsertOne) ClearExtraParams() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.ClearExtraParams()
	})
}

// SetStatus sets the "status" field.
func (u *PsdCombineTaskUpsertOne) SetStatus(v string) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertOne) UpdateStatus() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateStatus()
	})
}

// SetErrorMsg sets the "error_msg" field.
func (u *PsdCombineTaskUpsertOne) SetErrorMsg(v string) *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetErrorMsg(v)
	})
}

// UpdateErrorMsg sets the "error_msg" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertOne) UpdateErrorMsg() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateErrorMsg()
	})
}

// ClearErrorMsg clears the value of the "error_msg" field.
func (u *PsdCombineTaskUpsertOne) ClearErrorMsg() *PsdCombineTaskUpsertOne {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.ClearErrorMsg()
	})
}

// Exec executes the query.
func (u *PsdCombineTaskUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdCombineTaskCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdCombineTaskUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *PsdCombineTaskUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *PsdCombineTaskUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// PsdCombineTaskCreateBulk is the builder for creating many PsdCombineTask entities in bulk.
type PsdCombineTaskCreateBulk struct {
	config
	err      error
	builders []*PsdCombineTaskCreate
	conflict []sql.ConflictOption
}

// Save creates the PsdCombineTask entities in the database.
func (_c *PsdCombineTaskCreateBulk) Save(ctx context.Context) ([]*PsdCombineTask, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*PsdCombineTask, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PsdCombineTaskMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *PsdCombineTaskCreateBulk) SaveX(ctx context.Context) []*PsdCombineTask {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdCombineTaskCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdCombineTaskCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PsdCombineTask.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdCombineTaskUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdCombineTaskCreateBulk) OnConflict(opts ...sql.ConflictOption) *PsdCombineTaskUpsertBulk {
	_c.conflict = opts
	return &PsdCombineTaskUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PsdCombineTask.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdCombineTaskCreateBulk) OnConflictColumns(columns ...string) *PsdCombineTaskUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdCombineTaskUpsertBulk{
		create: _c,
	}
}

// PsdCombineTaskUpsertBulk is the builder for "upsert"-ing
// a bulk of PsdCombineTask nodes.
type PsdCombineTaskUpsertBulk struct {
	create *PsdCombineTaskCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.PsdCombineTask.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psdcombinetask.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdCombineTaskUpsertBulk) UpdateNewValues() *PsdCombineTaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(psdcombinetask.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(psdcombinetask.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PsdCombineTask.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *PsdCombineTaskUpsertBulk) Ignore() *PsdCombineTaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdCombineTaskUpsertBulk) DoNothing() *PsdCombineTaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdCombineTaskCreateBulk.OnConflict
// documentation for more info.
func (u *PsdCombineTaskUpsertBulk) Update(set func(*PsdCombineTaskUpsert)) *PsdCombineTaskUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdCombineTaskUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdCombineTaskUpsertBulk) SetUpdatedTime(v time.Time) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertBulk) UpdateUpdatedTime() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetPsdGroupID sets the "psd_group_id" field.
func (u *PsdCombineTaskUpsertBulk) SetPsdGroupID(v int64) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetPsdGroupID(v)
	})
}

// AddPsdGroupID adds v to the "psd_group_id" field.
func (u *PsdCombineTaskUpsertBulk) AddPsdGroupID(v int64) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.AddPsdGroupID(v)
	})
}

// UpdatePsdGroupID sets the "psd_group_id" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertBulk) UpdatePsdGroupID() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdatePsdGroupID()
	})
}

// SetMaterialURL sets the "material_url" field.
func (u *PsdCombineTaskUpsertBulk) SetMaterialURL(v string) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetMaterialURL(v)
	})
}

// UpdateMaterialURL sets the "material_url" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertBulk) UpdateMaterialURL() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateMaterialURL()
	})
}

// SetReferenceID sets the "reference_id" field.
func (u *PsdCombineTaskUpsertBulk) SetReferenceID(v int64) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetReferenceID(v)
	})
}

// AddReferenceID adds v to the "reference_id" field.
func (u *PsdCombineTaskUpsertBulk) AddReferenceID(v int64) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.AddReferenceID(v)
	})
}

// UpdateReferenceID sets the "reference_id" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertBulk) UpdateReferenceID() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateReferenceID()
	})
}

// ClearReferenceID clears the value of the "reference_id" field.
func (u *PsdCombineTaskUpsertBulk) ClearReferenceID() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.ClearReferenceID()
	})
}

// SetExtraParams sets the "extra_params" field.
func (u *PsdCombineTaskUpsertBulk) SetExtraParams(v string) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetExtraParams(v)
	})
}

// UpdateExtraParams sets the "extra_params" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertBulk) UpdateExtraParams() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateExtraParams()
	})
}

// ClearExtraParams clears the value of the "extra_params" field.
func (u *PsdCombineTaskUpsertBulk) ClearExtraParams() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.ClearExtraParams()
	})
}

// SetStatus sets the "status" field.
func (u *PsdCombineTaskUpsertBulk) SetStatus(v string) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertBulk) UpdateStatus() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateStatus()
	})
}

// SetErrorMsg sets the "error_msg" field.
func (u *PsdCombineTaskUpsertBulk) SetErrorMsg(v string) *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.SetErrorMsg(v)
	})
}

// UpdateErrorMsg sets the "error_msg" field to the value that was provided on create.
func (u *PsdCombineTaskUpsertBulk) UpdateErrorMsg() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.UpdateErrorMsg()
	})
}

// ClearErrorMsg clears the value of the "error_msg" field.
func (u *PsdCombineTaskUpsertBulk) ClearErrorMsg() *PsdCombineTaskUpsertBulk {
	return u.Update(func(s *PsdCombineTaskUpsert) {
		s.ClearErrorMsg()
	})
}

// Exec executes the query.
func (u *PsdCombineTaskUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the PsdCombineTaskCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdCombineTaskCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdCombineTaskUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
