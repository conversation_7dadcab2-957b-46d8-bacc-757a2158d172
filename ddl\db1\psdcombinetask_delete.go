// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psdcombinetask"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdCombineTaskDelete is the builder for deleting a PsdCombineTask entity.
type PsdCombineTaskDelete struct {
	config
	hooks    []Hook
	mutation *PsdCombineTaskMutation
}

// Where appends a list predicates to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) Where(ps ...predicate.PsdCombineTask) *PsdCombineTaskDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *PsdCombineTaskDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdCombineTaskDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *PsdCombineTaskDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(psdcombinetask.Table, sqlgraph.NewFieldSpec(psdcombinetask.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WhereCreatedTime(v time.Time) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WhereUpdatedTime(v time.Time) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.UpdatedTime(v))
	return _d
}

// WherePsdGroupID applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WherePsdGroupID(v int64) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.PsdGroupID(v))
	return _d
}

// WhereMaterialURL applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WhereMaterialURL(v string) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.MaterialURL(v))
	return _d
}

// WhereReferenceID applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WhereReferenceID(v int64) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.ReferenceID(v))
	return _d
}

// WhereExtraParams applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WhereExtraParams(v string) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.ExtraParams(v))
	return _d
}

// WhereStatus applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WhereStatus(v string) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.Status(v))
	return _d
}

// WhereErrorMsg applies equality check predicate to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDelete) WhereErrorMsg(v string) *PsdCombineTaskDelete {
	_d.Where(psdcombinetask.ErrorMsg(v))
	return _d
}

// PsdCombineTaskDeleteOne is the builder for deleting a single PsdCombineTask entity.
type PsdCombineTaskDeleteOne struct {
	_d *PsdCombineTaskDelete
}

// Where appends a list predicates to the PsdCombineTaskDelete builder.
func (_d *PsdCombineTaskDeleteOne) Where(ps ...predicate.PsdCombineTask) *PsdCombineTaskDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *PsdCombineTaskDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{psdcombinetask.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdCombineTaskDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
