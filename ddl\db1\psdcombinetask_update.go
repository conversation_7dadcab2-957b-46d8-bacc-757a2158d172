// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psdcombinetask"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdCombineTaskUpdate is the builder for updating PsdCombineTask entities.
type PsdCombineTaskUpdate struct {
	config
	hooks    []Hook
	mutation *PsdCombineTaskMutation
}

// Where appends a list predicates to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) Where(ps ...predicate.PsdCombineTask) *PsdCombineTaskUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdCombineTaskUpdate) SetUpdatedTime(v time.Time) *PsdCombineTaskUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetPsdGroupID sets the "psd_group_id" field.
func (_u *PsdCombineTaskUpdate) SetPsdGroupID(v int64) *PsdCombineTaskUpdate {
	_u.mutation.ResetPsdGroupID()
	_u.mutation.SetPsdGroupID(v)
	return _u
}

// SetNillablePsdGroupID sets the "psd_group_id" field if the given value is not nil.
func (_u *PsdCombineTaskUpdate) SetNillablePsdGroupID(v *int64) *PsdCombineTaskUpdate {
	if v != nil {
		_u.SetPsdGroupID(*v)
	}
	return _u
}

// AddPsdGroupID adds value to the "psd_group_id" field.
func (_u *PsdCombineTaskUpdate) AddPsdGroupID(v int64) *PsdCombineTaskUpdate {
	_u.mutation.AddPsdGroupID(v)
	return _u
}

// SetMaterialURL sets the "material_url" field.
func (_u *PsdCombineTaskUpdate) SetMaterialURL(v string) *PsdCombineTaskUpdate {
	_u.mutation.SetMaterialURL(v)
	return _u
}

// SetNillableMaterialURL sets the "material_url" field if the given value is not nil.
func (_u *PsdCombineTaskUpdate) SetNillableMaterialURL(v *string) *PsdCombineTaskUpdate {
	if v != nil {
		_u.SetMaterialURL(*v)
	}
	return _u
}

// SetReferenceID sets the "reference_id" field.
func (_u *PsdCombineTaskUpdate) SetReferenceID(v int64) *PsdCombineTaskUpdate {
	_u.mutation.ResetReferenceID()
	_u.mutation.SetReferenceID(v)
	return _u
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (_u *PsdCombineTaskUpdate) SetNillableReferenceID(v *int64) *PsdCombineTaskUpdate {
	if v != nil {
		_u.SetReferenceID(*v)
	}
	return _u
}

// AddReferenceID adds value to the "reference_id" field.
func (_u *PsdCombineTaskUpdate) AddReferenceID(v int64) *PsdCombineTaskUpdate {
	_u.mutation.AddReferenceID(v)
	return _u
}

// ClearReferenceID clears the value of the "reference_id" field.
func (_u *PsdCombineTaskUpdate) ClearReferenceID() *PsdCombineTaskUpdate {
	_u.mutation.ClearReferenceID()
	return _u
}

// SetExtraParams sets the "extra_params" field.
func (_u *PsdCombineTaskUpdate) SetExtraParams(v string) *PsdCombineTaskUpdate {
	_u.mutation.SetExtraParams(v)
	return _u
}

// SetNillableExtraParams sets the "extra_params" field if the given value is not nil.
func (_u *PsdCombineTaskUpdate) SetNillableExtraParams(v *string) *PsdCombineTaskUpdate {
	if v != nil {
		_u.SetExtraParams(*v)
	}
	return _u
}

// ClearExtraParams clears the value of the "extra_params" field.
func (_u *PsdCombineTaskUpdate) ClearExtraParams() *PsdCombineTaskUpdate {
	_u.mutation.ClearExtraParams()
	return _u
}

// SetStatus sets the "status" field.
func (_u *PsdCombineTaskUpdate) SetStatus(v string) *PsdCombineTaskUpdate {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *PsdCombineTaskUpdate) SetNillableStatus(v *string) *PsdCombineTaskUpdate {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetErrorMsg sets the "error_msg" field.
func (_u *PsdCombineTaskUpdate) SetErrorMsg(v string) *PsdCombineTaskUpdate {
	_u.mutation.SetErrorMsg(v)
	return _u
}

// SetNillableErrorMsg sets the "error_msg" field if the given value is not nil.
func (_u *PsdCombineTaskUpdate) SetNillableErrorMsg(v *string) *PsdCombineTaskUpdate {
	if v != nil {
		_u.SetErrorMsg(*v)
	}
	return _u
}

// ClearErrorMsg clears the value of the "error_msg" field.
func (_u *PsdCombineTaskUpdate) ClearErrorMsg() *PsdCombineTaskUpdate {
	_u.mutation.ClearErrorMsg()
	return _u
}

// Mutation returns the PsdCombineTaskMutation object of the builder.
func (_u *PsdCombineTaskUpdate) Mutation() *PsdCombineTaskMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *PsdCombineTaskUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdCombineTaskUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *PsdCombineTaskUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdCombineTaskUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdCombineTaskUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psdcombinetask.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdCombineTaskUpdate) check() error {
	if v, ok := _u.mutation.MaterialURL(); ok {
		if err := psdcombinetask.MaterialURLValidator(v); err != nil {
			return &ValidationError{Name: "material_url", err: fmt.Errorf(`db1: validator failed for field "PsdCombineTask.material_url": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdCombineTaskUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psdcombinetask.Table, psdcombinetask.Columns, sqlgraph.NewFieldSpec(psdcombinetask.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psdcombinetask.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.PsdGroupID(); ok {
		_spec.SetField(psdcombinetask.FieldPsdGroupID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedPsdGroupID(); ok {
		_spec.AddField(psdcombinetask.FieldPsdGroupID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.MaterialURL(); ok {
		_spec.SetField(psdcombinetask.FieldMaterialURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.ReferenceID(); ok {
		_spec.SetField(psdcombinetask.FieldReferenceID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedReferenceID(); ok {
		_spec.AddField(psdcombinetask.FieldReferenceID, field.TypeInt64, value)
	}
	if _u.mutation.ReferenceIDCleared() {
		_spec.ClearField(psdcombinetask.FieldReferenceID, field.TypeInt64)
	}
	if value, ok := _u.mutation.ExtraParams(); ok {
		_spec.SetField(psdcombinetask.FieldExtraParams, field.TypeString, value)
	}
	if _u.mutation.ExtraParamsCleared() {
		_spec.ClearField(psdcombinetask.FieldExtraParams, field.TypeString)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(psdcombinetask.FieldStatus, field.TypeString, value)
	}
	if value, ok := _u.mutation.ErrorMsg(); ok {
		_spec.SetField(psdcombinetask.FieldErrorMsg, field.TypeString, value)
	}
	if _u.mutation.ErrorMsgCleared() {
		_spec.ClearField(psdcombinetask.FieldErrorMsg, field.TypeString)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psdcombinetask.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// PsdCombineTaskUpdateOne is the builder for updating a single PsdCombineTask entity.
type PsdCombineTaskUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PsdCombineTaskMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdCombineTaskUpdateOne) SetUpdatedTime(v time.Time) *PsdCombineTaskUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetPsdGroupID sets the "psd_group_id" field.
func (_u *PsdCombineTaskUpdateOne) SetPsdGroupID(v int64) *PsdCombineTaskUpdateOne {
	_u.mutation.ResetPsdGroupID()
	_u.mutation.SetPsdGroupID(v)
	return _u
}

// SetNillablePsdGroupID sets the "psd_group_id" field if the given value is not nil.
func (_u *PsdCombineTaskUpdateOne) SetNillablePsdGroupID(v *int64) *PsdCombineTaskUpdateOne {
	if v != nil {
		_u.SetPsdGroupID(*v)
	}
	return _u
}

// AddPsdGroupID adds value to the "psd_group_id" field.
func (_u *PsdCombineTaskUpdateOne) AddPsdGroupID(v int64) *PsdCombineTaskUpdateOne {
	_u.mutation.AddPsdGroupID(v)
	return _u
}

// SetMaterialURL sets the "material_url" field.
func (_u *PsdCombineTaskUpdateOne) SetMaterialURL(v string) *PsdCombineTaskUpdateOne {
	_u.mutation.SetMaterialURL(v)
	return _u
}

// SetNillableMaterialURL sets the "material_url" field if the given value is not nil.
func (_u *PsdCombineTaskUpdateOne) SetNillableMaterialURL(v *string) *PsdCombineTaskUpdateOne {
	if v != nil {
		_u.SetMaterialURL(*v)
	}
	return _u
}

// SetReferenceID sets the "reference_id" field.
func (_u *PsdCombineTaskUpdateOne) SetReferenceID(v int64) *PsdCombineTaskUpdateOne {
	_u.mutation.ResetReferenceID()
	_u.mutation.SetReferenceID(v)
	return _u
}

// SetNillableReferenceID sets the "reference_id" field if the given value is not nil.
func (_u *PsdCombineTaskUpdateOne) SetNillableReferenceID(v *int64) *PsdCombineTaskUpdateOne {
	if v != nil {
		_u.SetReferenceID(*v)
	}
	return _u
}

// AddReferenceID adds value to the "reference_id" field.
func (_u *PsdCombineTaskUpdateOne) AddReferenceID(v int64) *PsdCombineTaskUpdateOne {
	_u.mutation.AddReferenceID(v)
	return _u
}

// ClearReferenceID clears the value of the "reference_id" field.
func (_u *PsdCombineTaskUpdateOne) ClearReferenceID() *PsdCombineTaskUpdateOne {
	_u.mutation.ClearReferenceID()
	return _u
}

// SetExtraParams sets the "extra_params" field.
func (_u *PsdCombineTaskUpdateOne) SetExtraParams(v string) *PsdCombineTaskUpdateOne {
	_u.mutation.SetExtraParams(v)
	return _u
}

// SetNillableExtraParams sets the "extra_params" field if the given value is not nil.
func (_u *PsdCombineTaskUpdateOne) SetNillableExtraParams(v *string) *PsdCombineTaskUpdateOne {
	if v != nil {
		_u.SetExtraParams(*v)
	}
	return _u
}

// ClearExtraParams clears the value of the "extra_params" field.
func (_u *PsdCombineTaskUpdateOne) ClearExtraParams() *PsdCombineTaskUpdateOne {
	_u.mutation.ClearExtraParams()
	return _u
}

// SetStatus sets the "status" field.
func (_u *PsdCombineTaskUpdateOne) SetStatus(v string) *PsdCombineTaskUpdateOne {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *PsdCombineTaskUpdateOne) SetNillableStatus(v *string) *PsdCombineTaskUpdateOne {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetErrorMsg sets the "error_msg" field.
func (_u *PsdCombineTaskUpdateOne) SetErrorMsg(v string) *PsdCombineTaskUpdateOne {
	_u.mutation.SetErrorMsg(v)
	return _u
}

// SetNillableErrorMsg sets the "error_msg" field if the given value is not nil.
func (_u *PsdCombineTaskUpdateOne) SetNillableErrorMsg(v *string) *PsdCombineTaskUpdateOne {
	if v != nil {
		_u.SetErrorMsg(*v)
	}
	return _u
}

// ClearErrorMsg clears the value of the "error_msg" field.
func (_u *PsdCombineTaskUpdateOne) ClearErrorMsg() *PsdCombineTaskUpdateOne {
	_u.mutation.ClearErrorMsg()
	return _u
}

// Mutation returns the PsdCombineTaskMutation object of the builder.
func (_u *PsdCombineTaskUpdateOne) Mutation() *PsdCombineTaskMutation {
	return _u.mutation
}

// Where appends a list predicates to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdateOne) Where(ps ...predicate.PsdCombineTask) *PsdCombineTaskUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *PsdCombineTaskUpdateOne) Select(field string, fields ...string) *PsdCombineTaskUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated PsdCombineTask entity.
func (_u *PsdCombineTaskUpdateOne) Save(ctx context.Context) (*PsdCombineTask, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdCombineTaskUpdateOne) SaveX(ctx context.Context) *PsdCombineTask {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *PsdCombineTaskUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdCombineTaskUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdCombineTaskUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psdcombinetask.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdCombineTaskUpdateOne) check() error {
	if v, ok := _u.mutation.MaterialURL(); ok {
		if err := psdcombinetask.MaterialURLValidator(v); err != nil {
			return &ValidationError{Name: "material_url", err: fmt.Errorf(`db1: validator failed for field "PsdCombineTask.material_url": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdCombineTaskUpdateOne) sqlSave(ctx context.Context) (_node *PsdCombineTask, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psdcombinetask.Table, psdcombinetask.Columns, sqlgraph.NewFieldSpec(psdcombinetask.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "PsdCombineTask.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, psdcombinetask.FieldID)
		for _, f := range fields {
			if !psdcombinetask.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != psdcombinetask.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psdcombinetask.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.PsdGroupID(); ok {
		_spec.SetField(psdcombinetask.FieldPsdGroupID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedPsdGroupID(); ok {
		_spec.AddField(psdcombinetask.FieldPsdGroupID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.MaterialURL(); ok {
		_spec.SetField(psdcombinetask.FieldMaterialURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.ReferenceID(); ok {
		_spec.SetField(psdcombinetask.FieldReferenceID, field.TypeInt64, value)
	}
	if value, ok := _u.mutation.AddedReferenceID(); ok {
		_spec.AddField(psdcombinetask.FieldReferenceID, field.TypeInt64, value)
	}
	if _u.mutation.ReferenceIDCleared() {
		_spec.ClearField(psdcombinetask.FieldReferenceID, field.TypeInt64)
	}
	if value, ok := _u.mutation.ExtraParams(); ok {
		_spec.SetField(psdcombinetask.FieldExtraParams, field.TypeString, value)
	}
	if _u.mutation.ExtraParamsCleared() {
		_spec.ClearField(psdcombinetask.FieldExtraParams, field.TypeString)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(psdcombinetask.FieldStatus, field.TypeString, value)
	}
	if value, ok := _u.mutation.ErrorMsg(); ok {
		_spec.SetField(psdcombinetask.FieldErrorMsg, field.TypeString, value)
	}
	if _u.mutation.ErrorMsgCleared() {
		_spec.ClearField(psdcombinetask.FieldErrorMsg, field.TypeString)
	}
	_node = &PsdCombineTask{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psdcombinetask.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WhereCreatedTime(v time.Time) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WhereUpdatedTime(v time.Time) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.UpdatedTime(v))
	return _u
}

// WherePsdGroupID applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WherePsdGroupID(v int64) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.PsdGroupID(v))
	return _u
}

// WhereMaterialURL applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WhereMaterialURL(v string) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.MaterialURL(v))
	return _u
}

// WhereReferenceID applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WhereReferenceID(v int64) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.ReferenceID(v))
	return _u
}

// WhereExtraParams applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WhereExtraParams(v string) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.ExtraParams(v))
	return _u
}

// WhereStatus applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WhereStatus(v string) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.Status(v))
	return _u
}

// WhereErrorMsg applies equality check predicate to the PsdCombineTaskUpdate builder.
func (_u *PsdCombineTaskUpdate) WhereErrorMsg(v string) *PsdCombineTaskUpdate {
	_u.Where(psdcombinetask.ErrorMsg(v))
	return _u
}
