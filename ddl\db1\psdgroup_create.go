// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/psdgroup"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdGroupCreate is the builder for creating a PsdGroup entity.
type PsdGroupCreate struct {
	config
	mutation *PsdGroupMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *PsdGroupCreate) SetCreatedTime(v time.Time) *PsdGroupCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *PsdGroupCreate) SetNillableCreatedTime(v *time.Time) *PsdGroupCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *PsdGroupCreate) SetUpdatedTime(v time.Time) *PsdGroupCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *PsdGroupCreate) SetNillableUpdatedTime(v *time.Time) *PsdGroupCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetName sets the "name" field.
func (_c *PsdGroupCreate) SetName(v string) *PsdGroupCreate {
	_c.mutation.SetName(v)
	return _c
}

// SetDesc sets the "desc" field.
func (_c *PsdGroupCreate) SetDesc(v string) *PsdGroupCreate {
	_c.mutation.SetDesc(v)
	return _c
}

// SetNillableDesc sets the "desc" field if the given value is not nil.
func (_c *PsdGroupCreate) SetNillableDesc(v *string) *PsdGroupCreate {
	if v != nil {
		_c.SetDesc(*v)
	}
	return _c
}

// SetID sets the "id" field.
func (_c *PsdGroupCreate) SetID(v int64) *PsdGroupCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the PsdGroupMutation object of the builder.
func (_c *PsdGroupCreate) Mutation() *PsdGroupMutation {
	return _c.mutation
}

// Save creates the PsdGroup in the database.
func (_c *PsdGroupCreate) Save(ctx context.Context) (*PsdGroup, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *PsdGroupCreate) SaveX(ctx context.Context) *PsdGroup {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdGroupCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdGroupCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *PsdGroupCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := psdgroup.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := psdgroup.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *PsdGroupCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "PsdGroup.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "PsdGroup.updated_time"`)}
	}
	if _, ok := _c.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`db1: missing required field "PsdGroup.name"`)}
	}
	if v, ok := _c.mutation.Name(); ok {
		if err := psdgroup.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`db1: validator failed for field "PsdGroup.name": %w`, err)}
		}
	}
	return nil
}

func (_c *PsdGroupCreate) sqlSave(ctx context.Context) (*PsdGroup, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *PsdGroupCreate) createSpec() (*PsdGroup, *sqlgraph.CreateSpec) {
	var (
		_node = &PsdGroup{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(psdgroup.Table, sqlgraph.NewFieldSpec(psdgroup.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(psdgroup.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(psdgroup.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.Name(); ok {
		_spec.SetField(psdgroup.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := _c.mutation.Desc(); ok {
		_spec.SetField(psdgroup.FieldDesc, field.TypeString, value)
		_node.Desc = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PsdGroup.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdGroupUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdGroupCreate) OnConflict(opts ...sql.ConflictOption) *PsdGroupUpsertOne {
	_c.conflict = opts
	return &PsdGroupUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PsdGroup.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdGroupCreate) OnConflictColumns(columns ...string) *PsdGroupUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdGroupUpsertOne{
		create: _c,
	}
}

type (
	// PsdGroupUpsertOne is the builder for "upsert"-ing
	//  one PsdGroup node.
	PsdGroupUpsertOne struct {
		create *PsdGroupCreate
	}

	// PsdGroupUpsert is the "OnConflict" setter.
	PsdGroupUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdGroupUpsert) SetUpdatedTime(v time.Time) *PsdGroupUpsert {
	u.Set(psdgroup.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdGroupUpsert) UpdateUpdatedTime() *PsdGroupUpsert {
	u.SetExcluded(psdgroup.FieldUpdatedTime)
	return u
}

// SetName sets the "name" field.
func (u *PsdGroupUpsert) SetName(v string) *PsdGroupUpsert {
	u.Set(psdgroup.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *PsdGroupUpsert) UpdateName() *PsdGroupUpsert {
	u.SetExcluded(psdgroup.FieldName)
	return u
}

// SetDesc sets the "desc" field.
func (u *PsdGroupUpsert) SetDesc(v string) *PsdGroupUpsert {
	u.Set(psdgroup.FieldDesc, v)
	return u
}

// UpdateDesc sets the "desc" field to the value that was provided on create.
func (u *PsdGroupUpsert) UpdateDesc() *PsdGroupUpsert {
	u.SetExcluded(psdgroup.FieldDesc)
	return u
}

// ClearDesc clears the value of the "desc" field.
func (u *PsdGroupUpsert) ClearDesc() *PsdGroupUpsert {
	u.SetNull(psdgroup.FieldDesc)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.PsdGroup.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psdgroup.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdGroupUpsertOne) UpdateNewValues() *PsdGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(psdgroup.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(psdgroup.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PsdGroup.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *PsdGroupUpsertOne) Ignore() *PsdGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdGroupUpsertOne) DoNothing() *PsdGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdGroupCreate.OnConflict
// documentation for more info.
func (u *PsdGroupUpsertOne) Update(set func(*PsdGroupUpsert)) *PsdGroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdGroupUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdGroupUpsertOne) SetUpdatedTime(v time.Time) *PsdGroupUpsertOne {
	return u.Update(func(s *PsdGroupUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdGroupUpsertOne) UpdateUpdatedTime() *PsdGroupUpsertOne {
	return u.Update(func(s *PsdGroupUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetName sets the "name" field.
func (u *PsdGroupUpsertOne) SetName(v string) *PsdGroupUpsertOne {
	return u.Update(func(s *PsdGroupUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *PsdGroupUpsertOne) UpdateName() *PsdGroupUpsertOne {
	return u.Update(func(s *PsdGroupUpsert) {
		s.UpdateName()
	})
}

// SetDesc sets the "desc" field.
func (u *PsdGroupUpsertOne) SetDesc(v string) *PsdGroupUpsertOne {
	return u.Update(func(s *PsdGroupUpsert) {
		s.SetDesc(v)
	})
}

// UpdateDesc sets the "desc" field to the value that was provided on create.
func (u *PsdGroupUpsertOne) UpdateDesc() *PsdGroupUpsertOne {
	return u.Update(func(s *PsdGroupUpsert) {
		s.UpdateDesc()
	})
}

// ClearDesc clears the value of the "desc" field.
func (u *PsdGroupUpsertOne) ClearDesc() *PsdGroupUpsertOne {
	return u.Update(func(s *PsdGroupUpsert) {
		s.ClearDesc()
	})
}

// Exec executes the query.
func (u *PsdGroupUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdGroupCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdGroupUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *PsdGroupUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *PsdGroupUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// PsdGroupCreateBulk is the builder for creating many PsdGroup entities in bulk.
type PsdGroupCreateBulk struct {
	config
	err      error
	builders []*PsdGroupCreate
	conflict []sql.ConflictOption
}

// Save creates the PsdGroup entities in the database.
func (_c *PsdGroupCreateBulk) Save(ctx context.Context) ([]*PsdGroup, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*PsdGroup, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PsdGroupMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *PsdGroupCreateBulk) SaveX(ctx context.Context) []*PsdGroup {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *PsdGroupCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *PsdGroupCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.PsdGroup.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PsdGroupUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *PsdGroupCreateBulk) OnConflict(opts ...sql.ConflictOption) *PsdGroupUpsertBulk {
	_c.conflict = opts
	return &PsdGroupUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.PsdGroup.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *PsdGroupCreateBulk) OnConflictColumns(columns ...string) *PsdGroupUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &PsdGroupUpsertBulk{
		create: _c,
	}
}

// PsdGroupUpsertBulk is the builder for "upsert"-ing
// a bulk of PsdGroup nodes.
type PsdGroupUpsertBulk struct {
	create *PsdGroupCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.PsdGroup.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(psdgroup.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PsdGroupUpsertBulk) UpdateNewValues() *PsdGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(psdgroup.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(psdgroup.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.PsdGroup.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *PsdGroupUpsertBulk) Ignore() *PsdGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PsdGroupUpsertBulk) DoNothing() *PsdGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PsdGroupCreateBulk.OnConflict
// documentation for more info.
func (u *PsdGroupUpsertBulk) Update(set func(*PsdGroupUpsert)) *PsdGroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PsdGroupUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *PsdGroupUpsertBulk) SetUpdatedTime(v time.Time) *PsdGroupUpsertBulk {
	return u.Update(func(s *PsdGroupUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *PsdGroupUpsertBulk) UpdateUpdatedTime() *PsdGroupUpsertBulk {
	return u.Update(func(s *PsdGroupUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetName sets the "name" field.
func (u *PsdGroupUpsertBulk) SetName(v string) *PsdGroupUpsertBulk {
	return u.Update(func(s *PsdGroupUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *PsdGroupUpsertBulk) UpdateName() *PsdGroupUpsertBulk {
	return u.Update(func(s *PsdGroupUpsert) {
		s.UpdateName()
	})
}

// SetDesc sets the "desc" field.
func (u *PsdGroupUpsertBulk) SetDesc(v string) *PsdGroupUpsertBulk {
	return u.Update(func(s *PsdGroupUpsert) {
		s.SetDesc(v)
	})
}

// UpdateDesc sets the "desc" field to the value that was provided on create.
func (u *PsdGroupUpsertBulk) UpdateDesc() *PsdGroupUpsertBulk {
	return u.Update(func(s *PsdGroupUpsert) {
		s.UpdateDesc()
	})
}

// ClearDesc clears the value of the "desc" field.
func (u *PsdGroupUpsertBulk) ClearDesc() *PsdGroupUpsertBulk {
	return u.Update(func(s *PsdGroupUpsert) {
		s.ClearDesc()
	})
}

// Exec executes the query.
func (u *PsdGroupUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the PsdGroupCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for PsdGroupCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PsdGroupUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
