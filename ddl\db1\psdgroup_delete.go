// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psdgroup"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdGroupDelete is the builder for deleting a PsdGroup entity.
type PsdGroupDelete struct {
	config
	hooks    []Hook
	mutation *PsdGroupMutation
}

// Where appends a list predicates to the PsdGroupDelete builder.
func (_d *PsdGroupDelete) Where(ps ...predicate.PsdGroup) *PsdGroupDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *PsdGroupDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdGroupDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *PsdGroupDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(psdgroup.Table, sqlgraph.NewFieldSpec(psdgroup.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the PsdGroupDelete builder.
func (_d *PsdGroupDelete) WhereCreatedTime(v time.Time) *PsdGroupDelete {
	_d.Where(psdgroup.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the PsdGroupDelete builder.
func (_d *PsdGroupDelete) WhereUpdatedTime(v time.Time) *PsdGroupDelete {
	_d.Where(psdgroup.UpdatedTime(v))
	return _d
}

// WhereName applies equality check predicate to the PsdGroupDelete builder.
func (_d *PsdGroupDelete) WhereName(v string) *PsdGroupDelete {
	_d.Where(psdgroup.Name(v))
	return _d
}

// WhereDesc applies equality check predicate to the PsdGroupDelete builder.
func (_d *PsdGroupDelete) WhereDesc(v string) *PsdGroupDelete {
	_d.Where(psdgroup.Desc(v))
	return _d
}

// PsdGroupDeleteOne is the builder for deleting a single PsdGroup entity.
type PsdGroupDeleteOne struct {
	_d *PsdGroupDelete
}

// Where appends a list predicates to the PsdGroupDelete builder.
func (_d *PsdGroupDeleteOne) Where(ps ...predicate.PsdGroup) *PsdGroupDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *PsdGroupDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{psdgroup.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *PsdGroupDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
