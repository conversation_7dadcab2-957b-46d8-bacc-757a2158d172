// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"fmt"
	"math"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psdgroup"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdGroupQuery is the builder for querying PsdGroup entities.
type PsdGroupQuery struct {
	config
	ctx        *QueryContext
	order      []psdgroup.OrderOption
	inters     []Interceptor
	predicates []predicate.PsdGroup
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the PsdGroupQuery builder.
func (_q *PsdGroupQuery) Where(ps ...predicate.PsdGroup) *PsdGroupQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *PsdGroupQuery) Limit(limit int) *PsdGroupQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *PsdGroupQuery) Offset(offset int) *PsdGroupQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *PsdGroupQuery) Unique(unique bool) *PsdGroupQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *PsdGroupQuery) Order(o ...psdgroup.OrderOption) *PsdGroupQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// First returns the first PsdGroup entity from the query.
// Returns a *NotFoundError when no PsdGroup was found.
func (_q *PsdGroupQuery) First(ctx context.Context) (*PsdGroup, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{psdgroup.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *PsdGroupQuery) FirstX(ctx context.Context) *PsdGroup {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first PsdGroup ID from the query.
// Returns a *NotFoundError when no PsdGroup ID was found.
func (_q *PsdGroupQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{psdgroup.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *PsdGroupQuery) FirstIDX(ctx context.Context) int64 {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single PsdGroup entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one PsdGroup entity is found.
// Returns a *NotFoundError when no PsdGroup entities are found.
func (_q *PsdGroupQuery) Only(ctx context.Context) (*PsdGroup, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{psdgroup.Label}
	default:
		return nil, &NotSingularError{psdgroup.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *PsdGroupQuery) OnlyX(ctx context.Context) *PsdGroup {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only PsdGroup ID in the query.
// Returns a *NotSingularError when more than one PsdGroup ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *PsdGroupQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{psdgroup.Label}
	default:
		err = &NotSingularError{psdgroup.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *PsdGroupQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of PsdGroups.
func (_q *PsdGroupQuery) All(ctx context.Context) ([]*PsdGroup, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*PsdGroup, *PsdGroupQuery]()
	return withInterceptors[[]*PsdGroup](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *PsdGroupQuery) AllX(ctx context.Context) []*PsdGroup {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of PsdGroup IDs.
func (_q *PsdGroupQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(psdgroup.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *PsdGroupQuery) IDsX(ctx context.Context) []int64 {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *PsdGroupQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*PsdGroupQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *PsdGroupQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *PsdGroupQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("db1: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *PsdGroupQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the PsdGroupQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *PsdGroupQuery) Clone() *PsdGroupQuery {
	if _q == nil {
		return nil
	}
	return &PsdGroupQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]psdgroup.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.PsdGroup{}, _q.predicates...),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.PsdGroup.Query().
//		GroupBy(psdgroup.FieldCreatedTime).
//		Aggregate(db1.Count()).
//		Scan(ctx, &v)
func (_q *PsdGroupQuery) GroupBy(field string, fields ...string) *PsdGroupGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &PsdGroupGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = psdgroup.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//	}
//
//	client.PsdGroup.Query().
//		Select(psdgroup.FieldCreatedTime).
//		Scan(ctx, &v)
func (_q *PsdGroupQuery) Select(fields ...string) *PsdGroupSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &PsdGroupSelect{PsdGroupQuery: _q}
	sbuild.label = psdgroup.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a PsdGroupSelect configured with the given aggregations.
func (_q *PsdGroupQuery) Aggregate(fns ...AggregateFunc) *PsdGroupSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *PsdGroupQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("db1: uninitialized interceptor (forgotten import db1/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !psdgroup.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *PsdGroupQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*PsdGroup, error) {
	var (
		nodes = []*PsdGroup{}
		_spec = _q.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*PsdGroup).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &PsdGroup{config: _q.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (_q *PsdGroupQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *PsdGroupQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(psdgroup.Table, psdgroup.Columns, sqlgraph.NewFieldSpec(psdgroup.FieldID, field.TypeInt64))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, psdgroup.FieldID)
		for i := range fields {
			if fields[i] != psdgroup.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *PsdGroupQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(psdgroup.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = psdgroup.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WhereCreatedTime applies equality check predicate on the "created_time" field
func (_m *PsdGroupQuery) WhereCreatedTime(v time.Time) *PsdGroupQuery {
	_m.Where(psdgroup.CreatedTime(v))
	return _m
}

// WhereUpdatedTime applies equality check predicate on the "updated_time" field
func (_m *PsdGroupQuery) WhereUpdatedTime(v time.Time) *PsdGroupQuery {
	_m.Where(psdgroup.UpdatedTime(v))
	return _m
}

// WhereName applies equality check predicate on the "name" field
func (_m *PsdGroupQuery) WhereName(v string) *PsdGroupQuery {
	_m.Where(psdgroup.Name(v))
	return _m
}

// WhereDesc applies equality check predicate on the "desc" field
func (_m *PsdGroupQuery) WhereDesc(v string) *PsdGroupQuery {
	_m.Where(psdgroup.Desc(v))
	return _m
}

// PsdGroupGroupBy is the group-by builder for PsdGroup entities.
type PsdGroupGroupBy struct {
	selector
	build *PsdGroupQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *PsdGroupGroupBy) Aggregate(fns ...AggregateFunc) *PsdGroupGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *PsdGroupGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PsdGroupQuery, *PsdGroupGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *PsdGroupGroupBy) sqlScan(ctx context.Context, root *PsdGroupQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// PsdGroupSelect is the builder for selecting fields of PsdGroup entities.
type PsdGroupSelect struct {
	*PsdGroupQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *PsdGroupSelect) Aggregate(fns ...AggregateFunc) *PsdGroupSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *PsdGroupSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*PsdGroupQuery, *PsdGroupSelect](ctx, _s.PsdGroupQuery, _s, _s.inters, v)
}

func (_s *PsdGroupSelect) sqlScan(ctx context.Context, root *PsdGroupQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
