// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"errors"
	"fmt"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/psdgroup"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PsdGroupUpdate is the builder for updating PsdGroup entities.
type PsdGroupUpdate struct {
	config
	hooks    []Hook
	mutation *PsdGroupMutation
}

// Where appends a list predicates to the PsdGroupUpdate builder.
func (_u *PsdGroupUpdate) Where(ps ...predicate.PsdGroup) *PsdGroupUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdGroupUpdate) SetUpdatedTime(v time.Time) *PsdGroupUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetName sets the "name" field.
func (_u *PsdGroupUpdate) SetName(v string) *PsdGroupUpdate {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *PsdGroupUpdate) SetNillableName(v *string) *PsdGroupUpdate {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetDesc sets the "desc" field.
func (_u *PsdGroupUpdate) SetDesc(v string) *PsdGroupUpdate {
	_u.mutation.SetDesc(v)
	return _u
}

// SetNillableDesc sets the "desc" field if the given value is not nil.
func (_u *PsdGroupUpdate) SetNillableDesc(v *string) *PsdGroupUpdate {
	if v != nil {
		_u.SetDesc(*v)
	}
	return _u
}

// ClearDesc clears the value of the "desc" field.
func (_u *PsdGroupUpdate) ClearDesc() *PsdGroupUpdate {
	_u.mutation.ClearDesc()
	return _u
}

// Mutation returns the PsdGroupMutation object of the builder.
func (_u *PsdGroupUpdate) Mutation() *PsdGroupMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *PsdGroupUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdGroupUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *PsdGroupUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdGroupUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdGroupUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psdgroup.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdGroupUpdate) check() error {
	if v, ok := _u.mutation.Name(); ok {
		if err := psdgroup.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`db1: validator failed for field "PsdGroup.name": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdGroupUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psdgroup.Table, psdgroup.Columns, sqlgraph.NewFieldSpec(psdgroup.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psdgroup.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(psdgroup.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Desc(); ok {
		_spec.SetField(psdgroup.FieldDesc, field.TypeString, value)
	}
	if _u.mutation.DescCleared() {
		_spec.ClearField(psdgroup.FieldDesc, field.TypeString)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psdgroup.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// PsdGroupUpdateOne is the builder for updating a single PsdGroup entity.
type PsdGroupUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *PsdGroupMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *PsdGroupUpdateOne) SetUpdatedTime(v time.Time) *PsdGroupUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetName sets the "name" field.
func (_u *PsdGroupUpdateOne) SetName(v string) *PsdGroupUpdateOne {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *PsdGroupUpdateOne) SetNillableName(v *string) *PsdGroupUpdateOne {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetDesc sets the "desc" field.
func (_u *PsdGroupUpdateOne) SetDesc(v string) *PsdGroupUpdateOne {
	_u.mutation.SetDesc(v)
	return _u
}

// SetNillableDesc sets the "desc" field if the given value is not nil.
func (_u *PsdGroupUpdateOne) SetNillableDesc(v *string) *PsdGroupUpdateOne {
	if v != nil {
		_u.SetDesc(*v)
	}
	return _u
}

// ClearDesc clears the value of the "desc" field.
func (_u *PsdGroupUpdateOne) ClearDesc() *PsdGroupUpdateOne {
	_u.mutation.ClearDesc()
	return _u
}

// Mutation returns the PsdGroupMutation object of the builder.
func (_u *PsdGroupUpdateOne) Mutation() *PsdGroupMutation {
	return _u.mutation
}

// Where appends a list predicates to the PsdGroupUpdate builder.
func (_u *PsdGroupUpdateOne) Where(ps ...predicate.PsdGroup) *PsdGroupUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *PsdGroupUpdateOne) Select(field string, fields ...string) *PsdGroupUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated PsdGroup entity.
func (_u *PsdGroupUpdateOne) Save(ctx context.Context) (*PsdGroup, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *PsdGroupUpdateOne) SaveX(ctx context.Context) *PsdGroup {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *PsdGroupUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *PsdGroupUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *PsdGroupUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := psdgroup.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *PsdGroupUpdateOne) check() error {
	if v, ok := _u.mutation.Name(); ok {
		if err := psdgroup.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`db1: validator failed for field "PsdGroup.name": %w`, err)}
		}
	}
	return nil
}

func (_u *PsdGroupUpdateOne) sqlSave(ctx context.Context) (_node *PsdGroup, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(psdgroup.Table, psdgroup.Columns, sqlgraph.NewFieldSpec(psdgroup.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "PsdGroup.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, psdgroup.FieldID)
		for _, f := range fields {
			if !psdgroup.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != psdgroup.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(psdgroup.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(psdgroup.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Desc(); ok {
		_spec.SetField(psdgroup.FieldDesc, field.TypeString, value)
	}
	if _u.mutation.DescCleared() {
		_spec.ClearField(psdgroup.FieldDesc, field.TypeString)
	}
	_node = &PsdGroup{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{psdgroup.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the PsdGroupUpdate builder.
func (_u *PsdGroupUpdate) WhereCreatedTime(v time.Time) *PsdGroupUpdate {
	_u.Where(psdgroup.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the PsdGroupUpdate builder.
func (_u *PsdGroupUpdate) WhereUpdatedTime(v time.Time) *PsdGroupUpdate {
	_u.Where(psdgroup.UpdatedTime(v))
	return _u
}

// WhereName applies equality check predicate to the PsdGroupUpdate builder.
func (_u *PsdGroupUpdate) WhereName(v string) *PsdGroupUpdate {
	_u.Where(psdgroup.Name(v))
	return _u
}

// WhereDesc applies equality check predicate to the PsdGroupUpdate builder.
func (_u *PsdGroupUpdate) WhereDesc(v string) *PsdGroupUpdate {
	_u.Where(psdgroup.Desc(v))
	return _u
}
