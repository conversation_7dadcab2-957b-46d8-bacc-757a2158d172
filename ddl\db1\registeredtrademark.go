// Code generated by ent, DO NOT EDIT.

package db1

import (
	"encoding/json"
	"fmt"
	"omnix/ddl/db1/registeredtrademark"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// RegisteredTrademark is the model entity for the RegisteredTrademark schema.
type RegisteredTrademark struct {
	config `json:"-"`
	// ID of the ent.
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedTime time.Time `json:"created_time,omitempty"`
	// 更新时间
	UpdatedTime time.Time `json:"updated_time,omitempty"`
	// 商标ID
	Tid int32 `json:"tid,omitempty"`
	// 商标数据
	Data         json.RawMessage `json:"data,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*RegisteredTrademark) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case registeredtrademark.FieldData:
			values[i] = new([]byte)
		case registeredtrademark.FieldID, registeredtrademark.FieldTid:
			values[i] = new(sql.NullInt64)
		case registeredtrademark.FieldCreatedTime, registeredtrademark.FieldUpdatedTime:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the RegisteredTrademark fields.
func (_m *RegisteredTrademark) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case registeredtrademark.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			_m.ID = int64(value.Int64)
		case registeredtrademark.FieldCreatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_time", values[i])
			} else if value.Valid {
				_m.CreatedTime = value.Time
			}
		case registeredtrademark.FieldUpdatedTime:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_time", values[i])
			} else if value.Valid {
				_m.UpdatedTime = value.Time
			}
		case registeredtrademark.FieldTid:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tid", values[i])
			} else if value.Valid {
				_m.Tid = int32(value.Int64)
			}
		case registeredtrademark.FieldData:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field data", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &_m.Data); err != nil {
					return fmt.Errorf("unmarshal field data: %w", err)
				}
			}
		default:
			_m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the RegisteredTrademark.
// This includes values selected through modifiers, order, etc.
func (_m *RegisteredTrademark) Value(name string) (ent.Value, error) {
	return _m.selectValues.Get(name)
}

// Update returns a builder for updating this RegisteredTrademark.
// Note that you need to call RegisteredTrademark.Unwrap() before calling this method if this RegisteredTrademark
// was returned from a transaction, and the transaction was committed or rolled back.
func (_m *RegisteredTrademark) Update() *RegisteredTrademarkUpdateOne {
	return NewRegisteredTrademarkClient(_m.config).UpdateOne(_m)
}

// Unwrap unwraps the RegisteredTrademark entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (_m *RegisteredTrademark) Unwrap() *RegisteredTrademark {
	_tx, ok := _m.config.driver.(*txDriver)
	if !ok {
		panic("db1: RegisteredTrademark is not a transactional entity")
	}
	_m.config.driver = _tx.drv
	return _m
}

// String implements the fmt.Stringer.
func (_m *RegisteredTrademark) String() string {
	var builder strings.Builder
	builder.WriteString("RegisteredTrademark(")
	builder.WriteString(fmt.Sprintf("id=%v, ", _m.ID))
	builder.WriteString("created_time=")
	builder.WriteString(_m.CreatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_time=")
	builder.WriteString(_m.UpdatedTime.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("tid=")
	builder.WriteString(fmt.Sprintf("%v", _m.Tid))
	builder.WriteString(", ")
	builder.WriteString("data=")
	builder.WriteString(fmt.Sprintf("%v", _m.Data))
	builder.WriteByte(')')
	return builder.String()
}

// RegisteredTrademarks is a parsable slice of RegisteredTrademark.
type RegisteredTrademarks []*RegisteredTrademark
