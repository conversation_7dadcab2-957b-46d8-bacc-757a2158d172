// Code generated by ent, DO NOT EDIT.

package registeredtrademark

import (
	"time"

	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the registeredtrademark type in the database.
	Label = "registered_trademark"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedTime holds the string denoting the created_time field in the database.
	FieldCreatedTime = "created_time"
	// FieldUpdatedTime holds the string denoting the updated_time field in the database.
	FieldUpdatedTime = "updated_time"
	// FieldTid holds the string denoting the tid field in the database.
	FieldTid = "tid"
	// FieldData holds the string denoting the data field in the database.
	FieldData = "data"
	// Table holds the table name of the registeredtrademark in the database.
	Table = "registered_trademark"
)

// Columns holds all SQL columns for registeredtrademark fields.
var Columns = []string{
	FieldID,
	FieldCreatedTime,
	FieldUpdatedTime,
	FieldTid,
	FieldData,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedTime holds the default value on creation for the "created_time" field.
	DefaultCreatedTime func() time.Time
	// DefaultUpdatedTime holds the default value on creation for the "updated_time" field.
	DefaultUpdatedTime func() time.Time
	// UpdateDefaultUpdatedTime holds the default value on update for the "updated_time" field.
	UpdateDefaultUpdatedTime func() time.Time
)

// OrderOption defines the ordering options for the RegisteredTrademark queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedTime orders the results by the created_time field.
func ByCreatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedTime, opts...).ToFunc()
}

// ByUpdatedTime orders the results by the updated_time field.
func ByUpdatedTime(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedTime, opts...).ToFunc()
}

// ByTid orders the results by the tid field.
func ByTid(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTid, opts...).ToFunc()
}
