// Code generated by ent, DO NOT EDIT.

package registeredtrademark

import (
	"omnix/ddl/db1/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLTE(FieldID, id))
}

// CreatedTime applies equality check predicate on the "created_time" field. It's identical to CreatedTimeEQ.
func CreatedTime(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldCreatedTime, v))
}

// UpdatedTime applies equality check predicate on the "updated_time" field. It's identical to UpdatedTimeEQ.
func UpdatedTime(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldUpdatedTime, v))
}

// Tid applies equality check predicate on the "tid" field. It's identical to TidEQ.
func Tid(v int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldTid, v))
}

// CreatedTimeEQ applies the EQ predicate on the "created_time" field.
func CreatedTimeEQ(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldCreatedTime, v))
}

// CreatedTimeNEQ applies the NEQ predicate on the "created_time" field.
func CreatedTimeNEQ(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNEQ(FieldCreatedTime, v))
}

// CreatedTimeIn applies the In predicate on the "created_time" field.
func CreatedTimeIn(vs ...time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldIn(FieldCreatedTime, vs...))
}

// CreatedTimeNotIn applies the NotIn predicate on the "created_time" field.
func CreatedTimeNotIn(vs ...time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNotIn(FieldCreatedTime, vs...))
}

// CreatedTimeGT applies the GT predicate on the "created_time" field.
func CreatedTimeGT(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGT(FieldCreatedTime, v))
}

// CreatedTimeGTE applies the GTE predicate on the "created_time" field.
func CreatedTimeGTE(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGTE(FieldCreatedTime, v))
}

// CreatedTimeLT applies the LT predicate on the "created_time" field.
func CreatedTimeLT(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLT(FieldCreatedTime, v))
}

// CreatedTimeLTE applies the LTE predicate on the "created_time" field.
func CreatedTimeLTE(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLTE(FieldCreatedTime, v))
}

// UpdatedTimeEQ applies the EQ predicate on the "updated_time" field.
func UpdatedTimeEQ(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldUpdatedTime, v))
}

// UpdatedTimeNEQ applies the NEQ predicate on the "updated_time" field.
func UpdatedTimeNEQ(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNEQ(FieldUpdatedTime, v))
}

// UpdatedTimeIn applies the In predicate on the "updated_time" field.
func UpdatedTimeIn(vs ...time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeNotIn applies the NotIn predicate on the "updated_time" field.
func UpdatedTimeNotIn(vs ...time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNotIn(FieldUpdatedTime, vs...))
}

// UpdatedTimeGT applies the GT predicate on the "updated_time" field.
func UpdatedTimeGT(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGT(FieldUpdatedTime, v))
}

// UpdatedTimeGTE applies the GTE predicate on the "updated_time" field.
func UpdatedTimeGTE(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGTE(FieldUpdatedTime, v))
}

// UpdatedTimeLT applies the LT predicate on the "updated_time" field.
func UpdatedTimeLT(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLT(FieldUpdatedTime, v))
}

// UpdatedTimeLTE applies the LTE predicate on the "updated_time" field.
func UpdatedTimeLTE(v time.Time) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLTE(FieldUpdatedTime, v))
}

// TidEQ applies the EQ predicate on the "tid" field.
func TidEQ(v int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldEQ(FieldTid, v))
}

// TidNEQ applies the NEQ predicate on the "tid" field.
func TidNEQ(v int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNEQ(FieldTid, v))
}

// TidIn applies the In predicate on the "tid" field.
func TidIn(vs ...int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldIn(FieldTid, vs...))
}

// TidNotIn applies the NotIn predicate on the "tid" field.
func TidNotIn(vs ...int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldNotIn(FieldTid, vs...))
}

// TidGT applies the GT predicate on the "tid" field.
func TidGT(v int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGT(FieldTid, v))
}

// TidGTE applies the GTE predicate on the "tid" field.
func TidGTE(v int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldGTE(FieldTid, v))
}

// TidLT applies the LT predicate on the "tid" field.
func TidLT(v int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLT(FieldTid, v))
}

// TidLTE applies the LTE predicate on the "tid" field.
func TidLTE(v int32) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.FieldLTE(FieldTid, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.RegisteredTrademark) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.RegisteredTrademark) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.RegisteredTrademark) predicate.RegisteredTrademark {
	return predicate.RegisteredTrademark(sql.NotPredicates(p))
}
