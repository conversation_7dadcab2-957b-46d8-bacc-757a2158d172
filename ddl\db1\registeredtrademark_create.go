// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"omnix/ddl/db1/registeredtrademark"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// RegisteredTrademarkCreate is the builder for creating a RegisteredTrademark entity.
type RegisteredTrademarkCreate struct {
	config
	mutation *RegisteredTrademarkMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedTime sets the "created_time" field.
func (_c *RegisteredTrademarkCreate) SetCreatedTime(v time.Time) *RegisteredTrademarkCreate {
	_c.mutation.SetCreatedTime(v)
	return _c
}

// SetNillableCreatedTime sets the "created_time" field if the given value is not nil.
func (_c *RegisteredTrademarkCreate) SetNillableCreatedTime(v *time.Time) *RegisteredTrademarkCreate {
	if v != nil {
		_c.SetCreatedTime(*v)
	}
	return _c
}

// SetUpdatedTime sets the "updated_time" field.
func (_c *RegisteredTrademarkCreate) SetUpdatedTime(v time.Time) *RegisteredTrademarkCreate {
	_c.mutation.SetUpdatedTime(v)
	return _c
}

// SetNillableUpdatedTime sets the "updated_time" field if the given value is not nil.
func (_c *RegisteredTrademarkCreate) SetNillableUpdatedTime(v *time.Time) *RegisteredTrademarkCreate {
	if v != nil {
		_c.SetUpdatedTime(*v)
	}
	return _c
}

// SetTid sets the "tid" field.
func (_c *RegisteredTrademarkCreate) SetTid(v int32) *RegisteredTrademarkCreate {
	_c.mutation.SetTid(v)
	return _c
}

// SetData sets the "data" field.
func (_c *RegisteredTrademarkCreate) SetData(v json.RawMessage) *RegisteredTrademarkCreate {
	_c.mutation.SetData(v)
	return _c
}

// SetID sets the "id" field.
func (_c *RegisteredTrademarkCreate) SetID(v int64) *RegisteredTrademarkCreate {
	_c.mutation.SetID(v)
	return _c
}

// Mutation returns the RegisteredTrademarkMutation object of the builder.
func (_c *RegisteredTrademarkCreate) Mutation() *RegisteredTrademarkMutation {
	return _c.mutation
}

// Save creates the RegisteredTrademark in the database.
func (_c *RegisteredTrademarkCreate) Save(ctx context.Context) (*RegisteredTrademark, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *RegisteredTrademarkCreate) SaveX(ctx context.Context) *RegisteredTrademark {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *RegisteredTrademarkCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *RegisteredTrademarkCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *RegisteredTrademarkCreate) defaults() {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		v := registeredtrademark.DefaultCreatedTime()
		_c.mutation.SetCreatedTime(v)
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		v := registeredtrademark.DefaultUpdatedTime()
		_c.mutation.SetUpdatedTime(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *RegisteredTrademarkCreate) check() error {
	if _, ok := _c.mutation.CreatedTime(); !ok {
		return &ValidationError{Name: "created_time", err: errors.New(`db1: missing required field "RegisteredTrademark.created_time"`)}
	}
	if _, ok := _c.mutation.UpdatedTime(); !ok {
		return &ValidationError{Name: "updated_time", err: errors.New(`db1: missing required field "RegisteredTrademark.updated_time"`)}
	}
	if _, ok := _c.mutation.Tid(); !ok {
		return &ValidationError{Name: "tid", err: errors.New(`db1: missing required field "RegisteredTrademark.tid"`)}
	}
	if _, ok := _c.mutation.Data(); !ok {
		return &ValidationError{Name: "data", err: errors.New(`db1: missing required field "RegisteredTrademark.data"`)}
	}
	return nil
}

func (_c *RegisteredTrademarkCreate) sqlSave(ctx context.Context) (*RegisteredTrademark, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *RegisteredTrademarkCreate) createSpec() (*RegisteredTrademark, *sqlgraph.CreateSpec) {
	var (
		_node = &RegisteredTrademark{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(registeredtrademark.Table, sqlgraph.NewFieldSpec(registeredtrademark.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = _c.conflict
	if id, ok := _c.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := _c.mutation.CreatedTime(); ok {
		_spec.SetField(registeredtrademark.FieldCreatedTime, field.TypeTime, value)
		_node.CreatedTime = value
	}
	if value, ok := _c.mutation.UpdatedTime(); ok {
		_spec.SetField(registeredtrademark.FieldUpdatedTime, field.TypeTime, value)
		_node.UpdatedTime = value
	}
	if value, ok := _c.mutation.Tid(); ok {
		_spec.SetField(registeredtrademark.FieldTid, field.TypeInt32, value)
		_node.Tid = value
	}
	if value, ok := _c.mutation.Data(); ok {
		_spec.SetField(registeredtrademark.FieldData, field.TypeJSON, value)
		_node.Data = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.RegisteredTrademark.Create().
//		SetCreatedTime(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RegisteredTrademarkUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *RegisteredTrademarkCreate) OnConflict(opts ...sql.ConflictOption) *RegisteredTrademarkUpsertOne {
	_c.conflict = opts
	return &RegisteredTrademarkUpsertOne{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.RegisteredTrademark.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *RegisteredTrademarkCreate) OnConflictColumns(columns ...string) *RegisteredTrademarkUpsertOne {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &RegisteredTrademarkUpsertOne{
		create: _c,
	}
}

type (
	// RegisteredTrademarkUpsertOne is the builder for "upsert"-ing
	//  one RegisteredTrademark node.
	RegisteredTrademarkUpsertOne struct {
		create *RegisteredTrademarkCreate
	}

	// RegisteredTrademarkUpsert is the "OnConflict" setter.
	RegisteredTrademarkUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedTime sets the "updated_time" field.
func (u *RegisteredTrademarkUpsert) SetUpdatedTime(v time.Time) *RegisteredTrademarkUpsert {
	u.Set(registeredtrademark.FieldUpdatedTime, v)
	return u
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsert) UpdateUpdatedTime() *RegisteredTrademarkUpsert {
	u.SetExcluded(registeredtrademark.FieldUpdatedTime)
	return u
}

// SetTid sets the "tid" field.
func (u *RegisteredTrademarkUpsert) SetTid(v int32) *RegisteredTrademarkUpsert {
	u.Set(registeredtrademark.FieldTid, v)
	return u
}

// UpdateTid sets the "tid" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsert) UpdateTid() *RegisteredTrademarkUpsert {
	u.SetExcluded(registeredtrademark.FieldTid)
	return u
}

// AddTid adds v to the "tid" field.
func (u *RegisteredTrademarkUpsert) AddTid(v int32) *RegisteredTrademarkUpsert {
	u.Add(registeredtrademark.FieldTid, v)
	return u
}

// SetData sets the "data" field.
func (u *RegisteredTrademarkUpsert) SetData(v json.RawMessage) *RegisteredTrademarkUpsert {
	u.Set(registeredtrademark.FieldData, v)
	return u
}

// UpdateData sets the "data" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsert) UpdateData() *RegisteredTrademarkUpsert {
	u.SetExcluded(registeredtrademark.FieldData)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.RegisteredTrademark.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(registeredtrademark.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RegisteredTrademarkUpsertOne) UpdateNewValues() *RegisteredTrademarkUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(registeredtrademark.FieldID)
		}
		if _, exists := u.create.mutation.CreatedTime(); exists {
			s.SetIgnore(registeredtrademark.FieldCreatedTime)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.RegisteredTrademark.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *RegisteredTrademarkUpsertOne) Ignore() *RegisteredTrademarkUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RegisteredTrademarkUpsertOne) DoNothing() *RegisteredTrademarkUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RegisteredTrademarkCreate.OnConflict
// documentation for more info.
func (u *RegisteredTrademarkUpsertOne) Update(set func(*RegisteredTrademarkUpsert)) *RegisteredTrademarkUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RegisteredTrademarkUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *RegisteredTrademarkUpsertOne) SetUpdatedTime(v time.Time) *RegisteredTrademarkUpsertOne {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsertOne) UpdateUpdatedTime() *RegisteredTrademarkUpsertOne {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetTid sets the "tid" field.
func (u *RegisteredTrademarkUpsertOne) SetTid(v int32) *RegisteredTrademarkUpsertOne {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.SetTid(v)
	})
}

// AddTid adds v to the "tid" field.
func (u *RegisteredTrademarkUpsertOne) AddTid(v int32) *RegisteredTrademarkUpsertOne {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.AddTid(v)
	})
}

// UpdateTid sets the "tid" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsertOne) UpdateTid() *RegisteredTrademarkUpsertOne {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.UpdateTid()
	})
}

// SetData sets the "data" field.
func (u *RegisteredTrademarkUpsertOne) SetData(v json.RawMessage) *RegisteredTrademarkUpsertOne {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.SetData(v)
	})
}

// UpdateData sets the "data" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsertOne) UpdateData() *RegisteredTrademarkUpsertOne {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.UpdateData()
	})
}

// Exec executes the query.
func (u *RegisteredTrademarkUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for RegisteredTrademarkCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RegisteredTrademarkUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *RegisteredTrademarkUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *RegisteredTrademarkUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// RegisteredTrademarkCreateBulk is the builder for creating many RegisteredTrademark entities in bulk.
type RegisteredTrademarkCreateBulk struct {
	config
	err      error
	builders []*RegisteredTrademarkCreate
	conflict []sql.ConflictOption
}

// Save creates the RegisteredTrademark entities in the database.
func (_c *RegisteredTrademarkCreateBulk) Save(ctx context.Context) ([]*RegisteredTrademark, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*RegisteredTrademark, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*RegisteredTrademarkMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = _c.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *RegisteredTrademarkCreateBulk) SaveX(ctx context.Context) []*RegisteredTrademark {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *RegisteredTrademarkCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *RegisteredTrademarkCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.RegisteredTrademark.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RegisteredTrademarkUpsert) {
//			SetCreatedTime(v+v).
//		}).
//		Exec(ctx)
func (_c *RegisteredTrademarkCreateBulk) OnConflict(opts ...sql.ConflictOption) *RegisteredTrademarkUpsertBulk {
	_c.conflict = opts
	return &RegisteredTrademarkUpsertBulk{
		create: _c,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.RegisteredTrademark.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (_c *RegisteredTrademarkCreateBulk) OnConflictColumns(columns ...string) *RegisteredTrademarkUpsertBulk {
	_c.conflict = append(_c.conflict, sql.ConflictColumns(columns...))
	return &RegisteredTrademarkUpsertBulk{
		create: _c,
	}
}

// RegisteredTrademarkUpsertBulk is the builder for "upsert"-ing
// a bulk of RegisteredTrademark nodes.
type RegisteredTrademarkUpsertBulk struct {
	create *RegisteredTrademarkCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.RegisteredTrademark.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(registeredtrademark.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RegisteredTrademarkUpsertBulk) UpdateNewValues() *RegisteredTrademarkUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(registeredtrademark.FieldID)
			}
			if _, exists := b.mutation.CreatedTime(); exists {
				s.SetIgnore(registeredtrademark.FieldCreatedTime)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.RegisteredTrademark.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *RegisteredTrademarkUpsertBulk) Ignore() *RegisteredTrademarkUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RegisteredTrademarkUpsertBulk) DoNothing() *RegisteredTrademarkUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RegisteredTrademarkCreateBulk.OnConflict
// documentation for more info.
func (u *RegisteredTrademarkUpsertBulk) Update(set func(*RegisteredTrademarkUpsert)) *RegisteredTrademarkUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RegisteredTrademarkUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedTime sets the "updated_time" field.
func (u *RegisteredTrademarkUpsertBulk) SetUpdatedTime(v time.Time) *RegisteredTrademarkUpsertBulk {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.SetUpdatedTime(v)
	})
}

// UpdateUpdatedTime sets the "updated_time" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsertBulk) UpdateUpdatedTime() *RegisteredTrademarkUpsertBulk {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.UpdateUpdatedTime()
	})
}

// SetTid sets the "tid" field.
func (u *RegisteredTrademarkUpsertBulk) SetTid(v int32) *RegisteredTrademarkUpsertBulk {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.SetTid(v)
	})
}

// AddTid adds v to the "tid" field.
func (u *RegisteredTrademarkUpsertBulk) AddTid(v int32) *RegisteredTrademarkUpsertBulk {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.AddTid(v)
	})
}

// UpdateTid sets the "tid" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsertBulk) UpdateTid() *RegisteredTrademarkUpsertBulk {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.UpdateTid()
	})
}

// SetData sets the "data" field.
func (u *RegisteredTrademarkUpsertBulk) SetData(v json.RawMessage) *RegisteredTrademarkUpsertBulk {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.SetData(v)
	})
}

// UpdateData sets the "data" field to the value that was provided on create.
func (u *RegisteredTrademarkUpsertBulk) UpdateData() *RegisteredTrademarkUpsertBulk {
	return u.Update(func(s *RegisteredTrademarkUpsert) {
		s.UpdateData()
	})
}

// Exec executes the query.
func (u *RegisteredTrademarkUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("db1: OnConflict was set for builder %d. Set it on the RegisteredTrademarkCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("db1: missing options for RegisteredTrademarkCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RegisteredTrademarkUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
