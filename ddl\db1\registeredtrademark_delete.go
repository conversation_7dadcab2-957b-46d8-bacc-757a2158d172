// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/registeredtrademark"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// RegisteredTrademarkDelete is the builder for deleting a RegisteredTrademark entity.
type RegisteredTrademarkDelete struct {
	config
	hooks    []Hook
	mutation *RegisteredTrademarkMutation
}

// Where appends a list predicates to the RegisteredTrademarkDelete builder.
func (_d *RegisteredTrademarkDelete) Where(ps ...predicate.RegisteredTrademark) *RegisteredTrademarkDelete {
	_d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (_d *RegisteredTrademarkDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, _d.sqlExec, _d.mutation, _d.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *RegisteredTrademarkDelete) ExecX(ctx context.Context) int {
	n, err := _d.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (_d *RegisteredTrademarkDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(registeredtrademark.Table, sqlgraph.NewFieldSpec(registeredtrademark.FieldID, field.TypeInt64))
	if ps := _d.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, _d.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	_d.mutation.done = true
	return affected, err
}

// WhereCreatedTime applies equality check predicate to the RegisteredTrademarkDelete builder.
func (_d *RegisteredTrademarkDelete) WhereCreatedTime(v time.Time) *RegisteredTrademarkDelete {
	_d.Where(registeredtrademark.CreatedTime(v))
	return _d
}

// WhereUpdatedTime applies equality check predicate to the RegisteredTrademarkDelete builder.
func (_d *RegisteredTrademarkDelete) WhereUpdatedTime(v time.Time) *RegisteredTrademarkDelete {
	_d.Where(registeredtrademark.UpdatedTime(v))
	return _d
}

// WhereTid applies equality check predicate to the RegisteredTrademarkDelete builder.
func (_d *RegisteredTrademarkDelete) WhereTid(v int32) *RegisteredTrademarkDelete {
	_d.Where(registeredtrademark.Tid(v))
	return _d
}

// RegisteredTrademarkDeleteOne is the builder for deleting a single RegisteredTrademark entity.
type RegisteredTrademarkDeleteOne struct {
	_d *RegisteredTrademarkDelete
}

// Where appends a list predicates to the RegisteredTrademarkDelete builder.
func (_d *RegisteredTrademarkDeleteOne) Where(ps ...predicate.RegisteredTrademark) *RegisteredTrademarkDeleteOne {
	_d._d.mutation.Where(ps...)
	return _d
}

// Exec executes the deletion query.
func (_d *RegisteredTrademarkDeleteOne) Exec(ctx context.Context) error {
	n, err := _d._d.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{registeredtrademark.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (_d *RegisteredTrademarkDeleteOne) ExecX(ctx context.Context) {
	if err := _d.Exec(ctx); err != nil {
		panic(err)
	}
}
