// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"fmt"
	"math"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/registeredtrademark"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// RegisteredTrademarkQuery is the builder for querying RegisteredTrademark entities.
type RegisteredTrademarkQuery struct {
	config
	ctx        *QueryContext
	order      []registeredtrademark.OrderOption
	inters     []Interceptor
	predicates []predicate.RegisteredTrademark
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the RegisteredTrademarkQuery builder.
func (_q *RegisteredTrademarkQuery) Where(ps ...predicate.RegisteredTrademark) *RegisteredTrademarkQuery {
	_q.predicates = append(_q.predicates, ps...)
	return _q
}

// Limit the number of records to be returned by this query.
func (_q *RegisteredTrademarkQuery) Limit(limit int) *RegisteredTrademarkQuery {
	_q.ctx.Limit = &limit
	return _q
}

// Offset to start from.
func (_q *RegisteredTrademarkQuery) Offset(offset int) *RegisteredTrademarkQuery {
	_q.ctx.Offset = &offset
	return _q
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (_q *RegisteredTrademarkQuery) Unique(unique bool) *RegisteredTrademarkQuery {
	_q.ctx.Unique = &unique
	return _q
}

// Order specifies how the records should be ordered.
func (_q *RegisteredTrademarkQuery) Order(o ...registeredtrademark.OrderOption) *RegisteredTrademarkQuery {
	_q.order = append(_q.order, o...)
	return _q
}

// First returns the first RegisteredTrademark entity from the query.
// Returns a *NotFoundError when no RegisteredTrademark was found.
func (_q *RegisteredTrademarkQuery) First(ctx context.Context) (*RegisteredTrademark, error) {
	nodes, err := _q.Limit(1).All(setContextOp(ctx, _q.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{registeredtrademark.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) FirstX(ctx context.Context) *RegisteredTrademark {
	node, err := _q.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first RegisteredTrademark ID from the query.
// Returns a *NotFoundError when no RegisteredTrademark ID was found.
func (_q *RegisteredTrademarkQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(1).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{registeredtrademark.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) FirstIDX(ctx context.Context) int64 {
	id, err := _q.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single RegisteredTrademark entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one RegisteredTrademark entity is found.
// Returns a *NotFoundError when no RegisteredTrademark entities are found.
func (_q *RegisteredTrademarkQuery) Only(ctx context.Context) (*RegisteredTrademark, error) {
	nodes, err := _q.Limit(2).All(setContextOp(ctx, _q.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{registeredtrademark.Label}
	default:
		return nil, &NotSingularError{registeredtrademark.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) OnlyX(ctx context.Context) *RegisteredTrademark {
	node, err := _q.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only RegisteredTrademark ID in the query.
// Returns a *NotSingularError when more than one RegisteredTrademark ID is found.
// Returns a *NotFoundError when no entities are found.
func (_q *RegisteredTrademarkQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = _q.Limit(2).IDs(setContextOp(ctx, _q.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{registeredtrademark.Label}
	default:
		err = &NotSingularError{registeredtrademark.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := _q.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of RegisteredTrademarks.
func (_q *RegisteredTrademarkQuery) All(ctx context.Context) ([]*RegisteredTrademark, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryAll)
	if err := _q.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*RegisteredTrademark, *RegisteredTrademarkQuery]()
	return withInterceptors[[]*RegisteredTrademark](ctx, _q, qr, _q.inters)
}

// AllX is like All, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) AllX(ctx context.Context) []*RegisteredTrademark {
	nodes, err := _q.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of RegisteredTrademark IDs.
func (_q *RegisteredTrademarkQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if _q.ctx.Unique == nil && _q.path != nil {
		_q.Unique(true)
	}
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryIDs)
	if err = _q.Select(registeredtrademark.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) IDsX(ctx context.Context) []int64 {
	ids, err := _q.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (_q *RegisteredTrademarkQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryCount)
	if err := _q.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, _q, querierCount[*RegisteredTrademarkQuery](), _q.inters)
}

// CountX is like Count, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) CountX(ctx context.Context) int {
	count, err := _q.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (_q *RegisteredTrademarkQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, _q.ctx, ent.OpQueryExist)
	switch _, err := _q.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("db1: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (_q *RegisteredTrademarkQuery) ExistX(ctx context.Context) bool {
	exist, err := _q.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the RegisteredTrademarkQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (_q *RegisteredTrademarkQuery) Clone() *RegisteredTrademarkQuery {
	if _q == nil {
		return nil
	}
	return &RegisteredTrademarkQuery{
		config:     _q.config,
		ctx:        _q.ctx.Clone(),
		order:      append([]registeredtrademark.OrderOption{}, _q.order...),
		inters:     append([]Interceptor{}, _q.inters...),
		predicates: append([]predicate.RegisteredTrademark{}, _q.predicates...),
		// clone intermediate query.
		sql:  _q.sql.Clone(),
		path: _q.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.RegisteredTrademark.Query().
//		GroupBy(registeredtrademark.FieldCreatedTime).
//		Aggregate(db1.Count()).
//		Scan(ctx, &v)
func (_q *RegisteredTrademarkQuery) GroupBy(field string, fields ...string) *RegisteredTrademarkGroupBy {
	_q.ctx.Fields = append([]string{field}, fields...)
	grbuild := &RegisteredTrademarkGroupBy{build: _q}
	grbuild.flds = &_q.ctx.Fields
	grbuild.label = registeredtrademark.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedTime time.Time `json:"created_time,omitempty"`
//	}
//
//	client.RegisteredTrademark.Query().
//		Select(registeredtrademark.FieldCreatedTime).
//		Scan(ctx, &v)
func (_q *RegisteredTrademarkQuery) Select(fields ...string) *RegisteredTrademarkSelect {
	_q.ctx.Fields = append(_q.ctx.Fields, fields...)
	sbuild := &RegisteredTrademarkSelect{RegisteredTrademarkQuery: _q}
	sbuild.label = registeredtrademark.Label
	sbuild.flds, sbuild.scan = &_q.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a RegisteredTrademarkSelect configured with the given aggregations.
func (_q *RegisteredTrademarkQuery) Aggregate(fns ...AggregateFunc) *RegisteredTrademarkSelect {
	return _q.Select().Aggregate(fns...)
}

func (_q *RegisteredTrademarkQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range _q.inters {
		if inter == nil {
			return fmt.Errorf("db1: uninitialized interceptor (forgotten import db1/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, _q); err != nil {
				return err
			}
		}
	}
	for _, f := range _q.ctx.Fields {
		if !registeredtrademark.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
		}
	}
	if _q.path != nil {
		prev, err := _q.path(ctx)
		if err != nil {
			return err
		}
		_q.sql = prev
	}
	return nil
}

func (_q *RegisteredTrademarkQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*RegisteredTrademark, error) {
	var (
		nodes = []*RegisteredTrademark{}
		_spec = _q.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*RegisteredTrademark).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &RegisteredTrademark{config: _q.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, _q.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (_q *RegisteredTrademarkQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := _q.querySpec()
	_spec.Node.Columns = _q.ctx.Fields
	if len(_q.ctx.Fields) > 0 {
		_spec.Unique = _q.ctx.Unique != nil && *_q.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, _q.driver, _spec)
}

func (_q *RegisteredTrademarkQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(registeredtrademark.Table, registeredtrademark.Columns, sqlgraph.NewFieldSpec(registeredtrademark.FieldID, field.TypeInt64))
	_spec.From = _q.sql
	if unique := _q.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if _q.path != nil {
		_spec.Unique = true
	}
	if fields := _q.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, registeredtrademark.FieldID)
		for i := range fields {
			if fields[i] != registeredtrademark.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := _q.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := _q.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := _q.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := _q.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (_q *RegisteredTrademarkQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(_q.driver.Dialect())
	t1 := builder.Table(registeredtrademark.Table)
	columns := _q.ctx.Fields
	if len(columns) == 0 {
		columns = registeredtrademark.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if _q.sql != nil {
		selector = _q.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if _q.ctx.Unique != nil && *_q.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range _q.predicates {
		p(selector)
	}
	for _, p := range _q.order {
		p(selector)
	}
	if offset := _q.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := _q.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// WhereCreatedTime applies equality check predicate on the "created_time" field
func (_m *RegisteredTrademarkQuery) WhereCreatedTime(v time.Time) *RegisteredTrademarkQuery {
	_m.Where(registeredtrademark.CreatedTime(v))
	return _m
}

// WhereUpdatedTime applies equality check predicate on the "updated_time" field
func (_m *RegisteredTrademarkQuery) WhereUpdatedTime(v time.Time) *RegisteredTrademarkQuery {
	_m.Where(registeredtrademark.UpdatedTime(v))
	return _m
}

// WhereTid applies equality check predicate on the "tid" field
func (_m *RegisteredTrademarkQuery) WhereTid(v int32) *RegisteredTrademarkQuery {
	_m.Where(registeredtrademark.Tid(v))
	return _m
}

// RegisteredTrademarkGroupBy is the group-by builder for RegisteredTrademark entities.
type RegisteredTrademarkGroupBy struct {
	selector
	build *RegisteredTrademarkQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (_g *RegisteredTrademarkGroupBy) Aggregate(fns ...AggregateFunc) *RegisteredTrademarkGroupBy {
	_g.fns = append(_g.fns, fns...)
	return _g
}

// Scan applies the selector query and scans the result into the given value.
func (_g *RegisteredTrademarkGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _g.build.ctx, ent.OpQueryGroupBy)
	if err := _g.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*RegisteredTrademarkQuery, *RegisteredTrademarkGroupBy](ctx, _g.build, _g, _g.build.inters, v)
}

func (_g *RegisteredTrademarkGroupBy) sqlScan(ctx context.Context, root *RegisteredTrademarkQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(_g.fns))
	for _, fn := range _g.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*_g.flds)+len(_g.fns))
		for _, f := range *_g.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*_g.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _g.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// RegisteredTrademarkSelect is the builder for selecting fields of RegisteredTrademark entities.
type RegisteredTrademarkSelect struct {
	*RegisteredTrademarkQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (_s *RegisteredTrademarkSelect) Aggregate(fns ...AggregateFunc) *RegisteredTrademarkSelect {
	_s.fns = append(_s.fns, fns...)
	return _s
}

// Scan applies the selector query and scans the result into the given value.
func (_s *RegisteredTrademarkSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, _s.ctx, ent.OpQuerySelect)
	if err := _s.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*RegisteredTrademarkQuery, *RegisteredTrademarkSelect](ctx, _s.RegisteredTrademarkQuery, _s, _s.inters, v)
}

func (_s *RegisteredTrademarkSelect) sqlScan(ctx context.Context, root *RegisteredTrademarkQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(_s.fns))
	for _, fn := range _s.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*_s.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := _s.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
