// Code generated by ent, DO NOT EDIT.

package db1

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"omnix/ddl/db1/predicate"
	"omnix/ddl/db1/registeredtrademark"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// RegisteredTrademarkUpdate is the builder for updating RegisteredTrademark entities.
type RegisteredTrademarkUpdate struct {
	config
	hooks    []Hook
	mutation *RegisteredTrademarkMutation
}

// Where appends a list predicates to the RegisteredTrademarkUpdate builder.
func (_u *RegisteredTrademarkUpdate) Where(ps ...predicate.RegisteredTrademark) *RegisteredTrademarkUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *RegisteredTrademarkUpdate) SetUpdatedTime(v time.Time) *RegisteredTrademarkUpdate {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetTid sets the "tid" field.
func (_u *RegisteredTrademarkUpdate) SetTid(v int32) *RegisteredTrademarkUpdate {
	_u.mutation.ResetTid()
	_u.mutation.SetTid(v)
	return _u
}

// SetNillableTid sets the "tid" field if the given value is not nil.
func (_u *RegisteredTrademarkUpdate) SetNillableTid(v *int32) *RegisteredTrademarkUpdate {
	if v != nil {
		_u.SetTid(*v)
	}
	return _u
}

// AddTid adds value to the "tid" field.
func (_u *RegisteredTrademarkUpdate) AddTid(v int32) *RegisteredTrademarkUpdate {
	_u.mutation.AddTid(v)
	return _u
}

// SetData sets the "data" field.
func (_u *RegisteredTrademarkUpdate) SetData(v json.RawMessage) *RegisteredTrademarkUpdate {
	_u.mutation.SetData(v)
	return _u
}

// AppendData appends value to the "data" field.
func (_u *RegisteredTrademarkUpdate) AppendData(v json.RawMessage) *RegisteredTrademarkUpdate {
	_u.mutation.AppendData(v)
	return _u
}

// Mutation returns the RegisteredTrademarkMutation object of the builder.
func (_u *RegisteredTrademarkUpdate) Mutation() *RegisteredTrademarkMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *RegisteredTrademarkUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *RegisteredTrademarkUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *RegisteredTrademarkUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *RegisteredTrademarkUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *RegisteredTrademarkUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := registeredtrademark.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

func (_u *RegisteredTrademarkUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	_spec := sqlgraph.NewUpdateSpec(registeredtrademark.Table, registeredtrademark.Columns, sqlgraph.NewFieldSpec(registeredtrademark.FieldID, field.TypeInt64))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(registeredtrademark.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Tid(); ok {
		_spec.SetField(registeredtrademark.FieldTid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedTid(); ok {
		_spec.AddField(registeredtrademark.FieldTid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.Data(); ok {
		_spec.SetField(registeredtrademark.FieldData, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.AppendedData(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, registeredtrademark.FieldData, value)
		})
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{registeredtrademark.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// RegisteredTrademarkUpdateOne is the builder for updating a single RegisteredTrademark entity.
type RegisteredTrademarkUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *RegisteredTrademarkMutation
}

// SetUpdatedTime sets the "updated_time" field.
func (_u *RegisteredTrademarkUpdateOne) SetUpdatedTime(v time.Time) *RegisteredTrademarkUpdateOne {
	_u.mutation.SetUpdatedTime(v)
	return _u
}

// SetTid sets the "tid" field.
func (_u *RegisteredTrademarkUpdateOne) SetTid(v int32) *RegisteredTrademarkUpdateOne {
	_u.mutation.ResetTid()
	_u.mutation.SetTid(v)
	return _u
}

// SetNillableTid sets the "tid" field if the given value is not nil.
func (_u *RegisteredTrademarkUpdateOne) SetNillableTid(v *int32) *RegisteredTrademarkUpdateOne {
	if v != nil {
		_u.SetTid(*v)
	}
	return _u
}

// AddTid adds value to the "tid" field.
func (_u *RegisteredTrademarkUpdateOne) AddTid(v int32) *RegisteredTrademarkUpdateOne {
	_u.mutation.AddTid(v)
	return _u
}

// SetData sets the "data" field.
func (_u *RegisteredTrademarkUpdateOne) SetData(v json.RawMessage) *RegisteredTrademarkUpdateOne {
	_u.mutation.SetData(v)
	return _u
}

// AppendData appends value to the "data" field.
func (_u *RegisteredTrademarkUpdateOne) AppendData(v json.RawMessage) *RegisteredTrademarkUpdateOne {
	_u.mutation.AppendData(v)
	return _u
}

// Mutation returns the RegisteredTrademarkMutation object of the builder.
func (_u *RegisteredTrademarkUpdateOne) Mutation() *RegisteredTrademarkMutation {
	return _u.mutation
}

// Where appends a list predicates to the RegisteredTrademarkUpdate builder.
func (_u *RegisteredTrademarkUpdateOne) Where(ps ...predicate.RegisteredTrademark) *RegisteredTrademarkUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *RegisteredTrademarkUpdateOne) Select(field string, fields ...string) *RegisteredTrademarkUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated RegisteredTrademark entity.
func (_u *RegisteredTrademarkUpdateOne) Save(ctx context.Context) (*RegisteredTrademark, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *RegisteredTrademarkUpdateOne) SaveX(ctx context.Context) *RegisteredTrademark {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *RegisteredTrademarkUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *RegisteredTrademarkUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *RegisteredTrademarkUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedTime(); !ok {
		v := registeredtrademark.UpdateDefaultUpdatedTime()
		_u.mutation.SetUpdatedTime(v)
	}
}

func (_u *RegisteredTrademarkUpdateOne) sqlSave(ctx context.Context) (_node *RegisteredTrademark, err error) {
	_spec := sqlgraph.NewUpdateSpec(registeredtrademark.Table, registeredtrademark.Columns, sqlgraph.NewFieldSpec(registeredtrademark.FieldID, field.TypeInt64))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`db1: missing "RegisteredTrademark.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, registeredtrademark.FieldID)
		for _, f := range fields {
			if !registeredtrademark.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("db1: invalid field %q for query", f)}
			}
			if f != registeredtrademark.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.UpdatedTime(); ok {
		_spec.SetField(registeredtrademark.FieldUpdatedTime, field.TypeTime, value)
	}
	if value, ok := _u.mutation.Tid(); ok {
		_spec.SetField(registeredtrademark.FieldTid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.AddedTid(); ok {
		_spec.AddField(registeredtrademark.FieldTid, field.TypeInt32, value)
	}
	if value, ok := _u.mutation.Data(); ok {
		_spec.SetField(registeredtrademark.FieldData, field.TypeJSON, value)
	}
	if value, ok := _u.mutation.AppendedData(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, registeredtrademark.FieldData, value)
		})
	}
	_node = &RegisteredTrademark{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{registeredtrademark.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}

// WhereCreatedTime applies equality check predicate to the RegisteredTrademarkUpdate builder.
func (_u *RegisteredTrademarkUpdate) WhereCreatedTime(v time.Time) *RegisteredTrademarkUpdate {
	_u.Where(registeredtrademark.CreatedTime(v))
	return _u
}

// WhereUpdatedTime applies equality check predicate to the RegisteredTrademarkUpdate builder.
func (_u *RegisteredTrademarkUpdate) WhereUpdatedTime(v time.Time) *RegisteredTrademarkUpdate {
	_u.Where(registeredtrademark.UpdatedTime(v))
	return _u
}

// WhereTid applies equality check predicate to the RegisteredTrademarkUpdate builder.
func (_u *RegisteredTrademarkUpdate) WhereTid(v int32) *RegisteredTrademarkUpdate {
	_u.Where(registeredtrademark.Tid(v))
	return _u
}
