// Code generated by ent, DO NOT EDIT.

package db1

import (
	"omnix/ddl/db1/administrator"
	"omnix/ddl/db1/material"
	"omnix/ddl/db1/materialgroup"
	"omnix/ddl/db1/productitem"
	"omnix/ddl/db1/psd"
	"omnix/ddl/db1/psdcombineresult"
	"omnix/ddl/db1/psdcombinetask"
	"omnix/ddl/db1/psdgroup"
	"omnix/ddl/db1/registeredtrademark"
	"omnix/ddl/db1/schema"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	administratorMixin := schema.Administrator{}.Mixin()
	administratorMixinFields1 := administratorMixin[1].Fields()
	_ = administratorMixinFields1
	administratorFields := schema.Administrator{}.Fields()
	_ = administratorFields
	// administratorDescCreatedTime is the schema descriptor for created_time field.
	administratorDescCreatedTime := administratorMixinFields1[0].Descriptor()
	// administrator.DefaultCreatedTime holds the default value on creation for the created_time field.
	administrator.DefaultCreatedTime = administratorDescCreatedTime.Default.(func() time.Time)
	// administratorDescUpdatedTime is the schema descriptor for updated_time field.
	administratorDescUpdatedTime := administratorMixinFields1[1].Descriptor()
	// administrator.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	administrator.DefaultUpdatedTime = administratorDescUpdatedTime.Default.(func() time.Time)
	// administrator.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	administrator.UpdateDefaultUpdatedTime = administratorDescUpdatedTime.UpdateDefault.(func() time.Time)
	// administratorDescUsername is the schema descriptor for username field.
	administratorDescUsername := administratorFields[0].Descriptor()
	// administrator.UsernameValidator is a validator for the "username" field. It is called by the builders before save.
	administrator.UsernameValidator = administratorDescUsername.Validators[0].(func(string) error)
	// administratorDescPassword is the schema descriptor for password field.
	administratorDescPassword := administratorFields[1].Descriptor()
	// administrator.PasswordValidator is a validator for the "password" field. It is called by the builders before save.
	administrator.PasswordValidator = administratorDescPassword.Validators[0].(func(string) error)
	// administratorDescState is the schema descriptor for state field.
	administratorDescState := administratorFields[2].Descriptor()
	// administrator.StateValidator is a validator for the "state" field. It is called by the builders before save.
	administrator.StateValidator = administratorDescState.Validators[0].(func(string) error)
	materialMixin := schema.Material{}.Mixin()
	materialMixinFields0 := materialMixin[0].Fields()
	_ = materialMixinFields0
	materialFields := schema.Material{}.Fields()
	_ = materialFields
	// materialDescCreatedTime is the schema descriptor for created_time field.
	materialDescCreatedTime := materialMixinFields0[0].Descriptor()
	// material.DefaultCreatedTime holds the default value on creation for the created_time field.
	material.DefaultCreatedTime = materialDescCreatedTime.Default.(func() time.Time)
	// materialDescUpdatedTime is the schema descriptor for updated_time field.
	materialDescUpdatedTime := materialMixinFields0[1].Descriptor()
	// material.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	material.DefaultUpdatedTime = materialDescUpdatedTime.Default.(func() time.Time)
	// material.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	material.UpdateDefaultUpdatedTime = materialDescUpdatedTime.UpdateDefault.(func() time.Time)
	materialgroupMixin := schema.MaterialGroup{}.Mixin()
	materialgroupMixinFields1 := materialgroupMixin[1].Fields()
	_ = materialgroupMixinFields1
	materialgroupFields := schema.MaterialGroup{}.Fields()
	_ = materialgroupFields
	// materialgroupDescCreatedTime is the schema descriptor for created_time field.
	materialgroupDescCreatedTime := materialgroupMixinFields1[0].Descriptor()
	// materialgroup.DefaultCreatedTime holds the default value on creation for the created_time field.
	materialgroup.DefaultCreatedTime = materialgroupDescCreatedTime.Default.(func() time.Time)
	// materialgroupDescUpdatedTime is the schema descriptor for updated_time field.
	materialgroupDescUpdatedTime := materialgroupMixinFields1[1].Descriptor()
	// materialgroup.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	materialgroup.DefaultUpdatedTime = materialgroupDescUpdatedTime.Default.(func() time.Time)
	// materialgroup.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	materialgroup.UpdateDefaultUpdatedTime = materialgroupDescUpdatedTime.UpdateDefault.(func() time.Time)
	productitemMixin := schema.ProductItem{}.Mixin()
	productitemMixinFields1 := productitemMixin[1].Fields()
	_ = productitemMixinFields1
	productitemFields := schema.ProductItem{}.Fields()
	_ = productitemFields
	// productitemDescCreatedTime is the schema descriptor for created_time field.
	productitemDescCreatedTime := productitemMixinFields1[0].Descriptor()
	// productitem.DefaultCreatedTime holds the default value on creation for the created_time field.
	productitem.DefaultCreatedTime = productitemDescCreatedTime.Default.(func() time.Time)
	// productitemDescUpdatedTime is the schema descriptor for updated_time field.
	productitemDescUpdatedTime := productitemMixinFields1[1].Descriptor()
	// productitem.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	productitem.DefaultUpdatedTime = productitemDescUpdatedTime.Default.(func() time.Time)
	// productitem.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	productitem.UpdateDefaultUpdatedTime = productitemDescUpdatedTime.UpdateDefault.(func() time.Time)
	// productitemDescHash is the schema descriptor for hash field.
	productitemDescHash := productitemFields[0].Descriptor()
	// productitem.HashValidator is a validator for the "hash" field. It is called by the builders before save.
	productitem.HashValidator = func() func(string) error {
		validators := productitemDescHash.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(hash string) error {
			for _, fn := range fns {
				if err := fn(hash); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// productitemDescItemID is the schema descriptor for item_id field.
	productitemDescItemID := productitemFields[1].Descriptor()
	// productitem.ItemIDValidator is a validator for the "item_id" field. It is called by the builders before save.
	productitem.ItemIDValidator = func() func(string) error {
		validators := productitemDescItemID.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(item_id string) error {
			for _, fn := range fns {
				if err := fn(item_id); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// productitemDescPlatform is the schema descriptor for platform field.
	productitemDescPlatform := productitemFields[2].Descriptor()
	// productitem.PlatformValidator is a validator for the "platform" field. It is called by the builders before save.
	productitem.PlatformValidator = func() func(string) error {
		validators := productitemDescPlatform.Validators
		fns := [...]func(string) error{
			validators[0].(func(string) error),
			validators[1].(func(string) error),
		}
		return func(platform string) error {
			for _, fn := range fns {
				if err := fn(platform); err != nil {
					return err
				}
			}
			return nil
		}
	}()
	// productitemDescData is the schema descriptor for data field.
	productitemDescData := productitemFields[6].Descriptor()
	// productitem.DataValidator is a validator for the "data" field. It is called by the builders before save.
	productitem.DataValidator = productitemDescData.Validators[0].(func(string) error)
	psdMixin := schema.Psd{}.Mixin()
	psdMixinFields1 := psdMixin[1].Fields()
	_ = psdMixinFields1
	psdFields := schema.Psd{}.Fields()
	_ = psdFields
	// psdDescCreatedTime is the schema descriptor for created_time field.
	psdDescCreatedTime := psdMixinFields1[0].Descriptor()
	// psd.DefaultCreatedTime holds the default value on creation for the created_time field.
	psd.DefaultCreatedTime = psdDescCreatedTime.Default.(func() time.Time)
	// psdDescUpdatedTime is the schema descriptor for updated_time field.
	psdDescUpdatedTime := psdMixinFields1[1].Descriptor()
	// psd.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	psd.DefaultUpdatedTime = psdDescUpdatedTime.Default.(func() time.Time)
	// psd.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	psd.UpdateDefaultUpdatedTime = psdDescUpdatedTime.UpdateDefault.(func() time.Time)
	// psdDescFilePath is the schema descriptor for file_path field.
	psdDescFilePath := psdFields[2].Descriptor()
	// psd.FilePathValidator is a validator for the "file_path" field. It is called by the builders before save.
	psd.FilePathValidator = psdDescFilePath.Validators[0].(func(string) error)
	// psdDescFileSha1 is the schema descriptor for file_sha1 field.
	psdDescFileSha1 := psdFields[3].Descriptor()
	// psd.FileSha1Validator is a validator for the "file_sha1" field. It is called by the builders before save.
	psd.FileSha1Validator = psdDescFileSha1.Validators[0].(func(string) error)
	// psdDescFileValid is the schema descriptor for file_valid field.
	psdDescFileValid := psdFields[4].Descriptor()
	// psd.DefaultFileValid holds the default value on creation for the file_valid field.
	psd.DefaultFileValid = psdDescFileValid.Default.(int32)
	// psdDescWeight is the schema descriptor for weight field.
	psdDescWeight := psdFields[5].Descriptor()
	// psd.DefaultWeight holds the default value on creation for the weight field.
	psd.DefaultWeight = psdDescWeight.Default.(int32)
	// psdDescIsCover is the schema descriptor for is_cover field.
	psdDescIsCover := psdFields[6].Descriptor()
	// psd.DefaultIsCover holds the default value on creation for the is_cover field.
	psd.DefaultIsCover = psdDescIsCover.Default.(int32)
	psdcombineresultMixin := schema.PsdCombineResult{}.Mixin()
	psdcombineresultMixinFields1 := psdcombineresultMixin[1].Fields()
	_ = psdcombineresultMixinFields1
	psdcombineresultFields := schema.PsdCombineResult{}.Fields()
	_ = psdcombineresultFields
	// psdcombineresultDescCreatedTime is the schema descriptor for created_time field.
	psdcombineresultDescCreatedTime := psdcombineresultMixinFields1[0].Descriptor()
	// psdcombineresult.DefaultCreatedTime holds the default value on creation for the created_time field.
	psdcombineresult.DefaultCreatedTime = psdcombineresultDescCreatedTime.Default.(func() time.Time)
	// psdcombineresultDescUpdatedTime is the schema descriptor for updated_time field.
	psdcombineresultDescUpdatedTime := psdcombineresultMixinFields1[1].Descriptor()
	// psdcombineresult.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	psdcombineresult.DefaultUpdatedTime = psdcombineresultDescUpdatedTime.Default.(func() time.Time)
	// psdcombineresult.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	psdcombineresult.UpdateDefaultUpdatedTime = psdcombineresultDescUpdatedTime.UpdateDefault.(func() time.Time)
	// psdcombineresultDescResultURL is the schema descriptor for result_url field.
	psdcombineresultDescResultURL := psdcombineresultFields[1].Descriptor()
	// psdcombineresult.ResultURLValidator is a validator for the "result_url" field. It is called by the builders before save.
	psdcombineresult.ResultURLValidator = psdcombineresultDescResultURL.Validators[0].(func(string) error)
	// psdcombineresultDescWeight is the schema descriptor for weight field.
	psdcombineresultDescWeight := psdcombineresultFields[2].Descriptor()
	// psdcombineresult.DefaultWeight holds the default value on creation for the weight field.
	psdcombineresult.DefaultWeight = psdcombineresultDescWeight.Default.(int32)
	// psdcombineresultDescIsCover is the schema descriptor for is_cover field.
	psdcombineresultDescIsCover := psdcombineresultFields[3].Descriptor()
	// psdcombineresult.DefaultIsCover holds the default value on creation for the is_cover field.
	psdcombineresult.DefaultIsCover = psdcombineresultDescIsCover.Default.(int32)
	psdcombinetaskMixin := schema.PsdCombineTask{}.Mixin()
	psdcombinetaskMixinFields1 := psdcombinetaskMixin[1].Fields()
	_ = psdcombinetaskMixinFields1
	psdcombinetaskFields := schema.PsdCombineTask{}.Fields()
	_ = psdcombinetaskFields
	// psdcombinetaskDescCreatedTime is the schema descriptor for created_time field.
	psdcombinetaskDescCreatedTime := psdcombinetaskMixinFields1[0].Descriptor()
	// psdcombinetask.DefaultCreatedTime holds the default value on creation for the created_time field.
	psdcombinetask.DefaultCreatedTime = psdcombinetaskDescCreatedTime.Default.(func() time.Time)
	// psdcombinetaskDescUpdatedTime is the schema descriptor for updated_time field.
	psdcombinetaskDescUpdatedTime := psdcombinetaskMixinFields1[1].Descriptor()
	// psdcombinetask.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	psdcombinetask.DefaultUpdatedTime = psdcombinetaskDescUpdatedTime.Default.(func() time.Time)
	// psdcombinetask.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	psdcombinetask.UpdateDefaultUpdatedTime = psdcombinetaskDescUpdatedTime.UpdateDefault.(func() time.Time)
	// psdcombinetaskDescMaterialURL is the schema descriptor for material_url field.
	psdcombinetaskDescMaterialURL := psdcombinetaskFields[1].Descriptor()
	// psdcombinetask.MaterialURLValidator is a validator for the "material_url" field. It is called by the builders before save.
	psdcombinetask.MaterialURLValidator = psdcombinetaskDescMaterialURL.Validators[0].(func(string) error)
	// psdcombinetaskDescStatus is the schema descriptor for status field.
	psdcombinetaskDescStatus := psdcombinetaskFields[4].Descriptor()
	// psdcombinetask.DefaultStatus holds the default value on creation for the status field.
	psdcombinetask.DefaultStatus = psdcombinetaskDescStatus.Default.(string)
	psdgroupMixin := schema.PsdGroup{}.Mixin()
	psdgroupMixinFields1 := psdgroupMixin[1].Fields()
	_ = psdgroupMixinFields1
	psdgroupFields := schema.PsdGroup{}.Fields()
	_ = psdgroupFields
	// psdgroupDescCreatedTime is the schema descriptor for created_time field.
	psdgroupDescCreatedTime := psdgroupMixinFields1[0].Descriptor()
	// psdgroup.DefaultCreatedTime holds the default value on creation for the created_time field.
	psdgroup.DefaultCreatedTime = psdgroupDescCreatedTime.Default.(func() time.Time)
	// psdgroupDescUpdatedTime is the schema descriptor for updated_time field.
	psdgroupDescUpdatedTime := psdgroupMixinFields1[1].Descriptor()
	// psdgroup.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	psdgroup.DefaultUpdatedTime = psdgroupDescUpdatedTime.Default.(func() time.Time)
	// psdgroup.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	psdgroup.UpdateDefaultUpdatedTime = psdgroupDescUpdatedTime.UpdateDefault.(func() time.Time)
	// psdgroupDescName is the schema descriptor for name field.
	psdgroupDescName := psdgroupFields[0].Descriptor()
	// psdgroup.NameValidator is a validator for the "name" field. It is called by the builders before save.
	psdgroup.NameValidator = psdgroupDescName.Validators[0].(func(string) error)
	registeredtrademarkMixin := schema.RegisteredTrademark{}.Mixin()
	registeredtrademarkMixinFields1 := registeredtrademarkMixin[1].Fields()
	_ = registeredtrademarkMixinFields1
	registeredtrademarkFields := schema.RegisteredTrademark{}.Fields()
	_ = registeredtrademarkFields
	// registeredtrademarkDescCreatedTime is the schema descriptor for created_time field.
	registeredtrademarkDescCreatedTime := registeredtrademarkMixinFields1[0].Descriptor()
	// registeredtrademark.DefaultCreatedTime holds the default value on creation for the created_time field.
	registeredtrademark.DefaultCreatedTime = registeredtrademarkDescCreatedTime.Default.(func() time.Time)
	// registeredtrademarkDescUpdatedTime is the schema descriptor for updated_time field.
	registeredtrademarkDescUpdatedTime := registeredtrademarkMixinFields1[1].Descriptor()
	// registeredtrademark.DefaultUpdatedTime holds the default value on creation for the updated_time field.
	registeredtrademark.DefaultUpdatedTime = registeredtrademarkDescUpdatedTime.Default.(func() time.Time)
	// registeredtrademark.UpdateDefaultUpdatedTime holds the default value on update for the updated_time field.
	registeredtrademark.UpdateDefaultUpdatedTime = registeredtrademarkDescUpdatedTime.UpdateDefault.(func() time.Time)
}
