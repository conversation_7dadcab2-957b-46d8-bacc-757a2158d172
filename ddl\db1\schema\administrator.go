// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// Administrator holds the schema definition for the Administrator entity.
type Administrator struct {
	ent.Schema
}

// Fields of the Administrator.
func (Administrator) Fields() []ent.Field {
	return []ent.Field{
		field.String("username").Unique().NotEmpty().Comment("用户名"),
		field.String("password").NotEmpty().Comment("密码"),
		field.String("state").NotEmpty().Comment("状态"),
	}
}
func (Administrator) Mixin() []ent.Mixin {
	return []ent.Mixin{
		IdMixin{},
		TimeMixin{},
	}
}

// Edges of the Administrator.
func (Administrator) Edges() []ent.Edge {
	return nil
}
