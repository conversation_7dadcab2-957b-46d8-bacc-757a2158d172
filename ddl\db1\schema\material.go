// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

type Material struct {
	ent.Schema
}

func (Material) Fields() []ent.Field {
	return []ent.Field{

		field.String("title").Comment("素材标题"),
		field.String("source_url").Comment("原始素材地址"),
		field.Other("material_group", pq.Int64Array{}).Optional().
			SchemaType(map[string]string{
				dialect.Postgres: "int8[]",
			}).Comment("素材组"),
		field.Other("flag", pq.StringArray{}).Optional().
			SchemaType(map[string]string{
				dialect.Postgres: "text[]",
			}).Comment("素材标记"),
		field.Int64("source_temu_id").Comment("关联Temu采集"),
		field.String("hash").Comment("文件校验码"),
		field.String("path").Comment("素材S3存储路径"),
	}
}
func (Material) Edges() []ent.Edge {
	return nil
}
func (Material) Mixin() []ent.Mixin {
	return []ent.Mixin{
		TimeMixin{},
		IdMixin{},
	}
}
func (Material) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "material"}}
}
