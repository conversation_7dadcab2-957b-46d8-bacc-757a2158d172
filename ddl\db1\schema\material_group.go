// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type MaterialGroup struct {
	ent.Schema
}

func (MaterialGroup) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Unique(),
	}
}
func (MaterialGroup) Edges() []ent.Edge {
	return nil
}
func (MaterialGroup) Mixin() []ent.Mixin {
	return []ent.Mixin{
        IdMixin{},
		TimeMixin{},
	}
}
func (MaterialGroup) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "material_group"}}
}
