// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
)

type TimeMixin struct {
	// We embed the `mixin.Schema` to avoid
	// implementing the rest of the methods.
	mixin.Schema
}

func (TimeMixin) Fields() []ent.Field {
	return []ent.Field{
		field.Time("created_time").Default(time.Now).
			SchemaType(map[string]string{
				dialect.Postgres: "timestamp without time zone",
			}).
			Immutable().Comment("创建时间"),
		field.Time("updated_time").Default(time.Now).UpdateDefault(time.Now).
			SchemaType(map[string]string{
				dialect.Postgres: "timestamp without time zone",
			}).Comment("更新时间"),
	}
}
