// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"omnix/genpb/msgpb"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
)

// ProductItem holds the schema definition for the ProductItem entity.
type ProductItem struct {
	ent.Schema
}

// Fields of the ProductItem.
func (ProductItem) Fields() []ent.Field {
	return []ent.Field{

		field.String("hash").NotEmpty().Unique().MaxLen(40).Comment("数据唯一"),
		field.String("item_id").NotEmpty().MaxLen(40).Comment("产品ID"),
		field.String("platform").NotEmpty().MaxLen(10).Comment("所属平台"),
		field.JSON("spec", &msgpb.ProductSpec{}).Comment("格式化的产品数据"),
		field.JSON("filter", &msgpb.ProductFilter{}).Comment("产品过滤属性"),
		field.Strings("mark").Optional().Comment("数据处理标记"),
		field.Text("data").NotEmpty().Comment("原始数据"),
	}
}
func (ProductItem) Mixin() []ent.Mixin {
	return []ent.Mixin{
		IdMixin{},
		TimeMixin{},
	}
}

// Edges of the ProductItem.
func (ProductItem) Edges() []ent.Edge {
	return nil
}
