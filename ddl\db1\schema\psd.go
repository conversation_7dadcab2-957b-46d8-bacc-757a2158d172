package schema

import (
    "entgo.io/ent"
    "entgo.io/ent/dialect"
    "entgo.io/ent/schema/field"
    "github.com/lib/pq"
)

// Psd holds the schema definition for the Psd entity.
type Psd struct {
    ent.Schema
}

// Fields of the Psd.
func (Psd) Fields() []ent.Field {
    return []ent.Field{
        field.Other("psd_group_id", pq.Int64Array{}).Optional().
            SchemaType(map[string]string{
                dialect.Postgres: "int8[]",
            }).Comment("所属模型分组id"),
        field.String("desc").Optional().Comment("模型描述"),
        field.String("file_path").NotEmpty().Comment("PSD文件路径"),
        field.String("file_sha1").Unique().NotEmpty().Comment("PSD文件SHA1值"),
        field.Int32("file_valid").Default(0).Comment("文件是否有效，0: 无效, 1: 有效"),
        field.Int32("weight").Default(0).Comment("模型权重，数值越大优先级越高"),
        field.Int32("is_cover").Default(0).Comment("是否为封面模型，0: 否, 1: 是"),
    }
}

// Mixin of the Psd.
func (Psd) Mixin() []ent.Mixin {
    return []ent.Mixin{
        IdMixin{},
        TimeMixin{},
    }
}

// Edges of the Psd.
func (Psd) Edges() []ent.Edge {
    return []ent.Edge{}
}
