package schema

import (
    "entgo.io/ent"
    "entgo.io/ent/schema/field"
)

// PsdCombineResult holds the schema definition for the PsdCombineResult entity.
type PsdCombineResult struct {
    ent.Schema
}

// Fields of the PsdCombineResult.
func (PsdCombineResult) Fields() []ent.Field {
    return []ent.Field{
        field.Int64("task_id").Comment("所属任务id"),
        field.String("result_url").NotEmpty().Comment("成品URL"),
        field.Int32("weight").Default(0).Comment("成品权重，数值越大优先级越高"),
        field.Int32("is_cover").Default(0).Comment("是否为封面成品，0: 否, 1: 是"),
    }
}

// Mixin of the PsdCombineResult.
func (PsdCombineResult) Mixin() []ent.Mixin {
    return []ent.Mixin{
        IdMixin{},
        TimeMixin{},
    }
}

// Edges of the PsdCombineResult.
func (PsdCombineResult) Edges() []ent.Edge {
    return []ent.Edge{}
}
