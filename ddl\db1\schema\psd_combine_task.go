package schema

import (
    "entgo.io/ent"
    "entgo.io/ent/schema/field"
)

// PsdCombineTask holds the schema definition for the PsdCombineTask entity.
type PsdCombineTask struct {
    ent.Schema
}

// Fields of the PsdCombineTask.
func (PsdCombineTask) Fields() []ent.Field {
    return []ent.Field{
        field.Int64("psd_group_id").Comment("所属模型分组id"),
        field.String("material_url").NotEmpty().Comment("素材URL"),
        field.Int64("reference_id").Optional().Comment("素材id"),
        field.Text("extra_params").Optional().Comment("额外参数，JSON格式"),
        field.String("status").Default("0").Comment("0: pending, 1: processing, 2: completed, 3: failed"),
        field.String("error_msg").Optional().Comment("错误信息"),
    }
}

// Mixin of the PsdCombineTask.
func (PsdCombineTask) Mixin() []ent.Mixin {
    return []ent.Mixin{
        IdMixin{},
        TimeMixin{},
    }
}

// Edges of the PsdCombineTask.
func (PsdCombineTask) Edges() []ent.Edge {
    return []ent.Edge{}
}
