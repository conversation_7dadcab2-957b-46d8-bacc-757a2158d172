package schema

import (
    "entgo.io/ent"
    "entgo.io/ent/schema/field"
)

// PsdGroup holds the schema definition for the PsdGroup entity.
type PsdGroup struct {
    ent.Schema
}

// Fields of the PsdGroup.
func (PsdGroup) Fields() []ent.Field {
    return []ent.Field{
        field.String("name").NotEmpty().Unique().Comment("分组名称"),
        field.String("desc").Optional().Comment("分组描述"),
    }
}

// Mixin of the PsdGroup.
func (PsdGroup) Mixin() []ent.Mixin {
    return []ent.Mixin{
        IdMixin{},
        TimeMixin{},
    }
}

// Edges of the PsdGroup.
func (PsdGroup) Edges() []ent.Edge {
    return []ent.Edge{}
}
