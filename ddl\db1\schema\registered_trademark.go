// Code generated by entimport, DO NOT EDIT.

package schema

import (
    "encoding/json"

    "entgo.io/ent"
    "entgo.io/ent/dialect/entsql"
    "entgo.io/ent/schema"
    "entgo.io/ent/schema/field"
)

// RegisteredTrademark holds the schema definition for the RegisteredTrademark entity.
type RegisteredTrademark struct {
	ent.Schema
}

// Fields of the RegisteredTrademark.
func (RegisteredTrademark) Fields() []ent.Field {
	return []ent.Field{

		field.Int32("tid").Unique().Comment("商标ID"),
        field.JSON("data",json.RawMessage{}).Comment("商标数据"),
	
	}
}
func (RegisteredTrademark) Mixin() []ent.Mixin {
	return []ent.Mixin{
		IdMixin{},
		TimeMixin{},
	}
}

// Edges of the RegisteredTrademark.
func (RegisteredTrademark) Edges() []ent.Edge {
	return nil
}
func (RegisteredTrademark) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "registered_trademark"}}
}
