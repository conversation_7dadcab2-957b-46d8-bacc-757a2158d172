-- +goose Up
-- Create "administrators" table
CREATE TABLE "administrators" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "created_time" timestamp NOT NULL,
    "updated_time" timestamp NOT NULL,
    "username" character varying NOT NULL,
    "password" character varying NOT NULL,
    "state" character varying NOT NULL,
    PRIMARY KEY ("id")
);
-- <PERSON><PERSON> index "administrators_username_key" to table: "administrators"
CREATE UNIQUE INDEX "administrators_username_key" ON "administrators" ("username");
-- Create "product_items" table
CREATE TABLE "product_items" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "created_time" timestamp NOT NULL,
    "updated_time" timestamp NOT NULL,
    "hash" character varying NOT NULL,
    "item_id" character varying NOT NULL,
    "platform" character varying NOT NULL,
    "spec" jsonb NOT NULL,
    "filter" jsonb NOT NULL,
    "mark" jsonb NULL,
    "data" text NOT NULL,
    PRIMARY KEY ("id")
);
-- <PERSON>reate index "product_items_hash_key" to table: "product_items"
CREATE UNIQUE INDEX "product_items_hash_key" ON "product_items" ("hash");
-- Create "goose_db_version" table
CREATE TABLE "goose_db_version" (
 "id" integer NOT NULL GENERATED BY DEFAULT AS IDENTITY,
 "version_id" bigint NOT NULL,
 "is_applied" boolean NOT NULL,
 "tstamp" timestamp NOT NULL DEFAULT now(),
 PRIMARY KEY ("id")
);