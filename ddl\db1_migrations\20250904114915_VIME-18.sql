-- +goose Up
-- Create "material_group" table
DROP TABLE IF EXISTS "material_group";
CREATE TABLE "material_group" (
  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "created_time" timestamp NOT NULL,
  "updated_time" timestamp NOT NULL,
  "name" character varying NOT NULL,
  PRIMARY KEY ("id")
);
-- <PERSON>reate index "material_group_name_key" to table: "material_group"
CREATE UNIQUE INDEX "material_group_name_key" ON "material_group" ("name");
