-- +goose Up
-- Create "registered_trademark" table
CREATE TABLE "registered_trademark" (
  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "created_time" timestamp NOT NULL,
  "updated_time" timestamp NOT NULL,
  "tid" integer NOT NULL,
  "data" jsonb NOT NULL,
  PRIMARY KEY ("id")
);
-- Create index "registered_trademark_tid_key" to table: "registered_trademark"
CREATE UNIQUE INDEX "registered_trademark_tid_key" ON "registered_trademark" ("tid");
