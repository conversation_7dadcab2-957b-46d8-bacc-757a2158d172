-- +goose Up
-- Create "psd_combine_results" table
CREATE TABLE "psd_combine_results" (
  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "created_time" timestamp NOT NULL,
  "updated_time" timestamp NOT NULL,
  "task_id" bigint NOT NULL,
  "result_url" character varying NOT NULL,
  "weight" integer NOT NULL DEFAULT 0,
  "is_cover" integer NOT NULL DEFAULT 0,
  PRIMARY KEY ("id")
);
-- Create "psd_combine_tasks" table
CREATE TABLE "psd_combine_tasks" (
  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "created_time" timestamp NOT NULL,
  "updated_time" timestamp NOT NULL,
  "psd_group_id" bigint NOT NULL,
  "material_url" character varying NOT NULL,
  "reference_id" bigint NULL,
  "extra_params" text NULL,
  "status" character varying NOT NULL DEFAULT '0',
  "error_msg" character varying NULL,
  PRIMARY KEY ("id")
);
-- Create "psd_groups" table
CREATE TABLE "psd_groups" (
  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "created_time" timestamp NOT NULL,
  "updated_time" timestamp NOT NULL,
  "name" character varying NOT NULL,
  "desc" character varying NULL,
  PRIMARY KEY ("id")
);
-- Create index "psd_groups_name_key" to table: "psd_groups"
CREATE UNIQUE INDEX "psd_groups_name_key" ON "psd_groups" ("name");
-- Create "psds" table
CREATE TABLE "psds" (
  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
  "created_time" timestamp NOT NULL,
  "updated_time" timestamp NOT NULL,
  "psd_group_id" bigint[] NULL,
  "desc" character varying NULL,
  "file_path" character varying NOT NULL,
  "file_sha1" character varying NOT NULL,
  "file_valid" integer NOT NULL DEFAULT 0,
  "weight" integer NOT NULL DEFAULT 0,
  "is_cover" integer NOT NULL DEFAULT 0,
  PRIMARY KEY ("id")
);
-- Create index "psds_file_sha1_key" to table: "psds"
CREATE UNIQUE INDEX "psds_file_sha1_key" ON "psds" ("file_sha1");
