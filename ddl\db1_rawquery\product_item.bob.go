// Code generated by BobGen psql v0.41.1. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package db1_rawquery

import (
	"context"
	_ "embed"
	"encoding/json"
	"io"
	"iter"
	"time"

	"github.com/aarondl/opt/null"
	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	"github.com/stephenafamo/bob/dialect/psql/dialect"
	"github.com/stephenafamo/bob/orm"
	"github.com/stephenafamo/bob/types"
	"github.com/stephenafamo/scan"
)

//go:embed product_item.bob.sql
var formattedQueries_product_item string

var getProductItemsForKouSQL = formattedQueries_product_item[159:712]

type GetProductItemsForKouQuery = orm.ModQuery[*dialect.SelectQuery, getProductItemsForKou, GetProductItemsForKouRow, []GetProductItemsForKouRow, getProductItemsForKouTransformer]

func GetProductItemsForKou(Arg1 int64) *GetProductItemsForKouQuery {
	var expressionTypArgs getProductItemsForKou

	expressionTypArgs.Arg1 = psql.Arg(Arg1)

	return &GetProductItemsForKouQuery{
		Query: orm.Query[getProductItemsForKou, GetProductItemsForKouRow, []GetProductItemsForKouRow, getProductItemsForKouTransformer]{
			ExecQuery: orm.ExecQuery[getProductItemsForKou]{
				BaseQuery: bob.BaseQuery[getProductItemsForKou]{
					Expression: expressionTypArgs,
					Dialect:    dialect.Dialect,
					QueryType:  bob.QueryTypeSelect,
				},
			},
			Scanner: func(context.Context, []string) (func(*scan.Row) (any, error), func(any) (GetProductItemsForKouRow, error)) {
				return func(row *scan.Row) (any, error) {
						var t GetProductItemsForKouRow
						row.ScheduleScanByIndex(0, &t.ID)
						row.ScheduleScanByIndex(1, &t.CreatedTime)
						row.ScheduleScanByIndex(2, &t.UpdatedTime)
						row.ScheduleScanByIndex(3, &t.Hash)
						row.ScheduleScanByIndex(4, &t.ItemID)
						row.ScheduleScanByIndex(5, &t.Platform)
						row.ScheduleScanByIndex(6, &t.Spec)
						row.ScheduleScanByIndex(7, &t.Filter)
						row.ScheduleScanByIndex(8, &t.Mark)
						row.ScheduleScanByIndex(9, &t.Data)
						return &t, nil
					}, func(v any) (GetProductItemsForKouRow, error) {
						return *(v.(*GetProductItemsForKouRow)), nil
					}
			},
		},
		Mod: bob.ModFunc[*dialect.SelectQuery](func(q *dialect.SelectQuery) {
			q.CombinedLimit.SetLimit(psql.Raw("$1"))
			q.AppendSelect(expressionTypArgs.subExpr(8, 392))
			q.SetTable(expressionTypArgs.subExpr(399, 412))
			q.AppendWhere(expressionTypArgs.subExpr(420, 544))
		}),
	}
}

type GetProductItemsForKouRow = struct {
	ID          int64                                 `db:"id"`
	CreatedTime time.Time                             `db:"created_time"`
	UpdatedTime time.Time                             `db:"updated_time"`
	Hash        string                                `db:"hash"`
	ItemID      string                                `db:"item_id"`
	Platform    string                                `db:"platform"`
	Spec        types.JSON[json.RawMessage]           `db:"spec"`
	Filter      types.JSON[json.RawMessage]           `db:"filter"`
	Mark        null.Val[types.JSON[json.RawMessage]] `db:"mark"`
	Data        string                                `db:"data"`
}

type getProductItemsForKouTransformer = bob.SliceTransformer[GetProductItemsForKouRow, []GetProductItemsForKouRow]

type getProductItemsForKou struct {
	Arg1 bob.Expression
}

func (o getProductItemsForKou) args() iter.Seq[orm.ArgWithPosition] {
	return func(yield func(arg orm.ArgWithPosition) bool) {
		if !yield(orm.ArgWithPosition{
			Name:       "arg1",
			Start:      551,
			Stop:       553,
			Expression: o.Arg1,
		}) {
			return
		}
	}
}

func (o getProductItemsForKou) raw(from, to int) string {
	return getProductItemsForKouSQL[from:to]
}

func (o getProductItemsForKou) subExpr(from, to int) bob.Expression {
	return orm.ArgsToExpression(getProductItemsForKouSQL, from, to, o.args())
}

func (o getProductItemsForKou) WriteSQL(ctx context.Context, w io.Writer, d bob.Dialect, start int) ([]any, error) {
	return o.subExpr(0, len(getProductItemsForKouSQL)).WriteSQL(ctx, w, d, start)
}
