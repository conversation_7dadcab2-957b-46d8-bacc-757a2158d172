-- Code generated by BobGen psql v0.41.1. DO NOT EDIT.
-- This file is meant to be re-generated in place and/or deleted at any time.

-- GetProductItemsForKou

SELECT "product_items"."id" AS "id", "product_items"."created_time" AS "created_time", "product_items"."updated_time" AS "updated_time", "product_items"."hash" AS "hash", "product_items"."item_id" AS "item_id", "product_items"."platform" AS "platform", "product_items"."spec" AS "spec", "product_items"."filter" AS "filter", "product_items"."mark" AS "mark", "product_items"."data" AS "data"
FROM product_items
WHERE NOT (mark ?| ARRAY['kou','kou_ignore']) AND to_tsvector('english', spec->>'title') @@ plainto_tsquery('english', 'wall art') limit $1;
