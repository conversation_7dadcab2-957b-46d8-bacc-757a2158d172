// Code generated by BobGen psql v0.41.1. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package db1_rawquery

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/dialect/psql"
	testutils "github.com/stephenafamo/bob/test/utils"
)

func TestGetProductItemsForKou(t *testing.T) {
	t.Run("Base", func(t *testing.T) {
		var sb strings.Builder

		query := GetProductItemsForKou(random_int64(nil))

		if _, err := query.WriteQuery(t.Context(), &sb, 1); err != nil {
			t.Fatal(err)
		}

		if diff := cmp.Diff(getProductItemsForKouSQL, sb.String()); diff != "" {
			t.Fatalf("unexpected result (-got +want):\n%s", diff)
		}
	})

	t.Run("Mod", func(t *testing.T) {
		var sb strings.Builder

		query := GetProductItemsForKou(random_int64(nil))

		if _, err := psql.Select(query).WriteQuery(t.Context(), &sb, 1); err != nil {
			t.Fatal(err)
		}

		queryDiff, err := testutils.QueryDiff(getProductItemsForKouSQL, sb.String(), formatQuery)
		if err != nil {
			t.Fatal(err)
		}
		if queryDiff != "" {
			fmt.Println(sb.String())
			t.Fatalf("unexpected result (-got +want):\n%s", queryDiff)
		}
	})

	t.Run("Scanning", func(t *testing.T) {
		if testDB == nil {
			t.Skip("skipping test, no DSN provided")
		}

		ctxTx, cancel := context.WithCancel(t.Context())
		defer cancel()

		tx, err := testDB.Begin(ctxTx)
		if err != nil {
			t.Fatalf("Error starting transaction: %v", err)
		}

		defer func() {
			if err := tx.Rollback(ctxTx); err != nil {
				t.Fatalf("Error rolling back transaction: %v", err)
			}
		}()

		query, args, err := bob.Build(ctxTx, psql.Select(GetProductItemsForKou(random_int64(nil))))
		if err != nil {
			t.Fatal(err)
		}

		rows, err := tx.QueryContext(ctxTx, query, args...)
		if err != nil {
			t.Fatal(err)
		}
		defer rows.Close()

		columns, err := rows.Columns()
		if err != nil {
			t.Fatal(err)
		}

		if len(columns) != 10 {
			t.Fatalf("expected %d columns, got %d", 10, len(columns))
		}

		if columns[0] != "id" {
			t.Fatalf("expected column %d to be %s, got %s", 0, "id", columns[0])
		}

		if columns[1] != "created_time" {
			t.Fatalf("expected column %d to be %s, got %s", 1, "created_time", columns[1])
		}

		if columns[2] != "updated_time" {
			t.Fatalf("expected column %d to be %s, got %s", 2, "updated_time", columns[2])
		}

		if columns[3] != "hash" {
			t.Fatalf("expected column %d to be %s, got %s", 3, "hash", columns[3])
		}

		if columns[4] != "item_id" {
			t.Fatalf("expected column %d to be %s, got %s", 4, "item_id", columns[4])
		}

		if columns[5] != "platform" {
			t.Fatalf("expected column %d to be %s, got %s", 5, "platform", columns[5])
		}

		if columns[6] != "spec" {
			t.Fatalf("expected column %d to be %s, got %s", 6, "spec", columns[6])
		}

		if columns[7] != "filter" {
			t.Fatalf("expected column %d to be %s, got %s", 7, "filter", columns[7])
		}

		if columns[8] != "mark" {
			t.Fatalf("expected column %d to be %s, got %s", 8, "mark", columns[8])
		}

		if columns[9] != "data" {
			t.Fatalf("expected column %d to be %s, got %s", 9, "data", columns[9])
		}
	})
}
