// Code generated by BobGen psql v0.41.1. DO NOT EDIT.
// This file is meant to be re-generated in place and/or deleted at any time.

package db1_rawquery

import (
	"bytes"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jaswdr/faker/v2"
	"github.com/stephenafamo/bob"
	"github.com/stephenafamo/bob/types"
	pg_query "github.com/wasilibs/go-pgquery"
)

// Set the testDB to enable tests that use the database
var testDB bob.Transactor[bob.Tx]

func formatQuery(s string) (string, error) {
	aTree, err := pg_query.Parse(s)
	if err != nil {
		return "", err
	}

	return pg_query.Deparse(aTree)
}

var defaultFaker = faker.New()

func random_int64(f *faker.Faker, limits ...string) int64 {
	if f == nil {
		f = &defaultFaker
	}

	return f.Int64()
}

func random_string(f *faker.Faker, limits ...string) string {
	if f == nil {
		f = &defaultFaker
	}

	val := strings.Join(f.Lorem().Words(f.IntBetween(1, 5)), " ")
	if len(limits) == 0 {
		return val
	}
	limitInt, _ := strconv.Atoi(limits[0])
	if limitInt > 0 && limitInt < len(val) {
		val = val[:limitInt]
	}
	return val
}

func random_time_Time(f *faker.Faker, limits ...string) time.Time {
	if f == nil {
		f = &defaultFaker
	}

	year := time.Hour * 24 * 365
	min := time.Now().Add(-year)
	max := time.Now().Add(year)
	return f.Time().TimeBetween(min, max)
}

func random_types_JSON_json_RawMessage_(f *faker.Faker, limits ...string) types.JSON[json.RawMessage] {
	if f == nil {
		f = &defaultFaker
	}

	s := &bytes.Buffer{}
	s.WriteRune('{')
	for i := range f.IntBetween(1, 5) {
		if i > 0 {
			fmt.Fprint(s, ", ")
		}
		fmt.Fprintf(s, "%q:%q", f.Lorem().Word(), f.Lorem().Word())
	}
	s.WriteRune('}')
	return types.NewJSON[json.RawMessage](s.Bytes())
}
