//go:build ignore

package main

import (
    "log"
    
    "entgo.io/ent/entc"
    "entgo.io/ent/entc/gen"
)

//go:generate go run entc.go
func main() {
    opts := []entc.Option{
        entc.FeatureNames("intercept", "schema/snapshot"),
        // entc.Dependency(
        //     entc.DependencyType(&http.Client{}),
        // ),
        entc.TemplateFiles(
            "template/query_where.tmpl",
            "template/delete_where.tmpl",
            "template/update_where.tmpl",
        ),
    }
    err := entc.Generate(
        "../schema", &gen.Config{
            Features: []gen.Feature{gen.FeatureVersionedMigration,gen.FeatureUpsert},
            Header: `
			// Code generated by ent, DO NOT EDIT.
		`,
            // A usage for custom templates with external functions.
            // Templates: []*gen.Template{
            //     gen.MustParse(
            //         gen.NewTemplate("static").
            //             Funcs(template.FuncMap{"title": strings.ToTitle}).
            //             ParseFiles("template/static.tmpl"),
            //     ),
            // },
        }, opts...,
    )
    if err != nil {
        log.Fatal("running ent codegen:", err)
    }
}
