//go:build ignore

package main

import (
    "context"
    "log"
    "omnix/ddl/db1/migrate"
    "os"
    "strings"
    "text/template"

    atlas "ariga.io/atlas/sql/migrate"
    "entgo.io/ent/dialect"
    "entgo.io/ent/dialect/sql/schema"
    _ "github.com/lib/pq"
)

func main() {
    if len(os.Args) < 3 {
        log.Fatal("执行格式为: go run main.go 文件名 测试数据库dsn")
    }

    ctx := context.Background()
    // Create a local migration directory able to understand Atlas migration file format for replay.
    dir, err := atlas.NewLocalDir("ddl/db1_migrations")
    if err != nil {
        log.Fatalf("failed creating atlas migration directory: %v", err)
    }
    templateFuncs := template.FuncMap{
        "upper": strings.ToUpper,
        "now":   atlas.NewVersion,
    }
    formatter, err := atlas.NewTemplateFormatter(template.Must(template.New("").Funcs(templateFuncs).Parse(
        "{{ with .Version }}{{ . }}{{ else }}{{ now }}{{ end }}{{ with .Name }}_{{ . }}{{ end }}.sql",
    )),
        template.Must(template.New("").Funcs(templateFuncs).Parse(
            `-- +goose Up
{{ range .Changes }}{{ with .Comment }}{{ printf "-- %s%s\n" (slice . 0 1 | upper ) (slice . 1) }}{{ end }}{{ printf "%s%s\n" .Cmd (or $.Delimiter ";") }}{{ end }}`,
        )))
    if err != nil {
        log.Fatal(err)
    }
    // Migrate diff options.
    opts := []schema.MigrateOption{
        schema.WithDir(dir),                         // provide migration directory
        schema.WithMigrationMode(schema.ModeReplay), // provide migration mode
        schema.WithDialect(dialect.Postgres),        // Ent dialect to use
        schema.WithFormatter(formatter),
        schema.WithDropColumn(true),
        schema.WithDropIndex(true),
        schema.WithIndent("    "),
    }
    // os.Args = append(os.Args, "ss1")
    if len(os.Args) < 2 {
        log.Fatalln("migration name is required. Use: 'go run -mod=mod ent/migrate/main.go <name>'")
    }

    // Generate migrations using Atlas support for MySQL (note the Ent dialect option passed above).
    err = migrate.NamedDiff(
        ctx,
        //fmt.Sprintf("**********************************************************/migratetest?search_path=public&sslmode=disable"),
        os.Args[2],
        os.Args[1], opts...,
    )
    if err != nil {
        log.Fatalf("failed generating migration file: %v", err)
    }
}
