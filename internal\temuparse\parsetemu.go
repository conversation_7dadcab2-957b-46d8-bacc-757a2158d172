package temuparse

import (
    "omnix/genpb/msgpb"
    "github.com/qwenode/rr"
    "strings"

    "github.com/tidwall/gjson"
)

func ToProductSpec(bmwDsItem string) (*msgpb.ProductSpec, error) {
    goods := gjson.Parse(bmwDsItem).Get("common_rec_goods")
    var (
        o = &msgpb.ProductSpec{}
    )
    o.Title = goods.Get("title").String()
    o.Currency = goods.Get("price_info.currency").String()
    o.Price = goods.Get("price_info.price").Int()
    o.Id = goods.Get("goods_id").String()
    o.SkuId = goods.Get("sku_id").String()
    o.ShopId = goods.Get("mall_id").String()
    o.Link = rr.StringGetFirst(goods.Get("seo_link_url").String(), "?")
    o.FeaturedImage = goods.Get("image.url").String()
    sales := goods.Get("sales_tip").String()
    o.Sales = rr.StringSanitizeAsInt64(sales)
    if strings.Contains(strings.ToLower(sales), "k") {
        o.Sales *= 1000
    }
    o.TemuCats = make([]string, 0)
    goods.Get("cat_ids").ForEach(func(_, v gjson.Result) bool {
        o.TemuCats = append(o.TemuCats, v.String())
        return true
    })

    return o, nil
}
