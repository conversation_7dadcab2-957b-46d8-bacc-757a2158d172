package temuparse

import (
    "omnix/genpb/msgpb"
    "reflect"
    "github.com/qwenode/rr"
    "testing"
)

func TestToProductSpec(t *testing.T) {
    type args struct {
        bmwDsItem string
    }
    tests := []struct {
        name    string
        args    args
        want    *msgpb.ProductSpec
        wantErr bool
    }{
        {
            args: args{
                bmwDsItem: rr.FileGetContents("./testdata/bmw.json"),
            },
            want: &msgpb.ProductSpec{
                Id:            "601101678750784",
                Title:         "Vibrant Red Bikini Beach Girl Metal Sign - Aluminum Wall Art with Tropical Beach Scene, Perfect for Home, Bar, Cafe Decor - Humorous & Stylish Room Decoration, Beach Theme Decor",
                Link:          "/vibrant-red--girl-metal-sign-aluminum-wall-art-with-tropical-beach-scene--home-bar-cafe-decor-humorous-stylish-room-decoration-beach-theme-decor-g-601101678750784.html",
                Price:         525,
                Sales:         2,
                ShopId:        "634418216782497",
                TemuCats:      []string{"9711","12986","40381"},
                FeaturedImage: "https://img.kwcdn.com/product/20237f3dc4/24b93240-1e74-42dd-b8de-93ff003d4584_800x800.jpeg",
                Image:         nil,
                Currency:      "USD",
                SkuId:         "17600985333075",
            },
        },
    }
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            got, err := ToProductSpec(tt.args.bmwDsItem)
            if (err != nil) != tt.wantErr {
                t.Errorf("ToProductSpec() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(got, tt.want) {
                t.Errorf("ToProductSpec() got = %v, want %v", got, tt.want)
            }
        })
    }
}
