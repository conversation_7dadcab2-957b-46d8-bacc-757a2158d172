syntax = "proto3";

package admin;

import "buf/validate/validate.proto";


option go_package = "omnix/genpb/adminpb;adminpb";


// 登录请求
message LoginRequest {
    //用户名
    string username = 1 [(buf.validate.field).required = true, (buf.validate.field).string.min_len = 6, (buf.validate.field).string.max_len = 30, (buf.validate.field).string.pattern = "^[a-zA-Z0-9]+$"];
    // 密码 
    string password = 2 [(buf.validate.field).required = true, (buf.validate.field).string.min_len = 6, (buf.validate.field).string.max_len = 60];
}
// 登录响应
message LoginResponse {
    //用户名
    string username = 1;
    //登录token
    string token = 2;
    //  过期时间 
    int64 expire_at = 3;
}
// 未登录的后台账号功能 20250827
service AuthService {
    // 登录
    rpc Login (LoginRequest) returns (LoginResponse);
}