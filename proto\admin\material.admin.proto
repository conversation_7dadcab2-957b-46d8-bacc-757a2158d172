syntax = "proto3";

package admin;

import "common.msg.proto";
import "material.msg.proto";


option go_package = "omnix/genpb/adminpb;adminpb";

//查询素材分组列表 请求
message QueryMaterialGroupListRequest {
    msgpb.PageSizeRequest page_size = 1;
    string name = 2; // 分组名称筛选
}
//查询素材分组列表 响应
message QueryMaterialGroupListResponse {
    int64 total = 1;
    repeated msgpb.MaterialGroup items = 2;
}

//创建素材分组 请求
message CreateMaterialGroupRequest {
    string name = 1; // 分组名称
}
//创建素材分组 响应
message CreateMaterialGroupResponse {
    msgpb.MaterialGroup item = 1;
}

//更新素材分组 请求
message UpdateMaterialGroupRequest {
    int64 id = 1;   // 分组ID
    string name = 2; // 分组名称
}
//更新素材分组 响应
message UpdateMaterialGroupResponse {
    msgpb.MaterialGroup item = 1;
}

//删除素材分组 请求
message DeleteMaterialGroupRequest {
    repeated int64 id = 1; // 分组ID
}
//删除素材分组 响应
message DeleteMaterialGroupResponse {

}

//获取素材分组详情 请求
message GetMaterialGroupRequest {
    int64 id = 1; // 分组ID
}
//获取素材分组详情 响应
message GetMaterialGroupResponse {
    msgpb.MaterialGroup item = 1;
}
//查询素材列表 请求
message QueryMaterialListRequest {
    msgpb.PageSizeRequest page_size = 1;
    // 查询标题 20250904
    string title = 2;
}
//查询素材列表 响应
message QueryMaterialListResponse {
    int64 total=1;
    repeated msgpb.Material items = 2;
}
// 素材管理 20250904
service MaterialService {
    // 查询素材分组列表 接口 20250904
    rpc QueryMaterialGroupList (QueryMaterialGroupListRequest) returns (QueryMaterialGroupListResponse);

    // 创建素材分组 接口 20250904
    rpc CreateMaterialGroup (CreateMaterialGroupRequest) returns (CreateMaterialGroupResponse);

    // 更新素材分组 接口 20250904
    rpc UpdateMaterialGroup (UpdateMaterialGroupRequest) returns (UpdateMaterialGroupResponse);

    // 删除素材分组 接口 20250904
    rpc DeleteMaterialGroup (DeleteMaterialGroupRequest) returns (DeleteMaterialGroupResponse);

    // 获取素材分组详情 接口 20250904
    rpc GetMaterialGroup (GetMaterialGroupRequest) returns (GetMaterialGroupResponse);

    // 查询素材列表 接口 20250904
    rpc QueryMaterialList (QueryMaterialListRequest) returns (QueryMaterialListResponse);
    

}