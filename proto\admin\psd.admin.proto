syntax = "proto3";

package admin;

import "common.msg.proto";
import "psd.msg.proto";
import "buf/validate/validate.proto";


option go_package = "omnix/genpb/adminpb;adminpb";

//查询PSD模板列表 请求
message QueryPsdListRequest {
  msgpb.PageSizeRequest page_size = 1;
}
//查询PSD模板列表 响应
message QueryPsdListResponse {
  int64 total = 1;
  repeated msgpb.PSD items = 2;
}

//上传PSD模板文件 请求
message UploadPsdFileRequest {
  string hash = 1 [
    (buf.validate.field) = {
      required: true,
      string: {
        len: 40,
      }
    }
  ];
}
//上传PSD模板文件 响应
message UploadPsdFileResponse {
}

//下载PSD模板文件 请求
message DownloadPsdFileRequest {
  int64 id = 1;   // 模板ID
}
//下载PSD模板文件 响应
message DownloadPsdFileResponse {

}

//删除PSD模板文件 请求
message DeletePsdFileRequest {
  repeated int64 id = 1; // 模板ID
}
//删除PSD模板文件 响应
message DeletePsdFileResponse {

}

// PSD模板管理 20250916
service PsdService {
  // 查询PSD模板列表 接口 20250916
  rpc QueryPsdList (QueryPsdListRequest) returns (QueryPsdListResponse);

  // 创建PSD分组 接口 20250916
  rpc UploadPsdFile (UploadPsdFileRequest) returns (UploadPsdFileResponse);

  // 更新PSD分组 接口 20250916
  rpc DownloadFile (DownloadPsdFileRequest) returns (DownloadPsdFileResponse);

  // 删除PSD分组 接口 20250916
  rpc DeletePsdFile (DeletePsdFileRequest) returns (DeletePsdFileResponse);

}