syntax = "proto3";

package admin;

import "common.msg.proto";
import "psd.msg.proto";


option go_package = "omnix/genpb/adminpb;adminpb";

//查询PSD分组列表 请求
message QueryPsdGroupListRequest {
  msgpb.PageSizeRequest page_size = 1;
  string name = 2; // 分组名称筛选
}
//查询PSD分组列表 响应
message QueryPsdGroupListResponse {
  int64 total = 1;
  repeated msgpb.PSDGroup items = 2;
}

//创建PSD分组 请求
message CreatePsdGroupRequest {
  string name = 1; // 分组名称
}
//创建PSD分组 响应
message CreatePsdGroupResponse {
  msgpb.PSDGroup item = 1;
}

//更新PSD分组 请求
message UpdatePsdGroupRequest {
  int64 id = 1;   // 分组ID
  string name = 2; // 更新的分组名称
}
//更新PSD分组 响应
message UpdatePsdGroupResponse {
  msgpb.PSDGroup item = 1;
}

//删除PSD分组 请求
message DeletePsdGroupRequest {
  repeated int64 id = 1; // 分组ID
}
//删除PSD分组 响应
message DeletePsdGroupResponse {

}

//获取PSD分组详情 请求
message GetPsdGroupRequest {
  int64 id = 1; // 分组ID
}
//获取PSD分组详情 响应
message GetPsdGroupResponse {
  msgpb.PSDGroup item = 1;
}
//查询PSD模板列表 请求
message QueryPSDListRequest {
  msgpb.PageSizeRequest page_size = 1;
  // 查询id
  int64 id = 2;
}
//查询PSD模板列表 响应
message QueryPSDListResponse {
  int64 total=1;
  repeated msgpb.PSD items = 2;
}


// PSD分组管理 20250910
service PsdGroupService {
  // 查询PSD分组列表 接口 20250910
  rpc QueryPsdGroupList (QueryPsdGroupListRequest) returns (QueryPsdGroupListResponse);

  // 创建PSD分组 接口 20250910
  rpc CreatePsdGroup (CreatePsdGroupRequest) returns (CreatePsdGroupResponse);

  // 更新PSD分组 接口 20250910
  rpc UpdatePsdGroup (UpdatePsdGroupRequest) returns (UpdatePsdGroupResponse);

  // 删除PSD分组 接口 20250910
  rpc DeletePsdGroup (DeletePsdGroupRequest) returns (DeletePsdGroupResponse);

  // 获取PSD分组详情 接口 20250910
  rpc GetPsdGroup (GetPsdGroupRequest) returns (GetPsdGroupResponse);

  // 查询PSD模板列表 接口 20250910
  rpc QueryPSDList (QueryPSDListRequest) returns (QueryPSDListResponse);
}