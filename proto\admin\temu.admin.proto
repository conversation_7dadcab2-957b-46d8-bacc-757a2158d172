syntax = "proto3";

package admin;

import "common.msg.proto";
import "google/protobuf/timestamp.proto";
import "product.msg.proto";


option go_package = "omnix/genpb/adminpb;adminpb";

//查询Temu数据列表 请求
message QueryTemuListRequest {
    msgpb.PageSizeRequest page_size = 1;
    // 搜索标题 20250828
    string title = 2;
    // 过滤分类 20250829
    repeated string temu_cats = 3;
}
//查询Temu数据列表 响应
message QueryTemuListResponse {
    // 数据列表 20250828
    repeated msgpb.ProductItem items = 1;
    // 总数量 20250828
    int64 total = 2;
    
}
// Temu服务 20250827
service TemuService {
    // 查询Temu数据列表 接口 20250828
    rpc QueryTemuList (QueryTemuListRequest) returns (QueryTemuListResponse);


}