syntax = "proto3";

package admin;

import "common.msg.proto";
import "material.msg.proto";

option go_package = "omnix/genpb/adminpb;adminpb";

//商标列表 请求
message QueryTrademarkListRequest {
  msgpb.PageSizeRequest page_size = 1;

}
//商标列表 响应
message QueryTrademarkListResponse {
    int64 total = 1;
    repeated msgpb.RegisteredTrademark items = 2;
}

// 商标管理 20250808
service TrademarkService {
  // 商标列表数据查询 接口 20250827
  rpc QueryTrademarkList (QueryTrademarkListRequest) returns (QueryTrademarkListResponse);


}