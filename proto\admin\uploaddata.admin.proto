syntax = "proto3";

package admin;

import "buf/validate/validate.proto";

option go_package = "omnix/genpb/adminpb;adminpb";


//Temu BMW数据上报 请求
message TemuBmwRequest {
    // 数据校验码 20250808
    string hash = 1 [
        (buf.validate.field) = {
            required: true,
            string: {
                len: 40,
            }
        }
    ];
    // 上报数据 20250808
    string data = 2;
}
//Temu BMW数据上报 响应
message TemuBmwResponse {
    int64 success = 1;
    int64 skip = 2;
    int64 error = 3;
    // 有销量的产品数量 20250811
    int64 sales = 4;
}
//商标数据上报 请求
message TrademarkRequest {
    // 数据校验码 20250808
    string hash = 1 [
        (buf.validate.field) = {
            required: true,
            string: {
                len: 40,
            }
        }
    ];
    // 上报数据,结构为对象,里面必须包含字段 id(类型int),系统根据id进行更新或创建 20250808
    string data = 2;
    
}
//商标数据上报 响应
message TrademarkResponse {

}

// 采集数据上报 20250808
service UploadDataService {
    // Temu BMW数据上报 接口 20250808
    rpc TemuBmw (TemuBmwRequest) returns (TemuBmwResponse);
    // 商标数据上报 接口 20250827
    rpc Trademark (TrademarkRequest) returns (TrademarkResponse);


}