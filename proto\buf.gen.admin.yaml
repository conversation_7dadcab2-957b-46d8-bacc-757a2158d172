version: v2
plugins:
  - local: protoc-gen-go
    out: ./genpb/adminpb
    opt: paths=source_relative
  - local: protoc-gen-connect-go
    out: ./genpb/adminpb
    opt: paths=source_relative
  - local: protoc-gen-connectclient-go
    include_imports: true
    out: ./genpb/adminpb
    opt:
      - paths=source_relative
  - local: protoc-gen-gg
    out: ./api/admin
    opt: paths=source_relative,connect-dir=./api/admin,connect-package=adminservice,pkg-prefix=omnix/api/admin
  - local: protoc-gen-connect-openapi
    out: ./tspb/docs/admin
    opt:
      - base=proto/admin/openapi.yaml
      - path=admin.yaml
      - allow-get
  - local: protoc-gen-doc
    out: ./tspb/docs/admin
    opt:
      - html,admin.html
  - local: protoc-gen-es
    include_imports: true
    out: ./apps/dashboard/src/api
  - local: protoc-gen-connect-query
    out: ./apps/dashboard/src/api
inputs:
  - directory: proto/admin/
