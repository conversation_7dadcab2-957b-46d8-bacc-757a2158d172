version: v2

plugins:
  - local: protoc-gen-go
    out: ./genpb/flowpb
    opt: paths=source_relative
  - local: protoc-gen-go_temporal
    out: ./genpb/flowpb
    opt: paths=source_relative,workflow-update-enabled=true,docs-out=./genpb/docs/flow.md
  - local: protoc-gen-go-setters
    out: ./genpb/flowpb
    opt: paths=source_relative
  - local: protoc-gen-gg
    out: ./api/flow
    opt: paths=source_relative,workflow-dir=./api/flow,workflow-package=flowservice,pkg-prefix=omnix/api/flow
inputs:
  - directory: proto/flow/
