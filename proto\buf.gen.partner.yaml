version: v2
plugins:
  - local: protoc-gen-go
    out: ./genpb/partnerpb
    opt: paths=source_relative
  - local: protoc-gen-connect-go
    out: ./genpb/partnerpb
    opt: paths=source_relative
  - local: protoc-gen-connectclient-go
    include_imports: true
    out: ./genpb/partnerpb
    opt:
      - paths=source_relative
  - local: protoc-gen-gg
    out: ./api/partner
    opt: paths=source_relative,connect-dir=./api/partner,connect-package=partnerservice,pkg-prefix=omnix/api/partner
  - local: protoc-gen-connect-openapi
    out: ./tspb/docs/partner
    opt:
      - base=proto/partner/openapi.yaml
      - path=partner.yaml
      - allow-get
  - local: protoc-gen-doc
    out: ./tspb/docs/partner
    opt:
      - html,partner.html
inputs:
  - directory: proto/partner/
