syntax = "proto3";

package enumpb;


option go_package = "omnix/genpb/enumpb;enumpb";


// 排序方向,有排序字段的必须指定排序字段,否则排序方向也不会生效 20250717
enum SortDirection {
    // 默认不排序 20250717
    SORT_DIRECTION_UNSPECIFIED = 0;
    // 正序 20250717
    SORT_DIRECTION_ASC = 1;
    // 倒序 20250717
    SORT_DIRECTION_DESC = 2;

}


// 目标平台 20250806
enum TargetPlatform {
    TARGET_PLATFORM_UNSPECIFIED = 0;
    TARGET_PLATFORM_TEMU = 1;
}

// 产品标记 20250904
enum ProductItemMark {
    PRODUCT_ITEM_MARK_UNSPECIFIED = 0;
    // 已执行全量抠图标记 20250904
    PRODUCT_ITEM_MARK_KOU = 1;
    // 忽略抠图 20250910
    PRODUCT_ITEM_MARK_KOU_IGNORE = 2;
}

// 后台账号状态 20250827
enum AdministratorState {
    ADMINISTRATOR_STATE_UNSPECIFIED = 0;
    ADMINISTRATOR_STATE_ACTIVE = 1;
}


// 工作流队列列表,每个队列的worker必须注册一样的处理函数,否则会找不到 20250903
enum FlowQueue {
    FLOW_QUEUE_UNSPECIFIED = 0;
    // 默认工作流队列 20250903
    FLOW_QUEUE_DEFAULT = 1;
    // Comfyui工作队列 20250904
    FLOW_QUEUE_COMFYUI = 2;
}

