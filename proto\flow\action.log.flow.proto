syntax = "proto3";

import "temporal/v1/temporal.proto";
import "google/protobuf/timestamp.proto";

option go_package = "omnix/genpb/flowpb;flowpb";

service ActionLogFlow {
    option (temporal.v1.service) = {task_queue: "default"};

    // 记录操作日志 接口 20250626
    rpc WriteActionLog (WriteActionLogRequest) returns (WriteActionLogResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {
                seconds: 10
            }
            retry_policy: {
                max_attempts: 3
            }
        };
        option (temporal.v1.workflow) = {
            id: "${! id}/WriteActionLog"
            id_reuse_policy: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY
            run_timeout: {seconds: 30}
        };
    }
}

// 记录操作日志 请求
message WriteActionLogRequest {
    // 操作ID 20250620
    string id = 1;
    // 记录时间 20250620
    google.protobuf.Timestamp created_time = 2;
    // 操作人ID,管理员或系统=-10 20250620
    string admin_id = 3;
    // 操作人名称 20250620
    string admin_name = 4;
    // 请求数据 20250620
    string request_data = 5;
    // 关联目标id,配合数据表名查询目标 20250620
    string referrer_id = 6;
    // 数据表名 20250626
    string index_id = 7;
    // 操作IP 20250620
    string ip_address = 8;
    // 浏览器UA 20250620
    string user_agent = 9;
    // 操作内容 20250620
    string message = 10;
}

// 记录操作日志 响应
message WriteActionLogResponse {
    // 结果 20250626
    string message = 1;
}
