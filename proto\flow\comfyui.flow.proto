syntax = "proto3";

import "flow.msg.proto";
import "temporal/v1/temporal.proto";

option go_package = "omnix/genpb/flowpb;flowpb";


//获取Comfyui队列大小 请求
message QueryComfyuiQueueSizeRequest {
    // comfyui 服务器 20250901
    string adapter_host = 1;
}

//获取Comfyui队列大小 响应
message QueryComfyuiQueueSizeResponse {
    int32 running = 1;
    int32 pending = 2;
}
//Comfyui一键抠图 请求
message ComfyuiBackgroundRemoveRequest {
    // 要抠图的图片url 20250901
    string url = 1;
    string id = 2;
}
//Comfyui一键抠图 响应
message ComfyuiBackgroundRemoveResponse {
    // 抠图成功的图片url 20250901
    string url = 1;
    msgpb.FaultMessage fault_message = 2;
    // 文件sha1 20250904
    string sha1=3;
}

//下载URL资源并上传到Comfyui 请求
message DownUrlAndUploadToComfyuiRequest {
    string url = 1;
    // comfyui 服务器 20250901
    string adapter_host = 2;
}
//下载URL资源并上传到Comfyui 响应
message DownUrlAndUploadToComfyuiResponse {
    // 上传到comfyui的文件名 20250902
    string filename = 1;
    msgpb.FaultMessage fault_message = 2;

}
//查询可用的Comfyui服务器 请求
message QueryAvailableComfyuiServerRequest {
    // comfyui工作流名称 如(xxx.json),检查服务器是否有需要的工作流 20250902
    string name = 1;
}
//查询可用的Comfyui服务器 响应
message QueryAvailableComfyuiServerResponse {
    msgpb.FaultMessage fault_message = 1;
    string adapter_host = 2;
}
//执行Comfyui任务 请求
message RunComfyuiPromptRequest {
    // 请求体 20250903
    string prompt = 1;
    // 执行的服务器 20250903
    string adapter_host = 2;
}
//执行Comfyui任务 响应
message RunComfyuiPromptResponse {
    msgpb.FaultMessage fault_message = 1;
    // 任务id 20250903
    string prompt_id = 2;
}
//等待抠图完成>下载图片>上传到s3>返回s3链接 请求
message ComfyuiBackgroundRemovePostprocessRequest {
    string prompt_id = 1;
    string adapter_host = 2;
}
//等待抠图完成>下载图片>上传到s3>返回s3链接 响应
message ComfyuiBackgroundRemovePostprocessResponse {
    msgpb.FaultMessage fault_message = 1;
    // 图片链接 20250903
    string s3url = 2;
    // 文件sha1 20250904
    string sha1 = 3;
}
//删除上传到Comfyui的素材文件 请求
message DeleteComfyuiUploadedMaterialRequest {
    // 要删除的文件名 20250903
    string filename = 1;
    string adapter_host = 2;
}
//删除上传到Comfyui的素材文件 响应
message DeleteComfyuiUploadedMaterialResponse {

}
// comfyui功能 20250901
service ComfyuiFlow {
    option (temporal.v1.service) = {task_queue: "comfyui"};
    // =============================== AI一键抠图 开始 20250901 ===============================


    // Comfyui一键抠图 任务编排 20250901
    rpc ComfyuiBackgroundRemove (ComfyuiBackgroundRemoveRequest) returns (ComfyuiBackgroundRemoveResponse) {
        option (temporal.v1.workflow) = {
            id: "${!id}/ComfyuiBackgroundRemove"
            id_reuse_policy: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY
            execution_timeout: {seconds: 600}
        };
    }

    // 等待抠图完成>下载图片>上传到s3>返回s3链接 Activity 20250903
    rpc ComfyuiBackgroundRemovePostprocess (ComfyuiBackgroundRemovePostprocessRequest) returns (ComfyuiBackgroundRemovePostprocessResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 30}
            start_to_close_timeout: {seconds: 300}
            retry_policy: {
                max_attempts: 10
            }
        };
    }


    // =============================== AI一键抠图 结束 20250901 ===============================

    // =============================== Comfyui相关接口功能 开始 20250901 ===============================

    // 执行Comfyui任务 Activity 20250903
    rpc RunComfyuiPrompt (RunComfyuiPromptRequest) returns (RunComfyuiPromptResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
            retry_policy: {
                max_attempts: 10
            }
        };
    }

    // 获取Comfyui队列大小 Activity 20250901
    rpc QueryComfyuiQueueSize (QueryComfyuiQueueSizeRequest) returns (QueryComfyuiQueueSizeResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }

    // 下载URL资源并上传到Comfyui Activity 20250902
    rpc DownUrlAndUploadToComfyui (DownUrlAndUploadToComfyuiRequest) returns (DownUrlAndUploadToComfyuiResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 30}
            start_to_close_timeout: {seconds: 300}
        };
    }
    // 查询可用的Comfyui服务器 Activity 20250902
    rpc QueryAvailableComfyuiServer (QueryAvailableComfyuiServerRequest) returns (QueryAvailableComfyuiServerResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 10}
            start_to_close_timeout: {seconds: 300}
        };
    }
    // 删除上传到Comfyui的素材文件 接口 20250903
    rpc DeleteComfyuiUploadedMaterial (DeleteComfyuiUploadedMaterialRequest) returns (DeleteComfyuiUploadedMaterialResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
            retry_policy: {
                max_attempts: 5
            }
        };
    }


    // =============================== Comfyui相关接口功能 结束 20250901 ===============================
}


