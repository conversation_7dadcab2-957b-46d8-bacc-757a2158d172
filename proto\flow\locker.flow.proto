syntax = "proto3";

import "temporal/v1/temporal.proto";
import "google/protobuf/empty.proto";

option go_package = "omnix/genpb/flowpb;flowpb";

//Photoshop全局信号锁 请求
message PsMutexLockerRequest {
    // 每个独占一个id 20250910
    string id = 1;
}
//Photoshop全局信号锁 响应
message PsMutexLockerResponse {

}


//获取PsMutexLocker锁 请求
message EnsurePsMutexLockerStartedAndSignalRequest {
    // ps服务器 20250910
    string adapter_host = 1;
    // 调用方执行id 20250910
    string workflow_id = 2;
}
//获取PsMutexLocker锁 响应
message EnsurePsMutexLockerStartedAndSignalResponse {
    // 全局锁运行ID 20250910
    string LockerId = 1;
}
// 全局锁 20250910
service LockerFlow {
    option (temporal.v1.service) = {task_queue: "default"};

    // Photoshop全局信号锁 任务编排 20250910
    rpc PsMutexLocker (PsMutexLockerRequest) returns (PsMutexLockerResponse) {
        option (temporal.v1.workflow) = {
            id: "${!id}/PsMutexLocker"
            id_reuse_policy: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY
        };
    }
 

    // 获取PsMutexLocker锁 Activity 20250910
    rpc EnsurePsMutexLockerStartedAndSignal (EnsurePsMutexLockerStartedAndSignalRequest) returns (EnsurePsMutexLockerStartedAndSignalResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }

}


