syntax = "proto3";

import "flow.msg.proto";
import "temporal/v1/temporal.proto";

option go_package = "omnix/genpb/flowpb;flowpb";

//Ps白色背景转透明 请求
message PsRemoveWhiteBackgroundRequest {
    string id = 1;
    string adapter_host = 2;
    // 如果filename未填,则从url自动下载,如果都没设置,则失败 20250910
    string url = 3;
    // 文件已经在ps服务器的upload目录 20250910
    string filename = 4;
}
//Ps白色背景转透明 响应
message PsRemoveWhiteBackgroundResponse {
    msgpb.FaultMessage fault_message = 1;
    // 文件名,在ps服务器的output目录 20250910
    string filename = 2;
    // Ps的执行日志 20250910
    string log = 3;
}
//下载资源并上传到Ps目录 请求
message PsDownAndUploadRequest {
    string url = 1;
}
//下载资源并上传到Ps目录 响应
message PsDownAndUploadResponse {
    msgpb.FaultMessage fault_message = 1;
    // 在ps服务器的文件名 20250910
    string filename = 2;
    // 服务器上完整文件名 20250910
    string full_filepath = 3;
}
//原样复制Ps服务器文件从Output目录到Upload目录 请求
message PsCopyFileFromOutputToUploadRequest {
    // 在ps服务器output的文件名 20250910
    string filename = 1;
}

//原样复制Ps服务器文件从Output目录到Upload目录 响应
message PsCopyFileFromOutputToUploadResponse {
    msgpb.FaultMessage fault_message = 1;
}
//从Ps Upload目录删除文件 请求
message PsDeleteFileFromUploadRequest {
    // ps服务器upload下的文件名 20250910
    string filename = 1;
}
//从Ps Upload目录删除文件 响应
message PsDeleteFileFromUploadResponse {

}
//从Ps Output目录删除文件 请求
message PsDeleteFileFromOutputRequest {
    // ps服务器output下的文件名 20250910
    string filename = 1;
}
//从Ps Output目录删除文件 响应
message PsDeleteFileFromOutputResponse {

}
//PS合成素材与模板 请求
message PsCombineRequest {
    // ps服务器 20250910
    string adapter_host = 1;
    // 任务ID 20250911
    string id = 2;
    // 图片url 20250911
    string url = 3;
}
//PS合成素材与模板 响应
message PsCombineResponse {

}
// Ps自动化 20250910
service PhotoshopFlow {
    option (temporal.v1.service) = {task_queue: "comfyui"};

    // PS合成素材与模板 任务编排 20250910
    rpc PsCombine (PsCombineRequest) returns (PsCombineResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 30}
            start_to_close_timeout: {seconds: 300}
        };
        option (temporal.v1.workflow) = {
            id: "${!id}/PsCombine"
            id_reuse_policy: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY
            execution_timeout: {seconds: 300}
        };
    }

    // Ps白色背景转透明 任务编排 20250910
    // 同一个id同一时间只能有一个在运行中, id为 adapter_host的hash
    rpc PsRemoveWhiteBackground (PsRemoveWhiteBackgroundRequest) returns (PsRemoveWhiteBackgroundResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 30}
            start_to_close_timeout: {seconds: 120}
        };
        option (temporal.v1.workflow) = {
            id: "${!id}/PsRemoveWhiteBackground"
            id_reuse_policy: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE
            execution_timeout: {seconds: 300}
        };
    }
    // 下载资源并上传到Ps目录 Activity 20250910
    rpc PsDownAndUpload (PsDownAndUploadRequest) returns (PsDownAndUploadResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 30}
            start_to_close_timeout: {seconds: 300}
        };
    }

    // 原样复制Ps服务器文件从Output目录到Upload目录 Activity 20250910
    rpc PsCopyFileFromOutputToUpload (PsCopyFileFromOutputToUploadRequest) returns (PsCopyFileFromOutputToUploadResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }
    // 从Ps Upload目录删除文件 Activity 20250910
    rpc PsDeleteFileFromUpload (PsDeleteFileFromUploadRequest) returns (PsDeleteFileFromUploadResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }

    // 从Ps Output目录删除文件 Activity 20250910
    rpc PsDeleteFileFromOutput (PsDeleteFileFromOutputRequest) returns (PsDeleteFileFromOutputResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }


}


