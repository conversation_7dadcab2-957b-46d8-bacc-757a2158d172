syntax = "proto3";

import "comfyui.flow.proto";
import "flow.msg.proto";
import "product.msg.proto";
import "temporal/v1/temporal.proto";

option go_package = "omnix/genpb/flowpb;flowpb";

//根据分类ID遍历产品 请求
message CrawlGoodsListByOptIdRequest {
    // temu分类ID 20250807
    int32 opt_id = 1;
    // 每次请求间隔时间,单位毫秒 20250807
    int32 sleep = 2;
}
//根据分类ID遍历产品 响应
message CrawlGoodsListByOptIdResponse {
    // 分页进度 20250807
    int64 offset = 1;
    // 采集总量 20250807
    int64 total = 2;
}
//对Temu数据全量抠图 请求
message BatchKouKouTemuRequest {
    // 并发执行数量 20250904
    int32 concurrent = 1;
}
//对Temu数据全量抠图 响应
message BatchKouKouTemuResponse {
    // 成功数量 20250904
    int32 success = 1;
    // 失败数量 20250904
    int32 failed = 2;
}
//查询待抠图的Temu数据 请求
message GetTemuListForKouKouRequest {
    // 提取数量 20250904
    int32 count = 1;
}
//查询待抠图的Temu数据 响应
message GetTemuListForKouKouResponse {
    repeated msgpb.ProductItem items = 1;
}
//保存抠图结果到素材库 请求
message SaveTemuKouKouRequest {
    // 产品数据 20250904
    msgpb.ProductItem item = 1;
    // 抠图结果 20250904
    ComfyuiBackgroundRemoveResponse response = 2;
}
//保存抠图结果到素材库 响应
message SaveTemuKouKouResponse {

}
//执行temu抠图任务 请求
message ExecKouKouTemuRequest {
    msgpb.ProductItem item = 1;
}
//执行temu抠图任务 响应
message ExecKouKouTemuResponse {
    msgpb.FaultMessage fault_message = 1;
    // 抠图结果 20250904
    ComfyuiBackgroundRemoveResponse response = 2;
}
//更新抠图原材料状态 请求
message UpdateTemuStatusRequest {
    // 要增加的标记 20250910
    string mark = 1;
    msgpb.ProductItem item = 2;
}
//更新抠图原材料状态 响应
message UpdateTemuStatusResponse {

}
service TemuFlow {
    option (temporal.v1.service) = {task_queue: "default"};

    // =============================== 对采集的数据全量抠图 开始 20250904 ===============================

    // 对Temu数据全量抠图 任务编排 20250904
    rpc BatchKouKouTemu (BatchKouKouTemuRequest) returns (BatchKouKouTemuResponse) {
        option (temporal.v1.workflow) = {
            id: "BatchKouKouTemu"
            id_reuse_policy: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE_FAILED_ONLY
            execution_timeout: {seconds: 3600}
        };
    }
    // 执行temu抠图任务 Activity 20250904
    rpc ExecKouKouTemu (ExecKouKouTemuRequest) returns (ExecKouKouTemuResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 30}
            start_to_close_timeout: {seconds: 600}
        };
    }

    // 查询待抠图的Temu数据 Activity 20250904
    rpc GetTemuListForKouKou (GetTemuListForKouKouRequest) returns (GetTemuListForKouKouResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }
    // 保存抠图结果到素材库 Activity 20250904
    rpc SaveTemuKouKou (SaveTemuKouKouRequest) returns (SaveTemuKouKouResponse) {
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }

    // 更新抠图原材料状态 接口 20250910
    rpc UpdateTemuStatus (UpdateTemuStatusRequest) returns (UpdateTemuStatusResponse){
        option (temporal.v1.activity) = {
            start_to_close_timeout: {seconds: 30}
        };
    }



    // =============================== 对采集的数据全量抠图 结束 20250904 ===============================

    // =============================== Temu爬虫 开始 20250807 ===============================



    // 根据分类ID遍历产品,通过API访问,数据量有限 任务编排 20250807
    rpc CrawlGoodsListByOptId (CrawlGoodsListByOptIdRequest) returns (CrawlGoodsListByOptIdResponse) {
        option (temporal.v1.activity) = {
            heartbeat_timeout: {seconds: 60}
            start_to_close_timeout: {seconds: 864000}
        };
        option (temporal.v1.workflow) = {
            id: "${!id}/CrawlGoodsListByOptId"
            id_reuse_policy: WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE
            execution_timeout: {seconds: 864000}
        };
    }


    // =============================== Temu爬虫 结束 20250807 ===============================
}


