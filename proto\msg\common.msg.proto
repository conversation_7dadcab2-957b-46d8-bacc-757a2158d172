syntax = "proto3";

package msgpb;

import "buf/validate/validate.proto";

option go_package = "omnix/genpb/msgpb;msgpb";

// proto验证错误转换 20250808
message ValidateMessages {
    repeated ValidateMessage fields = 1;
}
message ValidateMessage {
    string field = 1;
    string message = 2;
}

// 20250413 分页参数
message PageSizeRequest {
    // 20250413 页码
    int32 page = 1 [
        (buf.validate.field).int32 = {
            gte: 0
        }
    ];
    // 20250413 每页数量
    int32 size = 2 [
        (buf.validate.field).int32 = {
            gte: 0
        }
    ];
}
