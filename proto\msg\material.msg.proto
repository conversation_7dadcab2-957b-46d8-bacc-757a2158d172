syntax = "proto3";

package msgpb;

import "google/protobuf/timestamp.proto";
option go_package = "omnix/genpb/msgpb;msgpb";

message MaterialGroup {
  // ID of the ent.
  int64 id = 1;
  // 创建时间
  google.protobuf.Timestamp created_time = 2;
  // 更新时间
  google.protobuf.Timestamp updated_time = 3;
  // Name holds the value of the "name" field.
  string name = 4;
}

message Material {
  // ID of the ent.
  int64 id = 1;
  // 创建时间
  google.protobuf.Timestamp created_time = 2;
  // 更新时间
  google.protobuf.Timestamp updated_time = 3;
  // 素材标题
  string title = 4;
  // 原始素材地址
  string source_url = 5;
  // 素材组
  repeated int64 material_group = 6;
  // 素材标记
  repeated string flag = 7;
  // 关联Temu采集
  int64 source_temu_id = 8;
  // 文件校验码
  string hash = 9;
  // 素材S3存储路径
  string path = 10;
}

message Administrator {
  // ID of the ent.
  int64 id = 1;
  // 创建时间
  google.protobuf.Timestamp created_time = 2;
  // 更新时间
  google.protobuf.Timestamp updated_time = 3;
  // 用户名
  string username = 4;
  // 密码
  string password = 5;
  // 状态
  string state = 6;
}


// 注册商标信息
message RegisteredTrademark {
    // ID of the ent.
    int64 id = 1;
    // 创建时间
    google.protobuf.Timestamp created_time = 2;
    // 更新时间
    google.protobuf.Timestamp updated_time = 3;
    // 商标ID
    int32 tid = 4;
    // 商标数据 (JSON格式)
    string data = 5;
}

