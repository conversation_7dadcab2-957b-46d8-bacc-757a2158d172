syntax = "proto3";

package msgpb;

import "google/protobuf/timestamp.proto";
option go_package = "omnix/genpb/msgpb;msgpb";

// 格式化的采集产品结构 20250806
message ProductSpec {
    // 产品id 20250806
    string id = 1;
    // 产品标题 20250806
    string title = 2;
    // 产品链接 20250806
    string link = 3;
    // 价格 20250809
    int64 price = 4;
    // 销量 20250806
    int64 sales = 5;
    // 店铺id 20250806
    string shop_id = 6;
    // temu分类ID 20250806
    repeated string temu_cats = 7;
    // 主图 20250809
    string featured_image = 8;
    // 图片列表 20250809
    repeated string image = 9;
    // 币种 20250809
    string currency = 10;
    string sku_id = 11;
}

// 产品过滤属性 20250806
message ProductFilter {
    // 材质 20250806
    repeated string material = 1;
    // 风格 20250806
    repeated string style = 2;
    // 场景 20250806
    repeated string scene = 3;
    // 标签 20250806
    repeated string tag = 4;
}

// 产品项数据结构,db1的原型
message ProductItem {
    // ID of the ent.
    int64 id = 1;
    // 创建时间
    google.protobuf.Timestamp created_time = 2;
    // 更新时间
    google.protobuf.Timestamp updated_time = 3;
    // 数据唯一
    string hash = 4;
    // 产品ID
    string item_id = 5;
    // 所属平台
    string platform = 6;
    // 格式化的产品数据
    msgpb.ProductSpec spec = 7;
    // 产品过滤属性
    msgpb.ProductFilter filter = 8;
    // 数据处理标记
    repeated string mark = 9;
}