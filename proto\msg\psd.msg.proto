syntax = "proto3";

package msgpb;

import "google/protobuf/timestamp.proto";
option go_package = "omnix/genpb/msgpb;msgpb";

// psd组
message PSDGroup {
  // ID
  int64 id = 1;
  // 组名
  string name = 2;
  // 描述
  string desc = 3;
  // 创建时间
  google.protobuf.Timestamp created_time = 4;
  // 更新时间
  google.protobuf.Timestamp updated_time = 5;
}

// psd 模板
message PSD {
  // ID
  int64 id = 1;
  // 创建时间
  google.protobuf.Timestamp created_time = 2;
  // 更新时间
  google.protobuf.Timestamp updated_time = 3;
  // 所属模型分组id
  repeated int64 psd_group_id = 4;
  // 模型描述
  string desc = 5;
  // PSD文件路径
  string file_path = 6;
  // PSD文件SHA1值
  string file_sha1 = 7;
  // 文件是否有效，0: 无效, 1: 有效
  int32 file_valid = 8;
  // 模型权重，数值越大优先级越高
  int32 weight = 9;
  // 是否为封面模型，0: 否, 1: 是
  int32 is_cover = 10;
}
