syntax = "proto2";


package msgpb;

import "buf/validate/validate.proto";


option go_package = "omnix/genpb/msgpb;msgpb";


extend buf.validate.StringRules {
    // 20250413 验证用户名
    optional bool validate_only_alpha_number = 98500001 [(buf.validate.predefined).cel = {
        id: "string.only_alpha_number",
        message: "only alphanumeric numbers are allowed"
        expression: "this.matches('^[a-zA-Z0-9]+$')"
    }];
    // 20250413 验证 handle
    optional bool validate_url_handle = 98500002 [(buf.validate.predefined).cel = {
        id: "string.url_handle",
        message: "only alphanumeric numbers and (- _) are allowed"
        expression: "this.matches('^[a-zA-Z0-9_-]+$')"
    }];

    // 20250413 验证集合id,所有通过xid生成的字符串id
    optional bool validate_xid = 98500003 [(buf.validate.predefined).cel = {
        id: "string.xid",
        message: "must be exactly 20 alphanumeric characters"
        expression: "this.matches('^[a-zA-Z0-9]{20}$')"
    }];
    // 20250413 验证shopify product id
    optional bool validate_shopify_product_id = 98500004 [(buf.validate.predefined).cel = {
        id: "string.shopify_product_id",
        message: "must be exactly 11-15 numeric characters"
        expression: "this.matches('^[0-9]{11,15}$')"
    }];
    // 20250423 验证变量key,格式 abc.cba.123
    optional bool validate_var_key = 98500005 [(buf.validate.predefined).cel = {
        id: "string.var_key",
        message: "only alphanumeric numbers and (.) are allowed, 0-60 length",
        expression: "this.matches('^[a-zA-Z0-9\\\\.]{0,60}$')"
    }];
    optional bool validate_sha1 = 98500006 [(buf.validate.predefined).cel = {
        id: "string.sha1",
        message: "Invalid SHA-1 hash"
        expression: "this.matches('^[a-zA-Z0-9]{40}$')"
    }];
    // 密码验证 20250726
    optional bool validate_password = 98500007 [(buf.validate.predefined).cel = {
        id: "string.password",
        message: "password must be between 5 and 40 characters",
        expression: "size(this) >= 5 && size(this) <= 40"
    }];
    // 邮箱/用户名验证 20250726
    optional bool validate_email = 98500008 [(buf.validate.predefined).cel = {
        id: "string.validate_email",
        message: "must be a valid email",
        expression: "this.isEmail() && size(this) >= 5 && size(this) <= 254"
    }];
    // 验证shopify customer id
    optional bool validate_shopify_customer_id = 98500009 [(buf.validate.predefined).cel = {
        id: "string.shopify_customer_id",
        message: "must be exactly 11-15 numeric characters"
        expression: "this.matches('^[0-9]{11,15}$')"
    }];
}