syntax = "proto3";

package partner;

import "common.msg.proto";
import "google/protobuf/timestamp.proto";
import "product.msg.proto";


option go_package = "omnix/genpb/partnerpb;partnerpb";

//查询Temu数据列表 请求
message QueryTemuListRequest {
    msgpb.PageSizeRequest page_size = 1;
    // 搜索标题 20250828
    string title = 2;
    // 过滤分类 20250829
    repeated string temu_cats = 3;
    
}
//查询Temu数据列表 响应
message QueryTemuListResponse {
    // 数据列表 20250828
    repeated Item items = 1;
    // 总数量 20250828
    int64 total = 2;

    // 列表项目 20250828
    message Item {
        // ID of the ent. 20250828
        int64 id = 1;
        // 创建时间 20250828
        google.protobuf.Timestamp created_time = 2;
        // 更新时间 20250828
        google.protobuf.Timestamp updated_time = 3;
        // 数据唯一 20250828
        string hash = 4;
        // 产品ID 20250828
        string item_id = 5;
        // 所属平台 20250828
        string platform = 6;
        // 格式化的产品数据 20250828
        msgpb.ProductSpec spec = 7;
        // 原始数据 20250828
        string data = 8;
    }
}
// Temu服务 20250827
service TemuService {
    // 查询Temu数据列表 接口 20250828
    rpc QueryTemuList (QueryTemuListRequest) returns (QueryTemuListResponse);


}