package b2

import (
    "omnix/state"
    "sync"

    "github.com/minio/minio-go/v7"
    "github.com/minio/minio-go/v7/pkg/credentials"
)

var (
    instance = &Holder{once: sync.Once{}}
)

type Holder struct {
    client *minio.Client
    once   sync.Once
}

func (r *Holder) R() *minio.Client {
    if r.client != nil {
        return r.client
    }
    r.once.Do(
        func() {
            var err error
            r.client, err = minio.New(
                state.RemoteOptions.Backblaze.Endpoint, &minio.Options{
                    Creds:  credentials.NewStaticV4(state.RemoteOptions.Backblaze.AccessID, state.RemoteOptions.Backblaze.AccessKey, ""),
                    Secure: true,
                },
            )
            if err != nil {
                panic(err)
            }
        },
    )
    return r.client
}
func Get() *Holder {
    return instance
}
func R() *minio.Client {
    return Get().R()
}
