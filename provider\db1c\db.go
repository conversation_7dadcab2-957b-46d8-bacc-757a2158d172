package db1c

import (
    "database/sql"
    "omnix/ddl/db1"
    "omnix/state"
    "sync"

    "entgo.io/ent/dialect"
    entsql "entgo.io/ent/dialect/sql"
    _ "github.com/jackc/pgx/v5/stdlib"
    "github.com/rs/zerolog/log"
    "github.com/stephenafamo/bob"
)

var (
    instance = &Holder{}
)

type Holder struct {
    client *db1.Client
    drv    *sql.DB
    once   sync.Once
}

func R() *db1.Client {
    return Get().R()
}

func (r *Holder) R() *db1.Client {
    if r.client != nil {
        return r.client
    }
    r.once.Do(
        func() {
            var err error
            r.drv, err = sql.Open(
                "pgx", state.RemoteOptions.Databases.Pgsql.GetDSN(),
            )
            if err != nil {
                log.Fatal().Err(err).Send()
            }
            r.drv.SetMaxIdleConns(20)
            r.drv.SetMaxOpenConns(300)
            // Create an db1.Driver from `db`.
            drv := entsql.OpenDB(dialect.Postgres, r.drv)
            r.client = db1.NewClient(db1.Driver(drv))
        },
    )
    return r.client
}
func Get() *Holder {
    return instance
}
func (r *Holder) GetDrv() *sql.DB {
    r.R()
    return r.drv
}

func (r *Holder) GetBobDB() bob.DB {
    r.R()
    return bob.NewDB(r.drv)
}
