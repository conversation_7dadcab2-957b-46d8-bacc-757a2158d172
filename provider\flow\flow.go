package flow

import (
    "log/slog"
    "omnix/state"
    "os"
    "sync"

    "github.com/rs/zerolog"
    "github.com/rs/zerolog/log"
    slogzerolog "github.com/samber/slog-zerolog/v2"
    "go.temporal.io/sdk/client"
)

var (
    instance = &Holder{once: sync.Once{}}
)

type Holder struct {
    client client.Client
    once   sync.Once
}

func (r *Holder) R() client.Client {
    if r.client != nil {
        return r.client
    }
    r.once.Do(
        func() {
            var err error
            zerologLogger := zerolog.New(zerolog.ConsoleWriter{Out: os.Stdout, NoColor: true, TimeFormat: "2006-01-02T15:04:05Z"})
            r.client, err = client.Dial(
                client.Options{
                    Namespace: state.RemoteOptions.Temporal.Namespace,
                    HostPort: state.RemoteOptions.Temporal.Host,
                    Logger:   slog.New(slogzerolog.Option{Level: slog.Level(state.RemoteOptions.Temporal.LogLevel), Logger: &zerologLogger}.NewZerologHandler()),
                },
            )
            if err != nil {
                log.Fatal().Err(err).Msg("工作流引擎连接失败")
            }
        },
    )
    return r.client
}
func Get() *Holder {
    return instance
}
func R() client.Client {
    return Get().R()
}
