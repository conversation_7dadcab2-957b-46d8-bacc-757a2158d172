package provider

import (
    "omnix/provider/b2"
    "omnix/provider/db1c"
    "omnix/provider/flow"
    "omnix/toolkit/wrapper/kitcomfyui"

    "github.com/google/wire"
)

// 各种单例带状态的服务提供者 20250722

// 全局服务注入配置 20250707
var GlobalProviderSet = wire.NewSet(
    // pgsql 20250818
    db1c.Get,
    // mongodb 20250818
    kitcomfyui.New,
    // 分布式系统 20250903
    flow.Get,
    // s3存储 20250903
    b2.Get,
)
