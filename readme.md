
### 安装项目工具
```go
go install github.com/go-task/task/v3/cmd/task@latest
```


### 目录结构
* /api/ 所有接口与业务代码实现
* /proto/ 所有api接口定义,由protobuf语言编写
* /genpb/ 所有接口生成的代码
* /toolkit 项目工具/助手
* /toolkit/wrapper 对第三方库包一层,方便调用
* /ports 项目接口定义
* /admin 后端管理前端项目



### 前端组件/UI库

https://github.com/vantezzen/autoform 快速创建表单
https://github.com/Seedsa/echo-editor 编辑器
https://github.com/JaleelB/emblor 标签输入组件
https://github.com/Aslam97/shadcn-minimal-tiptap mini编辑器

#### blocks
https://21st.dev/s/menu 开源大量
https://eo-n.vercel.app/docs/ui/menubar
https://www.hextaui.com/docs/ui/components/breadcrumb
https://bg.ibelick.com 现代化背景
https://www.kibo-ui.com/components/status 小众增强组件,尤其状态小圆点不错
https://kokonutui.com/docs/components/particle-button 小众增强组件,按钮特色
https://blocks.mvp-subha.me/docs/dashboards/admin 设计不错的成品组件,实现了大部分常用页面
https://originui.com 包含大量常用组件,每种都有10来种样式
https://www.armand-salle.fr/post/phone-input-shadcn-ui/ 手机区号输入组件

### 其他
https://ui.ewgenius.me/shadcn-radix-colors 定制主题