[log]
level = -1

# 监控配置
[watch]
#监控文件夹
include = ["."]
#.git .idea 系统默认忽略
exclude = ["tmp/", "release/", ".json", "try/main", "experiment", "proto/", ".ggv","apps/"]

[build]
#延迟,毫秒
delay = 3000
name = "./tmp/app.exe"
package = "./cmd/main"
command = ["-p", "20"]

#管理后台
[run.dashboard]
command = ["dashboard","-v=-1"]

#合作伙伴接口
[run.partner]
only = true
command = ["partner","-v=-1"]

#flow 默认工作队列
[run.worker_default]
only = true
command = ["flow","worker.default","-v=-1"]

#flow Comfyui工作队列
[run.worker_comfyui]
only = true
command = ["flow","worker.comfyui","-v=-1"]
