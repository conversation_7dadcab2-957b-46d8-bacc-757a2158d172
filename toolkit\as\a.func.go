package as

import (
	"encoding/json"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
)

func int32ToInt(input int32) int {
	return int(input)
}
func intToInt32(input int) int32 {
	return int32(input)
}
func uintToUint32(input uint) uint32 {
	return uint32(input)
}
func boolPToBool(input *bool) bool {
	if input == nil {
		return false
	}

	return *input
}
func rawMessageToString(input json.RawMessage) string {
	return string(input)
}

func timeToProto(time time.Time) *timestamppb.Timestamp {
	return timestamppb.New(time)
}
