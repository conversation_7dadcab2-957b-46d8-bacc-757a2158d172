//go:build goverter
// +build goverter

//
package as

import (
	"omnix/ddl/db1"
	"omnix/genpb/msgpb"
	"omnix/genpb/partnerpb"
)

// 结构体转换 struct converter 20250724

//go:generate goverter gen -g "ignoreUnexported" ./

// goverter:output:file ./gen.go
// goverter:converter
// goverter:extend uintToUint32
// goverter:extend intToInt32
// goverter:extend int32ToInt
// goverter:extend boolPToBool
// goverter:extend rawMessageToString
// goverter:extend timeToProto
// goverter:matchIgnoreCase
type common interface {
	RegisteredTrademark_MsgpbRegisteredTrademark(trademark *db1.RegisteredTrademark) *msgpb.RegisteredTrademark
	Material_MsgpbMaterial(item *db1.Material) *msgpb.Material
	MaterialGroup_MsgpbMaterialGroup(item *db1.MaterialGroup) *msgpb.MaterialGroup
	ProductItem_MsgpbProductItem(item *db1.ProductItem) *msgpb.ProductItem
	ProductItem_PartnerQueryTemuListResponse_Item(item *db1.ProductItem) *partnerpb.QueryTemuListResponse_Item
	PSDGroup_MsgPSDGroup(item *db1.PsdGroup) *msgpb.PSDGroup
	PSDp_MsgPSD(item *db1.Psd) *msgpb.PSD
}
