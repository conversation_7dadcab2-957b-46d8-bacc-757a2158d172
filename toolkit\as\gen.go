// Code generated by github.com/qwenode/goverter, DO NOT EDIT.
//go:build !goverter

package as

import (
	pq "github.com/lib/pq"
	db1 "omnix/ddl/db1"
	msgpb "omnix/genpb/msgpb"
	partnerpb "omnix/genpb/partnerpb"
)

type commonImpl struct{}

var CommonConvert = commonImpl{}

func (c *commonImpl) MaterialGroup_MsgpbMaterialGroup(source *db1.MaterialGroup) *msgpb.MaterialGroup {
	var pMsgpbMaterialGroup *msgpb.MaterialGroup
	if source != nil {
		var msgpbMaterialGroup msgpb.MaterialGroup
		msgpbMaterialGroup.Id = (*source).ID
		msgpbMaterialGroup.CreatedTime = timeToProto((*source).CreatedTime)
		msgpbMaterialGroup.UpdatedTime = timeToProto((*source).UpdatedTime)
		msgpbMaterialGroup.Name = (*source).Name
		pMsgpbMaterialGroup = &msgpbMaterialGroup
	}
	return pMsgpbMaterialGroup
}
func (c *commonImpl) Material_MsgpbMaterial(source *db1.Material) *msgpb.Material {
	var pMsgpbMaterial *msgpb.Material
	if source != nil {
		var msgpbMaterial msgpb.Material
		msgpbMaterial.Id = (*source).ID
		msgpbMaterial.CreatedTime = timeToProto((*source).CreatedTime)
		msgpbMaterial.UpdatedTime = timeToProto((*source).UpdatedTime)
		msgpbMaterial.Title = (*source).Title
		msgpbMaterial.SourceUrl = (*source).SourceURL
		msgpbMaterial.MaterialGroup = c.pqInt64ArrayToInt64List((*source).MaterialGroup)
		msgpbMaterial.Flag = c.pqStringArrayToStringList((*source).Flag)
		msgpbMaterial.SourceTemuId = (*source).SourceTemuID
		msgpbMaterial.Hash = (*source).Hash
		msgpbMaterial.Path = (*source).Path
		pMsgpbMaterial = &msgpbMaterial
	}
	return pMsgpbMaterial
}
func (c *commonImpl) PSDGroup_MsgPSDGroup(source *db1.PsdGroup) *msgpb.PSDGroup {
	var pMsgpbPSDGroup *msgpb.PSDGroup
	if source != nil {
		var msgpbPSDGroup msgpb.PSDGroup
		msgpbPSDGroup.Id = (*source).ID
		msgpbPSDGroup.Name = (*source).Name
		msgpbPSDGroup.Desc = (*source).Desc
		msgpbPSDGroup.CreatedTime = timeToProto((*source).CreatedTime)
		msgpbPSDGroup.UpdatedTime = timeToProto((*source).UpdatedTime)
		pMsgpbPSDGroup = &msgpbPSDGroup
	}
	return pMsgpbPSDGroup
}
func (c *commonImpl) PSDp_MsgPSD(source *db1.Psd) *msgpb.PSD {
	var pMsgpbPSD *msgpb.PSD
	if source != nil {
		var msgpbPSD msgpb.PSD
		msgpbPSD.Id = (*source).ID
		msgpbPSD.CreatedTime = timeToProto((*source).CreatedTime)
		msgpbPSD.UpdatedTime = timeToProto((*source).UpdatedTime)
		msgpbPSD.PsdGroupId = c.pqInt64ArrayToInt64List((*source).PsdGroupID)
		msgpbPSD.Desc = (*source).Desc
		msgpbPSD.FilePath = (*source).FilePath
		msgpbPSD.FileSha1 = (*source).FileSha1
		msgpbPSD.FileValid = (*source).FileValid
		msgpbPSD.Weight = (*source).Weight
		msgpbPSD.IsCover = (*source).IsCover
		pMsgpbPSD = &msgpbPSD
	}
	return pMsgpbPSD
}
func (c *commonImpl) ProductItem_MsgpbProductItem(source *db1.ProductItem) *msgpb.ProductItem {
	var pMsgpbProductItem *msgpb.ProductItem
	if source != nil {
		var msgpbProductItem msgpb.ProductItem
		msgpbProductItem.Id = (*source).ID
		msgpbProductItem.CreatedTime = timeToProto((*source).CreatedTime)
		msgpbProductItem.UpdatedTime = timeToProto((*source).UpdatedTime)
		msgpbProductItem.Hash = (*source).Hash
		msgpbProductItem.ItemId = (*source).ItemID
		msgpbProductItem.Platform = (*source).Platform
		msgpbProductItem.Spec = c.pMsgpbProductSpecToPMsgpbProductSpec((*source).Spec)
		msgpbProductItem.Filter = c.pMsgpbProductFilterToPMsgpbProductFilter((*source).Filter)
		if (*source).Mark != nil {
			msgpbProductItem.Mark = make([]string, len((*source).Mark))
			for i := 0; i < len((*source).Mark); i++ {
				msgpbProductItem.Mark[i] = (*source).Mark[i]
			}
		}
		pMsgpbProductItem = &msgpbProductItem
	}
	return pMsgpbProductItem
}
func (c *commonImpl) ProductItem_PartnerQueryTemuListResponse_Item(source *db1.ProductItem) *partnerpb.QueryTemuListResponse_Item {
	var pPartnerpbQueryTemuListResponse_Item *partnerpb.QueryTemuListResponse_Item
	if source != nil {
		var partnerpbQueryTemuListResponse_Item partnerpb.QueryTemuListResponse_Item
		partnerpbQueryTemuListResponse_Item.Id = (*source).ID
		partnerpbQueryTemuListResponse_Item.CreatedTime = timeToProto((*source).CreatedTime)
		partnerpbQueryTemuListResponse_Item.UpdatedTime = timeToProto((*source).UpdatedTime)
		partnerpbQueryTemuListResponse_Item.Hash = (*source).Hash
		partnerpbQueryTemuListResponse_Item.ItemId = (*source).ItemID
		partnerpbQueryTemuListResponse_Item.Platform = (*source).Platform
		partnerpbQueryTemuListResponse_Item.Spec = c.pMsgpbProductSpecToPMsgpbProductSpec((*source).Spec)
		partnerpbQueryTemuListResponse_Item.Data = (*source).Data
		pPartnerpbQueryTemuListResponse_Item = &partnerpbQueryTemuListResponse_Item
	}
	return pPartnerpbQueryTemuListResponse_Item
}
func (c *commonImpl) RegisteredTrademark_MsgpbRegisteredTrademark(source *db1.RegisteredTrademark) *msgpb.RegisteredTrademark {
	var pMsgpbRegisteredTrademark *msgpb.RegisteredTrademark
	if source != nil {
		var msgpbRegisteredTrademark msgpb.RegisteredTrademark
		msgpbRegisteredTrademark.Id = (*source).ID
		msgpbRegisteredTrademark.CreatedTime = timeToProto((*source).CreatedTime)
		msgpbRegisteredTrademark.UpdatedTime = timeToProto((*source).UpdatedTime)
		msgpbRegisteredTrademark.Tid = (*source).Tid
		msgpbRegisteredTrademark.Data = rawMessageToString((*source).Data)
		pMsgpbRegisteredTrademark = &msgpbRegisteredTrademark
	}
	return pMsgpbRegisteredTrademark
}
func (c *commonImpl) pMsgpbProductFilterToPMsgpbProductFilter(source *msgpb.ProductFilter) *msgpb.ProductFilter {
	var pMsgpbProductFilter *msgpb.ProductFilter
	if source != nil {
		var msgpbProductFilter msgpb.ProductFilter
		if (*source).Material != nil {
			msgpbProductFilter.Material = make([]string, len((*source).Material))
			for i := 0; i < len((*source).Material); i++ {
				msgpbProductFilter.Material[i] = (*source).Material[i]
			}
		}
		if (*source).Style != nil {
			msgpbProductFilter.Style = make([]string, len((*source).Style))
			for j := 0; j < len((*source).Style); j++ {
				msgpbProductFilter.Style[j] = (*source).Style[j]
			}
		}
		if (*source).Scene != nil {
			msgpbProductFilter.Scene = make([]string, len((*source).Scene))
			for k := 0; k < len((*source).Scene); k++ {
				msgpbProductFilter.Scene[k] = (*source).Scene[k]
			}
		}
		if (*source).Tag != nil {
			msgpbProductFilter.Tag = make([]string, len((*source).Tag))
			for l := 0; l < len((*source).Tag); l++ {
				msgpbProductFilter.Tag[l] = (*source).Tag[l]
			}
		}
		pMsgpbProductFilter = &msgpbProductFilter
	}
	return pMsgpbProductFilter
}
func (c *commonImpl) pMsgpbProductSpecToPMsgpbProductSpec(source *msgpb.ProductSpec) *msgpb.ProductSpec {
	var pMsgpbProductSpec *msgpb.ProductSpec
	if source != nil {
		var msgpbProductSpec msgpb.ProductSpec
		msgpbProductSpec.Id = (*source).Id
		msgpbProductSpec.Title = (*source).Title
		msgpbProductSpec.Link = (*source).Link
		msgpbProductSpec.Price = (*source).Price
		msgpbProductSpec.Sales = (*source).Sales
		msgpbProductSpec.ShopId = (*source).ShopId
		if (*source).TemuCats != nil {
			msgpbProductSpec.TemuCats = make([]string, len((*source).TemuCats))
			for i := 0; i < len((*source).TemuCats); i++ {
				msgpbProductSpec.TemuCats[i] = (*source).TemuCats[i]
			}
		}
		msgpbProductSpec.FeaturedImage = (*source).FeaturedImage
		if (*source).Image != nil {
			msgpbProductSpec.Image = make([]string, len((*source).Image))
			for j := 0; j < len((*source).Image); j++ {
				msgpbProductSpec.Image[j] = (*source).Image[j]
			}
		}
		msgpbProductSpec.Currency = (*source).Currency
		msgpbProductSpec.SkuId = (*source).SkuId
		pMsgpbProductSpec = &msgpbProductSpec
	}
	return pMsgpbProductSpec
}
func (c *commonImpl) pqInt64ArrayToInt64List(source pq.Int64Array) []int64 {
	var int64List []int64
	if source != nil {
		int64List = make([]int64, len(source))
		for i := 0; i < len(source); i++ {
			int64List[i] = source[i]
		}
	}
	return int64List
}
func (c *commonImpl) pqStringArrayToStringList(source pq.StringArray) []string {
	var stringList []string
	if source != nil {
		stringList = make([]string, len(source))
		for i := 0; i < len(source); i++ {
			stringList[i] = source[i]
		}
	}
	return stringList
}
