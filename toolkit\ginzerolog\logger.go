package ginzerolog

import (
    "omnix/toolkit/kitctx"
    "time"

    "github.com/gin-gonic/gin"
    "github.com/rs/zerolog"
    "github.com/rs/zerolog/log"
)

// 替换gin的日志输出
func DefaultZerologLogger() gin.HandlerFunc {
    return StructuredLogger(&log.Logger)
}

// StructuredLogger logs a gin HTTP request in JSON format. Allows to set the
// logger for testing purposes.
func StructuredLogger(logger *zerolog.Logger) gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()// Start timer
        path := c.Request.URL.Path
        raw := c.Request.URL.RawQuery
        
        // Process request
        c.Next()
        
        // Fill the params
        param := gin.LogFormatterParams{}
        
        param.TimeStamp = time.Now() // Stop timer
        param.Latency = param.TimeStamp.Sub(start)
        if param.Latency > time.Minute {
            param.Latency = param.Latency.Truncate(time.Second)
        }
        
        param.ClientIP = kitctx.GetClientIp(c)
        param.Method = c.Request.Method
        param.StatusCode = c.Writer.Status()
        param.ErrorMessage = c.Errors.ByType(gin.ErrorTypePrivate).String()
        param.BodySize = c.Writer.Size()
        if raw != "" {
            path = path + "?" + raw
        }
        param.Path = path
        
        // Log using the params
        var logEvent *zerolog.Event
        if c.Writer.Status() >= 500 {
            logEvent = logger.Error()
        } else {
            logEvent = logger.Info()
        }
        
        logEvent.Str("IP", param.ClientIP).
            Str("Method", param.Method).
            Int("Code", param.StatusCode).
            Int("Size", param.BodySize).
            Str("Path", param.Path).
            Str("Latency", param.Latency.String()).
            Msg(param.ErrorMessage)
    }
}
