package kitctx

import (
    "context"
    "errors"
    "net"
    "omnix/genpb/enumpb"
    "omnix/types"
    "strings"

    "connectrpc.com/connect"
    "github.com/gin-gonic/gin"
)

// =============================== 对connect封装 开始 20250808 ===============================

func NewInvalidArgument(message string) *connect.Error {
    return NewInvalidArgumentErr(errors.New(message))
}
func NewInvalidArgumentErr(err error) *connect.Error {
    return connect.NewError(connect.CodeInvalidArgument, err)
}

func NewNotFound(message string) *connect.Error {
    return NewNotFoundErr(errors.New(message))
}
func NewNotFoundErr(err error) *connect.Error {
    return connect.NewError(connect.CodeNotFound, err)
}

func NewAlreadyExists(message string) *connect.Error {
    return NewAlreadyExistsErr(errors.New(message))
}
func NewAlreadyExistsErr(err error) *connect.Error {
    return connect.NewError(connect.CodeAlreadyExists, err)
}

func NewPermissionDenied(message string) *connect.Error {
    return NewPermissionDeniedErr(errors.New(message))
}
func NewPermissionDeniedErr(err error) *connect.Error {
    return connect.NewError(connect.CodePermissionDenied, err)
}

func NewUnauthenticated(message string) *connect.Error {
    return NewUnauthenticatedErr(errors.New(message))
}
func NewUnauthenticatedErr(err error) *connect.Error {
    return connect.NewError(connect.CodeUnauthenticated, err)
}

func NewInternal(message string) *connect.Error {
    return NewInternalErr(errors.New(message))
}
func NewInternalErr(err error) *connect.Error {
    return connect.NewError(connect.CodeInternal, err)
}

func NewUnimplemented(message string) *connect.Error {
    return NewUnimplementedErr(errors.New(message))
}
func NewUnimplementedErr(err error) *connect.Error {
    return connect.NewError(connect.CodeUnimplemented, err)
}

func NewUnavailable(message string) *connect.Error {
    return NewUnavailableErr(errors.New(message))
}
func NewUnavailableErr(err error) *connect.Error {
    return connect.NewError(connect.CodeUnavailable, err)
}

func NewDeadlineExceeded(message string) *connect.Error {
    return NewDeadlineExceededErr(errors.New(message))
}
func NewDeadlineExceededErr(err error) *connect.Error {
    return connect.NewError(connect.CodeDeadlineExceeded, err)
}

func NewCanceled(message string) *connect.Error {
    return NewCanceledErr(errors.New(message))
}
func NewCanceledErr(err error) *connect.Error {
    return connect.NewError(connect.CodeCanceled, err)
}

func NewUnknown(message string) *connect.Error {
    return NewUnknownErr(errors.New(message))
}
func NewUnknownErr(err error) *connect.Error {
    return connect.NewError(connect.CodeUnknown, err)
}

func NewFailedPrecondition(message string) *connect.Error {
    return NewFailedPreconditionErr(errors.New(message))
}
func NewFailedPreconditionErr(err error) *connect.Error {
    return connect.NewError(connect.CodeFailedPrecondition, err)
}

func NewAborted(message string) *connect.Error {
    return NewAbortedErr(errors.New(message))
}
func NewAbortedErr(err error) *connect.Error {
    return connect.NewError(connect.CodeAborted, err)
}

func NewOutOfRange(message string) *connect.Error {
    return NewOutOfRangeErr(errors.New(message))
}
func NewOutOfRangeErr(err error) *connect.Error {
    return connect.NewError(connect.CodeOutOfRange, err)
}

func NewDataLoss(message string) *connect.Error {
    return NewDataLossErr(errors.New(message))
}
func NewDataLossErr(err error) *connect.Error {
    return connect.NewError(connect.CodeDataLoss, err)
}

func NewResourceExhausted(message string) *connect.Error {
    return NewResourceExhaustedErr(errors.New(message))
}
func NewResourceExhaustedErr(err error) *connect.Error {
    return connect.NewError(connect.CodeResourceExhausted, err)
}
// =============================== 对connect封装 结束 20250808 ===============================


// =============================== 对gin封装 开始 20250808 ===============================


// GetClientIp 获取客户端真实IP地址
// 优先级: Ali-Real-Client-Ip > CF-CONNECTING-IP > X-Forwarded-For > X-Real-IP > RemoteIP
// 如果CF和Ali的IP同时存在，说明都不合法，直接使用保底方案
func GetClientIp(ctx *gin.Context) string {
    cfIp := strings.TrimSpace(ctx.GetHeader("CF-CONNECTING-IP"))
    aliIp := strings.TrimSpace(ctx.GetHeader("Ali-Real-Client-Ip"))
    
    // 如果CF和Ali的IP同时存在，说明都不合法，跳过这两个来源
    if cfIp != "" && aliIp != "" {
        return getFallbackIP(ctx)
    }
    
    // 优先使用阿里云的真实IP
    if aliIp != "" && isValidIP(aliIp) {
        return aliIp
    }
    
    // 其次使用Cloudflare的真实IP
    if cfIp != "" && isValidIP(cfIp) {
        return cfIp
    }
    
    // 使用保底方案获取IP
    return getFallbackIP(ctx)
}

// getFallbackIP 保底方案获取客户端IP
func getFallbackIP(ctx *gin.Context) string {
    // 尝试从X-Forwarded-For获取（通常是代理链中的第一个IP）
    //if xForwardedFor := ctx.GetHeader("X-Forwarded-For"); xForwardedFor != "" {
    //   ips := strings.Split(xForwardedFor, ",")
    //   if len(ips) > 0 {
    //       firstIP := strings.TrimSpace(ips[0])
    //       if isValidIP(firstIP) && !isPrivateIP(firstIP) {
    //           return firstIP
    //       }
    //   }
    //}
    
    // 尝试从X-Real-IP获取（Nginx代理常用）
    //if e.V.Api.V1.Proxy.Nginx {
    //    if xRealIP := ctx.GetHeader("X-Real-IP"); xRealIP != "" {
    //        xRealIP = strings.TrimSpace(xRealIP)
    //        if isValidIP(xRealIP) {
    //            return xRealIP
    //        }
    //    }
    //}
    
    // 最后使用Gin的RemoteIP作为保底
    return ctx.RemoteIP()
}

// isValidIP 检查IP地址是否有效
func isValidIP(ip string) bool {
    return net.ParseIP(ip) != nil
}
func getClaims(c context.Context) (any, error) {
    value := c.Value(enumpb.CTX_VAR_JWT_CLAIMS.String())
    if value == nil {
        return nil,errors.New("claims is nil")
    }
    return value, nil
}
func GetAdminClains(c context.Context) (*types.JwtAdminClaims,error)   {
    value, err := getClaims(c)
    if err != nil {
        return nil,err
    }
    claims, ok := value.(*types.JwtAdminClaims)
    if !ok {
        return nil,errors.New("claims is not of type *types.JwtAdminClaims")
    }
    return claims,nil
}
// =============================== 对gin封装 结束 20250808 ===============================

// =============================== 常用工具 开始 20250828 ===============================



// =============================== 常用工具 结束 20250828 ===============================