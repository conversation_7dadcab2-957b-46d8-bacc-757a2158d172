package kitdb

import (
    "context"
    "database/sql"
    "fmt"
    "omnix/ddl/db1"
)

func WithTx(ctx context.Context, client *db1.Client, fn func(tx *db1.Tx) error) error {
    tx, err := client.BeginTx(ctx,&sql.TxOptions{})
    if err != nil {
        return err
    }
    defer func() {
        if v := recover(); v != nil {
            tx.Rollback()
            panic(v)
        }
    }()
    if err := fn(tx); err != nil {
        if rerr := tx.Rollback(); rerr != nil {
            err = fmt.Errorf("%w: rolling back transaction: %v", err, rerr)
        }
        return err
    }
    if err := tx.Commit(); err != nil {
        return fmt.Errorf("committing transaction: %w", err)
    }
    return nil
}
