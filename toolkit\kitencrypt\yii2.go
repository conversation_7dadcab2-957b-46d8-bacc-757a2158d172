package kitencrypt

import "golang.org/x/crypto/bcrypt"

// yii2 的密码加密
func GeneratePasswordHash(password string) (_hash string) {
    fromPassword, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
    return string(fromPassword)
}

// yii2 的密码校验
func ValidatePasswordHash(password, passwordHash string) bool {
    return bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte(password)) == nil
}

