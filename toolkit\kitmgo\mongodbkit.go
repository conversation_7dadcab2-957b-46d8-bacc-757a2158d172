package kitmgo

import (
    "errors"

    "go.mongodb.org/mongo-driver/v2/mongo"
)

func BulkCountDulicateKey(err error) int {
    count := 0
    // 情况 2：InsertMany / BulkWrite 错误
    var bwe mongo.BulkWriteException
    if errors.As(err, &bwe) {
        for _, we := range bwe.WriteErrors {
            if we.Code == 11000 || we.Code == 11001 || we.Code == 12582 {
                count++
            }
        }
    }
    return count
}
func IsDuplicateKey(err error) bool {
    // 情况 1：单条写入错误
    var e mongo.WriteException
    if errors.As(err, &e) {
        return e.HasErrorCode(11000) || e.HasErrorCode(11001) || e.<PERSON>r<PERSON>ode(12582)
    }

    // 情况 2：InsertMany / BulkWrite 错误
    var bwe mongo.BulkWriteException
    if errors.As(err, &bwe) {
        for _, we := range bwe.WriteErrors {
            if we.Code == 11000 || we.Code == 11001 || we.Code == 12582 {
                return true
            }
        }
    }

    // 情况 3：CommandError（如 createIndex 时冲突）
    var ce mongo.CommandError
    if errors.As(err, &ce) {
        return ce.HasErrorCode(11000)
    }

    return false
}
