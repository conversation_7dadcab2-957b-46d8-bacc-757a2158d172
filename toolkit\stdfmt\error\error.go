package stdfmterror

import (
    "omnix/genpb/msgpb"
    "omnix/ports"
    "strings"
)

var (
    // 将匹配到的错误转为业务错误,并设置固定错误内容 20250730
    // 如需保持原错误,则把值设置为空即可
    businessErrors = map[string]string{
        "Limit exceeded": "request limit exceeded. please try again later.",
    }
)

// 判断error是否是业务错误 20250730
func IsBusinessError[T ports.ValidateFaultMessage](err error, to T) bool {
    msg := err.Error()
    for businessError, hint := range businessErrors {
        if strings.Contains(msg, businessError) {
            if hint == "" {
                hint = msg
            }
            to.SetFaultMessage(&msgpb.FaultMessage{
                Halt: true,
                Hint: hint,
            })
            return true
        }
    }
    return false
}

// 判断流程是否停止执行并返回 20250730
func IsHalt[T ports.ValidateFaultMessage](response T) bool {
    // Check if response is nil by converting to interface{}
    if any(response) == nil {
        return false
    }
    faultMessage := response.GetFaultMessage()
    if faultMessage == nil {
        return false
    }
    return faultMessage.GetHalt()
}

// 停止执行后续流程并设置错误消息 20250730
func MustHaltAndHint[T ports.SetFaultMessage](response T, hint string) T {
    response.SetFaultMessage(&msgpb.FaultMessage{
        Hint: hint,
        Halt: true,
    })
    return response
}

// 判断是否有错误,如果有,将from错误设置到to 20250730
func CheckHaltAndTransfer[CHECK ports.ValidateFaultMessage, TO ports.ValidateFaultMessage](check CHECK, to TO) bool {
    message := check.GetFaultMessage()
    if message == nil {
        return false
    }
    if message.GetHalt() {
        to.SetFaultMessage(message)
        return true
    }
    return false
}
