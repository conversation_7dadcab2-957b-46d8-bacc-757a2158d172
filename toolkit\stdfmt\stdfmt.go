package stdfmt

import "omnix/ports"

func PageSizeLimit(input ports.PageSize, maxSize int) (_from int, _size int, _page int) {
    if input == nil {
        return 0, 10, 1
    }
    page := int(input.GetPage())
    size := int(input.GetSize())
    if page <= 0 {
        page = 1
    }
    if size <= 0 || size > maxSize {
        size = 10
    }
    return (page - 1) * size, size, page
}

func PageSize(input ports.PageSize) (_from int, _size int, _page int) {
    return PageSizeLimit(input, 5000)
}


