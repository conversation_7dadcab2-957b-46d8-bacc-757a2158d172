package kitb2

import (
    "bytes"
    "omnix/provider/b2"

    "github.com/minio/minio-go/v7"
)

import (
    "context"
    "errors"
    "io"
)

type IAdapter interface {
    Upload(b2Path string, b2Data []byte, size int64, contentType string) (_md5 string, _err error)
    VerifyMd5(b2Path, md5str string) bool
    Exist(b2Path string) bool
    Stat(b2Path string) (minio.ObjectInfo, error)
    UploadBuffer(path string, reader io.Reader, size int64, contentType string) (_md5 string, _err error)
    Remove(b2Path string) (_err error)
}
type adapter struct {
    c      context.Context
    b2h    *b2.Holder
    bucket string
}

// 20241122 上传文件,如果 contentType为空,则默认application/octet-stream by Node
func (r *adapter) Upload(b2Path string, b2Data []byte, size int64, contentType string) (_md5 string, _err error) {
    return r.UploadBuffer(b2Path, bytes.NewReader(b2Data), size, contentType)
}

// 20241206 校验文件MD5是否一致 by Node
func (r *adapter) VerifyMd5(b2Path, md5str string) bool {
    stat, err := r.Stat(b2Path)
    if err != nil {
        return false
    }
    return stat.ETag == md5str
}
func (r *adapter) Exist(b2Path string) bool {
    stat, err := r.Stat(b2Path)
    if err != nil {
        return false
    }
    return stat.Size > 0
}
func (r *adapter) Stat(b2Path string) (minio.ObjectInfo, error) {
    object, err := r.b2h.R().StatObject(r.c, r.bucket, b2Path, minio.StatObjectOptions{})
    return object, err
}
func (r *adapter) Remove(b2Path string) error {
    return r.b2h.R().RemoveObject(r.c, r.bucket, b2Path, minio.RemoveObjectOptions{})
}
func (r *adapter) UploadBuffer(
    path string, reader io.Reader, size int64, contentType string,
) (_md5 string, _err error) {
    if contentType == "" {
        contentType = "application/octet-stream"
    }
    info, err := r.b2h.R().PutObject(
        r.c, r.bucket, path, reader, size, minio.PutObjectOptions{ContentType: contentType, SendContentMd5: true},
    )
    if err != nil {
        return "", err
    }
    if info.ETag == "" {
        return "", errors.New("B2 ETag not set")
    }

    return info.ETag, nil
}

func New(c context.Context,b2h *b2.Holder, bucketName string) IAdapter {
    return &adapter{
        b2h:    b2h,
        c:      c,
        bucket: bucketName,
    }
}
