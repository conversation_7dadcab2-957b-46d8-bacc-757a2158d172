package kitcomfyui

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io"
    "mime/multipart"
    "net/http"
    "net/url"
    "os"
    "path/filepath"
    "strings"
    "time"
)

// Client represents the ComfyUI API client
type Client struct {
    baseURL    string
    httpClient *http.Client
}

// New creates a new ComfyUI API client
func New(baseURL string) *Client {
    // Ensure baseURL doesn't end with slash
    baseURL = strings.TrimRight(baseURL, "/")

    return &Client{
        baseURL: baseURL,
        httpClient: &http.Client{
            Timeout: 30 * time.Second,
        },
    }
}

// SetTimeout sets the HTTP client timeout
func (c *Client) SetTimeout(timeout time.Duration) {
    c.httpClient.Timeout = timeout
}

// UploadFile uploads a file to the ComfyUI upload directory
func (c *Client) UploadFile(filename string, fileContent io.Reader) (*UploadResponse, error) {
    // Create multipart form
    var buf bytes.Buffer
    writer := multipart.NewWriter(&buf)

    // Create form file field
    part, err := writer.CreateFormFile("file", filename)
    if err != nil {
        return nil, fmt.Errorf("failed to create form file: %w", err)
    }

    // Copy file content to form
    _, err = io.Copy(part, fileContent)
    if err != nil {
        return nil, fmt.Errorf("failed to copy file content: %w", err)
    }

    // Close writer to finalize the form
    err = writer.Close()
    if err != nil {
        return nil, fmt.Errorf("failed to close multipart writer: %w", err)
    }

    // Create request
    req, err := http.NewRequest("POST", c.baseURL+"/api/upload", &buf)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Set content type
    req.Header.Set("Content-Type", writer.FormDataContentType())
    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var uploadResp UploadResponse
    if err := json.Unmarshal(body, &uploadResp); err != nil {
        return nil, fmt.Errorf("failed to parse upload response: %w", err)
    }

    return &uploadResp, nil
}

// ListFiles lists all files in the ComfyUI output directory
func (c *Client) ListFiles() (*FileListResponse, error) {
    // Create request
    req, err := http.NewRequest("GET", c.baseURL+"/api/files", nil)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("list files failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var listResp FileListResponse
    if err := json.Unmarshal(body, &listResp); err != nil {
        return nil, fmt.Errorf("failed to parse list response: %w", err)
    }

    return &listResp, nil
}

// DownloadFile downloads a file from the ComfyUI output directory
func (c *Client) DownloadFile(filename string) (io.ReadCloser, error) {
    // URL encode the filename to handle special characters
    encodedFilename := url.PathEscape(filename)

    // Create request
    req, err := http.NewRequest("GET", c.baseURL+"/api/files/"+encodedFilename, nil)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        defer resp.Body.Close()
        body, err := io.ReadAll(resp.Body)
        if err != nil {
            return nil, fmt.Errorf("download failed with status %d", resp.StatusCode)
        }

        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("download failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Return response body as ReadCloser for streaming
    return resp.Body, nil
}

// DownloadFileToBytes downloads a file and returns its content as bytes
func (c *Client) DownloadFileToBytes(filename string) ([]byte, error) {
    reader, err := c.DownloadFile(filename)
    if err != nil {
        return nil, err
    }
    defer reader.Close()

    return io.ReadAll(reader)
}

// DeleteFile deletes a file from the ComfyUI output directory
func (c *Client) DeleteFile(filename string) (*DeleteResponse, error) {
    // URL encode the filename to handle special characters
    encodedFilename := url.PathEscape(filename)

    // Create request
    req, err := http.NewRequest("DELETE", c.baseURL+"/api/files/"+encodedFilename, nil)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("delete failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var deleteResp DeleteResponse
    if err := json.Unmarshal(body, &deleteResp); err != nil {
        return nil, fmt.Errorf("failed to parse delete response: %w", err)
    }

    return &deleteResp, nil
}

// DeleteUploadFile deletes a file from the ComfyUI upload directory
func (c *Client) DeleteUploadFile(filename string) (*DeleteResponse, error) {
    // URL encode the filename to handle special characters
    encodedFilename := url.PathEscape(filename)

    // Create request to delete from upload directory
    req, err := http.NewRequest("DELETE", c.baseURL+"/api/upload/"+encodedFilename, nil)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("delete upload file failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var deleteResp DeleteResponse
    if err := json.Unmarshal(body, &deleteResp); err != nil {
        return nil, fmt.Errorf("failed to parse delete response: %w", err)
    }

    return &deleteResp, nil
}

// ProxyRequest sends a request to ComfyUI through the adapter proxy
func (c *Client) ProxyRequest(method, path string, body io.Reader, headers map[string]string) (*http.Response, error) {
    // Ensure path starts with /
    if !strings.HasPrefix(path, "/") {
        path = "/" + path
    }

    // Create request URL
    requestURL := c.baseURL + "/api/adapter" + path

    // Create request
    req, err := http.NewRequest(method, requestURL, body)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Add custom headers
    for key, value := range headers {
        req.Header.Set(key, value)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute proxy request: %w", err)
    }

    return resp, nil
}

// ProxyGet sends a GET request to ComfyUI through the adapter proxy
func (c *Client) ProxyGet(path string, headers map[string]string) (*http.Response, error) {
    return c.ProxyRequest("GET", path, nil, headers)
}

// ProxyPost sends a POST request to ComfyUI through the adapter proxy
func (c *Client) ProxyPost(path string, body io.Reader, headers map[string]string) (*http.Response, error) {
    return c.ProxyRequest("POST", path, body, headers)
}

// ProxyPostJSON sends a POST request with JSON body to ComfyUI through the adapter proxy
func (c *Client) ProxyPostJSON(path string, data any, headers map[string]string) (*http.Response, error) {
    var jsonData []byte
    var err error

    // Check if data is already a JSON string
    if str, ok := data.(string); ok {
        jsonData = []byte(str)
    } else {
        // Marshal the data to JSON
        jsonData, err = json.Marshal(data)
        if err != nil {
            return nil, fmt.Errorf("failed to marshal JSON: %w", err)
        }
    }

    if headers == nil {
        headers = make(map[string]string)
    }
    headers["Content-Type"] = "application/json"

    return c.ProxyPost(path, bytes.NewReader(jsonData), headers)
}

// UploadFileFromPath uploads a file from a local file path
func (c *Client) UploadFileFromPath(filePath string) (*UploadResponse, error) {
    // Open file
    file, err := os.Open(filePath)
    if err != nil {
        return nil, fmt.Errorf("failed to open file: %w", err)
    }
    defer file.Close()

    // Get filename from path
    filename := filepath.Base(filePath)

    return c.UploadFile(filename, file)
}

// Ping checks if the ComfyUI API service is available
func (c *Client) Ping() error {
    req, err := http.NewRequest("GET", c.baseURL+"/ping", nil)
    if err != nil {
        return fmt.Errorf("failed to create ping request: %w", err)
    }

    resp, err := c.httpClient.Do(req)
    if err != nil {
        return fmt.Errorf("failed to ping service: %w", err)
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("ping failed with status %d", resp.StatusCode)
    }

    return nil
}

// GetBaseURL returns the base URL of the client
func (c *Client) GetBaseURL() string {
    return c.baseURL
}

// SetBaseURL updates the base URL of the client
func (c *Client) SetBaseURL(baseURL string) {
    c.baseURL = strings.TrimRight(baseURL, "/")
}

// GET /api/queue 20250901
func (c *Client) QueryQueueList() (_body string, _err error) {
    response, err := c.ProxyGet("/api/queue", nil)
    if err != nil {
        return "", err
    }
    defer response.Body.Close()
    body, err := io.ReadAll(response.Body)
    if err != nil {
        return "", err
    }
    return string(body), nil
}
func (c *Client) GetHistoryList() (_body string, _err error) {
    response, err := c.ProxyGet("/api/history?max_items=10", nil)
    if err != nil {
        return "", err
    }
    defer response.Body.Close()
    body, err := io.ReadAll(response.Body)
    if err != nil {
        return "", err
    }
    return string(body), nil
}

// 检测任务是否在执行或队列中 20250903
func (c *Client) IsTaskInQueue(promptId string) (bool, error) {
    body, err := c.QueryQueueList()
    if err != nil {
        return false, err
    }
    if strings.Contains(body, promptId) {
        return true, nil
    }
    return false, nil
}

// 查询工作流列表 http://127.0.0.1:8188/api/userdata?dir=workflows&recurse=true&split=false&full_info=true
func (c *Client) QueryWorkflows() (_details []WorkflowDetail, _err error) {
    response, err := c.ProxyGet("/api/userdata?dir=workflows&recurse=true&split=false&full_info=true", nil)
    if err != nil {
        return nil, err
    }
    defer response.Body.Close()
    body, err := io.ReadAll(response.Body)
    if err != nil {
        return nil, err
    }
    details := make([]WorkflowDetail, 0)
    if err := json.Unmarshal(body, &details); err != nil {
        return nil, err
    }

    return details, nil
}

// 检查工作流是否存在 20250902
func (c *Client) HasWorkflow(name string) (bool, error) {
    details, err := c.QueryWorkflows()
    if err != nil {
        return false, err
    }
    for _, detail := range details {
        if strings.Contains(strings.ToLower(detail.Path), strings.ToLower(name)) {
            return true, nil
        }
    }
    return false, nil
}

// =============================================================================
// Photoshop API Methods
// =============================================================================

// PersistDir operations (upload, list, download, delete)

// PSUploadToPersist uploads a file to the Photoshop PersistDir
func (c *Client) PSUploadToPersist(filename string, content io.Reader) (*UploadResponse, error) {
    return c.uploadToPhotoshopDir("persist", filename, content)
}

// PSUploadToPersistFromPath uploads a file from a local path to the Photoshop PersistDir
func (c *Client) PSUploadToPersistFromPath(filePath string) (*UploadResponse, error) {
    // Open file
    file, err := os.Open(filePath)
    if err != nil {
        return nil, fmt.Errorf("failed to open file: %w", err)
    }
    defer file.Close()

    // Get filename from path
    filename := filepath.Base(filePath)

    return c.PSUploadToPersist(filename, file)
}

// PSListPersistFiles lists all files in the Photoshop PersistDir
func (c *Client) PSListPersistFiles() (*FileListResponse, error) {
    return c.listPhotoshopFiles("persist")
}

// PSDownloadFromPersist downloads a file from the Photoshop PersistDir
func (c *Client) PSDownloadFromPersist(filename string) (io.ReadCloser, error) {
    return c.downloadFromPhotoshopDir("persist", filename)
}

// PSDownloadFromPersistToBytes downloads a file from PersistDir and returns its content as bytes
func (c *Client) PSDownloadFromPersistToBytes(filename string) ([]byte, error) {
    reader, err := c.PSDownloadFromPersist(filename)
    if err != nil {
        return nil, err
    }
    defer reader.Close()

    return io.ReadAll(reader)
}

// PSDeleteFromPersist deletes a file from the Photoshop PersistDir
func (c *Client) PSDeleteFromPersist(filename string) (*DeleteResponse, error) {
    return c.deleteFromPhotoshopDir("persist", filename)
}

// UploadDir operations (upload, list, download, delete)

// PSUploadToUpload uploads a file to the Photoshop UploadDir
func (c *Client) PSUploadToUpload(filename string, content io.Reader) (*UploadResponse, error) {
    return c.uploadToPhotoshopDir("upload", filename, content)
}

// PSUploadToUploadFromPath uploads a file from a local path to the Photoshop UploadDir
func (c *Client) PSUploadToUploadFromPath(filePath string) (*UploadResponse, error) {
    // Open file
    file, err := os.Open(filePath)
    if err != nil {
        return nil, fmt.Errorf("failed to open file: %w", err)
    }
    defer file.Close()

    // Get filename from path
    filename := filepath.Base(filePath)

    return c.PSUploadToUpload(filename, file)
}

// PSListUploadFiles lists all files in the Photoshop UploadDir
func (c *Client) PSListUploadFiles() (*FileListResponse, error) {
    return c.listPhotoshopFiles("upload")
}

// PSDownloadFromUpload downloads a file from the Photoshop UploadDir
func (c *Client) PSDownloadFromUpload(filename string) (io.ReadCloser, error) {
    return c.downloadFromPhotoshopDir("upload", filename)
}

// PSDownloadFromUploadToBytes downloads a file from UploadDir and returns its content as bytes
func (c *Client) PSDownloadFromUploadToBytes(filename string) ([]byte, error) {
    reader, err := c.PSDownloadFromUpload(filename)
    if err != nil {
        return nil, err
    }
    defer reader.Close()

    return io.ReadAll(reader)
}

// PSDeleteFromUpload deletes a file from the Photoshop UploadDir
func (c *Client) PSDeleteFromUpload(filename string) (*DeleteResponse, error) {
    return c.deleteFromPhotoshopDir("upload", filename)
}

// OutputDir operations (list, download, delete - read-only, no upload)

// PSListOutputFiles lists all files in the Photoshop OutputDir
func (c *Client) PSListOutputFiles() (*FileListResponse, error) {
    return c.listPhotoshopFiles("output")
}

// PSDownloadFromOutput downloads a file from the Photoshop OutputDir
func (c *Client) PSDownloadFromOutput(filename string) (io.ReadCloser, error) {
    return c.downloadFromPhotoshopDir("output", filename)
}

// PSDownloadFromOutputToBytes downloads a file from OutputDir and returns its content as bytes
func (c *Client) PSDownloadFromOutputToBytes(filename string) ([]byte, error) {
    reader, err := c.PSDownloadFromOutput(filename)
    if err != nil {
        return nil, err
    }
    defer reader.Close()

    return io.ReadAll(reader)
}

// PSDeleteFromOutput deletes a file from the Photoshop OutputDir
func (c *Client) PSDeleteFromOutput(filename string) (*DeleteResponse, error) {
    return c.deleteFromPhotoshopDir("output", filename)
}

// =============================================================================
// Helper methods for Photoshop operations
// =============================================================================

// uploadToPhotoshopDir uploads a file to a specific Photoshop directory
func (c *Client) uploadToPhotoshopDir(dirType, filename string, content io.Reader) (*UploadResponse, error) {
    // Create multipart form
    var buf bytes.Buffer
    writer := multipart.NewWriter(&buf)

    // Create form file field
    part, err := writer.CreateFormFile("file", filename)
    if err != nil {
        return nil, fmt.Errorf("failed to create form file: %w", err)
    }

    // Copy file content to form
    _, err = io.Copy(part, content)
    if err != nil {
        return nil, fmt.Errorf("failed to copy file content: %w", err)
    }

    // Close writer to finalize the form
    err = writer.Close()
    if err != nil {
        return nil, fmt.Errorf("failed to close multipart writer: %w", err)
    }

    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/%s/upload", c.baseURL, dirType)

    // Create request
    req, err := http.NewRequest("POST", requestURL, &buf)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Set content type
    req.Header.Set("Content-Type", writer.FormDataContentType())

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("upload to %s failed with status %d: %s", dirType, resp.StatusCode, string(body))
    }

    // Parse success response
    var uploadResp UploadResponse
    if err := json.Unmarshal(body, &uploadResp); err != nil {
        return nil, fmt.Errorf("failed to parse upload response: %w", err)
    }

    return &uploadResp, nil
}

// listPhotoshopFiles lists files in a specific Photoshop directory
func (c *Client) listPhotoshopFiles(dirType string) (*FileListResponse, error) {
    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/%s/files", c.baseURL, dirType)

    // Create request
    req, err := http.NewRequest("GET", requestURL, nil)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("list %s files failed with status %d: %s", dirType, resp.StatusCode, string(body))
    }

    // Parse success response
    var listResp FileListResponse
    if err := json.Unmarshal(body, &listResp); err != nil {
        return nil, fmt.Errorf("failed to parse list response: %w", err)
    }

    return &listResp, nil
}

// downloadFromPhotoshopDir downloads a file from a specific Photoshop directory
func (c *Client) downloadFromPhotoshopDir(dirType, filename string) (io.ReadCloser, error) {
    // URL encode the filename to handle special characters
    encodedFilename := url.PathEscape(filename)

    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/%s/files/%s", c.baseURL, dirType, encodedFilename)

    // Create request
    req, err := http.NewRequest("GET", requestURL, nil)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        defer resp.Body.Close()
        body, err := io.ReadAll(resp.Body)
        if err != nil {
            return nil, fmt.Errorf("download from %s failed with status %d", dirType, resp.StatusCode)
        }

        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("download from %s failed with status %d: %s", dirType, resp.StatusCode, string(body))
    }

    // Return response body as ReadCloser for streaming
    return resp.Body, nil
}

// deleteFromPhotoshopDir deletes a file from a specific Photoshop directory
func (c *Client) deleteFromPhotoshopDir(dirType, filename string) (*DeleteResponse, error) {
    // URL encode the filename to handle special characters
    encodedFilename := url.PathEscape(filename)

    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/%s/files/%s", c.baseURL, dirType, encodedFilename)

    // Create request
    req, err := http.NewRequest("DELETE", requestURL, nil)
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("delete from %s failed with status %d: %s", dirType, resp.StatusCode, string(body))
    }

    // Parse success response
    var deleteResp DeleteResponse
    if err := json.Unmarshal(body, &deleteResp); err != nil {
        return nil, fmt.Errorf("failed to parse delete response: %w", err)
    }

    return &deleteResp, nil
}

// PSRemoveWhiteBackground removes white background from an image using Photoshop automation
func (c *Client) PSRemoveWhiteBackground(filename string) (*RemoveWhiteBackgroundResponse, error) {
    // Create request body
    reqBody := RemoveWhiteBackgroundRequest{
        Filename: filename,
    }

    // Marshal request to JSON
    jsonData, err := json.Marshal(reqBody)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal request: %w", err)
    }

    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/remove-white-background", c.baseURL)

    // Create request
    req, err := http.NewRequest("POST", requestURL, bytes.NewReader(jsonData))
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Set content type
    req.Header.Set("Content-Type", "application/json")

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("remove white background failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var removeResp RemoveWhiteBackgroundResponse
    if err := json.Unmarshal(body, &removeResp); err != nil {
        return nil, fmt.Errorf("failed to parse remove white background response: %w", err)
    }

    return &removeResp, nil
}

// PSCheckPersistFile checks if a file exists in persist directory and validates its SHA1
func (c *Client) PSCheckPersistFile(filename, sha1 string) (*CheckFileResponse, error) {
    // Create request body
    reqBody := CheckFileRequest{
        Filename: filename,
        Sha1:     sha1,
    }

    // Marshal request to JSON
    jsonData, err := json.Marshal(reqBody)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal request: %w", err)
    }

    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/persist/check", c.baseURL)

    // Create request
    req, err := http.NewRequest("POST", requestURL, bytes.NewReader(jsonData))
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Set content type
    req.Header.Set("Content-Type", "application/json")

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("check persist file failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var checkResp CheckFileResponse
    if err := json.Unmarshal(body, &checkResp); err != nil {
        return nil, fmt.Errorf("failed to parse check file response: %w", err)
    }

    return &checkResp, nil
}

// PSCombine combines PSD with material using Photoshop automation
func (c *Client) PSCombine(psdFilename, materialFilename string) (*CombineResponse, error) {
    // Create request body
    reqBody := CombineRequest{
        PsdFilename:      psdFilename,
        MaterialFilename: materialFilename,
    }

    // Marshal request to JSON
    jsonData, err := json.Marshal(reqBody)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal request: %w", err)
    }

    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/combine", c.baseURL)

    // Create request
    req, err := http.NewRequest("POST", requestURL, bytes.NewReader(jsonData))
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Set content type
    req.Header.Set("Content-Type", "application/json")

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("combine failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var combineResp CombineResponse
    if err := json.Unmarshal(body, &combineResp); err != nil {
        return nil, fmt.Errorf("failed to parse combine response: %w", err)
    }

    return &combineResp, nil
}

// PSCopyOutputToUpload copies a file from output directory to upload directory
func (c *Client) PSCopyOutputToUpload(filename string) (*CopyOutputToUploadResponse, error) {
    // Create request body
    reqBody := CopyOutputToUploadRequest{
        Filename: filename,
    }

    // Marshal request to JSON
    jsonData, err := json.Marshal(reqBody)
    if err != nil {
        return nil, fmt.Errorf("failed to marshal request: %w", err)
    }

    // Create request URL
    requestURL := fmt.Sprintf("%s/ps/copy-to-upload", c.baseURL)

    // Create request
    req, err := http.NewRequest("POST", requestURL, bytes.NewReader(jsonData))
    if err != nil {
        return nil, fmt.Errorf("failed to create request: %w", err)
    }

    // Set content type
    req.Header.Set("Content-Type", "application/json")

    // Execute request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return nil, fmt.Errorf("failed to execute request: %w", err)
    }
    defer resp.Body.Close()

    // Read response body
    body, err := io.ReadAll(resp.Body)
    if err != nil {
        return nil, fmt.Errorf("failed to read response body: %w", err)
    }

    // Check for error response
    if resp.StatusCode >= 400 {
        var errResp ErrorResponse
        if err := json.Unmarshal(body, &errResp); err == nil {
            return nil, errResp
        }
        return nil, fmt.Errorf("copy output to upload failed with status %d: %s", resp.StatusCode, string(body))
    }

    // Parse success response
    var copyResp CopyOutputToUploadResponse
    if err := json.Unmarshal(body, &copyResp); err != nil {
        return nil, fmt.Errorf("failed to parse copy response: %w", err)
    }

    return &copyResp, nil
}
