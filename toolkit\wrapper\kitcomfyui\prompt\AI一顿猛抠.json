{"233": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "加载VAE"}}, "234": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "双CLIP加载器"}}, "237": {"inputs": {"pixels": ["279", 0], "vae": ["233", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE编码"}}, "238": {"inputs": {"conditioning": ["281", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "条件零化"}}, "239": {"inputs": {"conditioning": ["281", 0], "latent": ["237", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "240": {"inputs": {"guidance": 5, "conditioning": ["239", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "Flux引导"}}, "241": {"inputs": {"seed": 1042507374406481, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["272", 0], "positive": ["240", 0], "negative": ["238", 0], "latent_image": ["246", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K采样器"}}, "242": {"inputs": {"samples": ["241", 0], "vae": ["233", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE解码"}}, "246": {"inputs": {"width": 1024, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "空Latent图像（SD3）"}}, "272": {"inputs": {"lora_name": "FLUX.1-Turbo-Alpha-alimama-creative.safetensors", "lora_strength": 1, "model": ["273", 0]}, "class_type": "NunchakuFluxLoraLoader", "_meta": {"title": "Nunchaku FLUX LoRA Loader"}}, "273": {"inputs": {"model_path": "svdq-int4_r32-flux.1-kontext-dev.safetensors", "cache_threshold": 0, "attention": "nunchaku-fp16", "cpu_offload": "auto", "device_id": 0, "data_type": "bfloat16", "i2f_mode": "enabled"}, "class_type": "NunchakuFluxDiTLoader", "_meta": {"title": "Nunchaku FLUX DiT Loader"}}, "279": {"inputs": {"image": "{{KIT_INPUT}}"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "281": {"inputs": {"text": "{{KIT_PROMPT}}", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["234", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP文本编码"}}, "286": {"inputs": {"filename_prefix": "<PERSON><PERSON><PERSON><PERSON>", "images": ["242", 0]}, "class_type": "SaveImage", "_meta": {"title": "保存图像"}}}