package kitcomfyuiprompt

import (
    "embed"
    "strings"
)

//go:embed *.json
var Prompts embed.FS


const (
   AI一顿猛抠 = "AI一顿猛抠"
)

// 读取配置 20250903
func ReadPrompt(name string,args map[string]string) (string,error) {
    if !strings.HasPrefix(name, ".json") {
        name+=".json"
    }
    file, err := Prompts.ReadFile(name)
    if err != nil {
        return "", err
    }
    body := string(file)
    for k, v := range args {
        body = strings.ReplaceAll(body, k, v)
    }
    return body, nil
}
// 抠 20250903
func GenAI一顿猛抠Prompt(fileName string) (string, error) {
    prompt, err := ReadPrompt(AI一顿猛抠, map[string]string{
        "{{KIT_PROMPT}}": "Extract the first wall decal in its original form,against a clean pure white background,keep wall decal Intact complete",
        "{{KIT_INPUT}}":  fileName,
    })
    if err != nil {
        return "", err
    }
    return prompt, nil
}