package kitcomfyui

import (
    "fmt"
    "time"
)

// FileInfo represents metadata about a file
type FileInfo struct {
    Name        string    `json:"name"`
    Size        int64     `json:"size"`
    ModTime     time.Time `json:"mod_time"`
    IsDirectory bool      `json:"is_directory"`
}

// FileListResponse represents the response for file listing
type FileListResponse struct {
    Files []FileInfo `json:"files"`
    Count int        `json:"count"`
}

// UploadResponse represents the response for file upload
type UploadResponse struct {
    Success  bool   `json:"success"`
    Filename string `json:"filename"`
    Size     int64  `json:"size"`
    Path     string `json:"path"`
    Message  string `json:"message,omitempty"`
}

// DeleteResponse represents the response for file deletion
type DeleteResponse struct {
    Success  bool   `json:"success"`
    Message  string `json:"message"`
    Filename string `json:"filename"`
}

// ErrorResponse represents a standardized error response
type ErrorResponse struct {
    ErrorType string `json:"error"`
    Code      int    `json:"code"`
    Message   string `json:"message"`
}

// 工作流详情 20250902
type WorkflowDetail struct {
    Path     string  `json:"path"`
    Size     int     `json:"size"`
    Modified float64 `json:"modified"`
}

// Error implements the error interface for ErrorResponse
func (e ErrorResponse) Error() string {
    return fmt.Sprintf("ComfyUI API Error [%d]: %s - %s", e.Code, e.ErrorType, e.Message)
}

// Common error types for consistent error handling
const (
    ErrorTypeValidation         = "validation_error"
    ErrorTypeNotFound           = "not_found"
    ErrorTypePermission         = "permission_denied"
    ErrorTypeConfiguration      = "config_error"
    ErrorTypeFileSystem         = "filesystem_error"
    ErrorTypeNetwork            = "network_error"
    ErrorTypeProxy              = "proxy_error"
    ErrorTypeSecurity           = "security_violation"
    ErrorTypeTimeout            = "timeout_error"
    ErrorTypeServiceUnavailable = "service_unavailable"
)

// Helper function to create consistent error responses
func NewErrorResponse(code int, errorType, message string) ErrorResponse {
    return ErrorResponse{
        ErrorType: errorType,
        Code:      code,
        Message:   message,
    }
}

// NewValidationError creates a validation error response
func NewValidationError(message string) ErrorResponse {
    return NewErrorResponse(400, ErrorTypeValidation, message)
}

// NewNotFoundError creates a not found error response
func NewNotFoundError(resource string) ErrorResponse {
    return NewErrorResponse(404, ErrorTypeNotFound, "The requested "+resource+" was not found")
}

// NewPermissionError creates a permission denied error response
func NewPermissionError(operation string) ErrorResponse {
    return NewErrorResponse(403, ErrorTypePermission, "Permission denied: "+operation)
}

// NewConfigurationError creates a configuration error response
func NewConfigurationError(message string) ErrorResponse {
    return NewErrorResponse(500, ErrorTypeConfiguration, "Configuration error: "+message)
}

// NewFileSystemError creates a filesystem error response
func NewFileSystemError(operation string) ErrorResponse {
    return NewErrorResponse(500, ErrorTypeFileSystem, "Filesystem error: "+operation)
}

// NewNetworkError creates a network error response
func NewNetworkError(message string) ErrorResponse {
    return NewErrorResponse(502, ErrorTypeNetwork, "Network error: "+message)
}

// NewProxyError creates a proxy error response
func NewProxyError(message string) ErrorResponse {
    return NewErrorResponse(502, ErrorTypeProxy, "Proxy error: "+message)
}

// NewSecurityError creates a security violation error response
func NewSecurityError(message string) ErrorResponse {
    return NewErrorResponse(400, ErrorTypeSecurity, "Security violation: "+message)
}

// NewTimeoutError creates a timeout error response
func NewTimeoutError(operation string) ErrorResponse {
    return NewErrorResponse(504, ErrorTypeTimeout, "Timeout: "+operation)
}

// NewServiceUnavailableError creates a service unavailable error response
func NewServiceUnavailableError(service string) ErrorResponse {
    return NewErrorResponse(503, ErrorTypeServiceUnavailable, service+" service is unavailable")
}

// RemoveWhiteBackgroundRequest represents the request for removing white background from an image
type RemoveWhiteBackgroundRequest struct {
    Filename string `json:"filename"`
}

// RemoveWhiteBackgroundResponse represents the response for white background removal operation
type RemoveWhiteBackgroundResponse struct {
    Success  bool   `json:"success"`
    Filename string `json:"filename"`
    Log      string `json:"log"`
    Message  string `json:"message,omitempty"`
}

// CombineRequest represents the request for combining PSD with material using Photoshop automation
type CombineRequest struct {
    PsdFilename      string `json:"psd_filename"`
    MaterialFilename string `json:"material_filename"`
}

// CombineResponse represents the response for combine operation
type CombineResponse struct {
    Success  bool   `json:"success"`
    Filename string `json:"filename"`
    Log      string `json:"log"`
    Message  string `json:"message,omitempty"`
}

// CheckFileRequest represents the request for checking file existence and SHA1
type CheckFileRequest struct {
    Filename string `json:"filename"`
    Sha1     string `json:"sha1"`
}

// CheckFileResponse represents the response for file check operation
type CheckFileResponse struct {
    Success      bool   `json:"success"`
    Valid        bool   `json:"valid"`
    FileExists   bool   `json:"file_exists"`
    Sha1Match    bool   `json:"sha1_match"`
    ActualSha1   string `json:"actual_sha1,omitempty"`
    ExpectedSha1 string `json:"expected_sha1,omitempty"`
    Message      string `json:"message,omitempty"`
}

// CopyOutputToUploadRequest represents the request for copying file from output to upload directory
type CopyOutputToUploadRequest struct {
    Filename string `json:"filename" binding:"required"`
}

// CopyOutputToUploadResponse represents the response for copy operation
type CopyOutputToUploadResponse struct {
    Success    bool   `json:"success"`
    Message    string `json:"message"`
    Filename   string `json:"filename"`
    SourcePath string `json:"source_path"`
    TargetPath string `json:"target_path"`
}
