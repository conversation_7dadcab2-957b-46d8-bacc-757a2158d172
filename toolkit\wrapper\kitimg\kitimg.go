package kitimg

import (
    "errors"
    "net/http"
)

func IsImage(body []byte) bool {
    contentType := http.DetectContentType(body)
    switch contentType {
    case "image/jpeg":
        return true
    case "image/png":
        return true
    case "image/gif":
        return true
    case "image/webp":
        return true
    default:
        return false
    }
}
func GetImageExtension(body []byte) (_ext string,_contentType string,_err error) {
    contentType := http.DetectContentType(body)
    switch contentType {
    case "image/jpeg":
        return "jpg",contentType, nil
    case "image/png":
        return "png", contentType,nil
    case "image/gif":
        return "gif", contentType,nil
    case "image/webp":
        return "webp", contentType,nil
    default:
        return "",contentType, errors.New("非正常图片: "+contentType)
    }
}
