package kitviper

import (
    "time"

    "github.com/rs/zerolog/log"
    "github.com/spf13/viper"
)

func ReadToml(cfgFilePath string, bindToObj any) error {
    v := viper.New()
    v.SetConfigFile(cfgFilePath)
    v.SetConfigType("toml")
    err := v.ReadInConfig()
    if err != nil {
        return err
    }
    return v.Unmarshal(bindToObj)
}

// 每分钟刷新一次配置
func WatchRemoteTomlOnConsul(consulHost, consulKey string, bindToObj any) error {
    v := viper.New()
    err := v.<PERSON>d<PERSON><PERSON><PERSON>ider("consul", consulHost, consulKey)
    if err != nil {
        return err
    }
    v.SetConfigType("toml")
    err = v.ReadRemoteConfig()
    if err != nil {
        return err
    }
    err = v.Unmarshal(bindToObj)
    if err != nil {
        return err
    }
    go func() {
        for {
            time.Sleep(time.Minute)
            err = v.WatchRemoteConfig()
            if err != nil {
                log.Err(err).Msg("远程配置文件读取失败")
                continue
            }
            v.Unmarshal(bindToObj)
        }
    }()
    return nil
}
