<!DOCTYPE html>

<html>
  <head>
    <title>Protocol Documentation</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Ubuntu:400,700,400italic"/>
    <style>
      body {
        width: 60em;
        margin: 1em auto;
        color: #222;
        font-family: "Ubuntu", sans-serif;
        padding-bottom: 4em;
      }

      h1 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      h2 {
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
        margin: 1.5em 0;
      }

      h3 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      a {
        text-decoration: none;
        color: #567e25;
      }

      table {
        width: 100%;
        font-size: 80%;
        border-collapse: collapse;
      }

      thead {
        font-weight: 700;
        background-color: #dcdcdc;
      }

      tbody tr:nth-child(even) {
        background-color: #fbfbfb;
      }

      td {
        border: 1px solid #ccc;
        padding: 0.5ex 2ex;
      }

      td p {
        text-indent: 1em;
        margin: 0;
      }

      td p:nth-child(1) {
        text-indent: 0;  
      }

       
      .field-table td:nth-child(1) {  
        width: 10em;
      }
      .field-table td:nth-child(2) {  
        width: 10em;
      }
      .field-table td:nth-child(3) {  
        width: 6em;
      }
      .field-table td:nth-child(4) {  
        width: auto;
      }

       
      .extension-table td:nth-child(1) {  
        width: 10em;
      }
      .extension-table td:nth-child(2) {  
        width: 10em;
      }
      .extension-table td:nth-child(3) {  
        width: 10em;
      }
      .extension-table td:nth-child(4) {  
        width: 5em;
      }
      .extension-table td:nth-child(5) {  
        width: auto;
      }

       
      .enum-table td:nth-child(1) {  
        width: 10em;
      }
      .enum-table td:nth-child(2) {  
        width: 10em;
      }
      .enum-table td:nth-child(3) {  
        width: auto;
      }

       
      .scalar-value-types-table tr {
        height: 3em;
      }

       
      #toc-container ul {
        list-style-type: none;
        padding-left: 1em;
        line-height: 180%;
        margin: 0;
      }
      #toc > li > a {
        font-weight: bold;
      }

       
      .file-heading {
        width: 100%;
        display: table;
        border-bottom: 1px solid #aaa;
        margin: 4em 0 1.5em 0;
      }
      .file-heading h2 {
        border: none;
        display: table-cell;
      }
      .file-heading a {
        text-align: right;
        display: table-cell;
      }

       
      .badge {
        width: 1.6em;
        height: 1.6em;
        display: inline-block;

        line-height: 1.6em;
        text-align: center;
        font-weight: bold;
        font-size: 60%;

        color: #89ba48;
        background-color: #dff0c8;

        margin: 0.5ex 1em 0.5ex -1em;
        border: 1px solid #fbfbfb;
        border-radius: 1ex;
      }
    </style>

    
    <link rel="stylesheet" type="text/css" href="stylesheet.css"/>
  </head>

  <body>

    <h1 id="title">Protocol Documentation</h1>

    <h2>Table of Contents</h2>

    <div id="toc-container">
      <ul id="toc">
        
          
          <li>
            <a href="#auth.admin.proto">auth.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.LoginRequest"><span class="badge">M</span>LoginRequest</a>
                </li>
              
                <li>
                  <a href="#admin.LoginResponse"><span class="badge">M</span>LoginResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.AuthService"><span class="badge">S</span>AuthService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#authed.admin.proto">authed.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.ProfileRequest"><span class="badge">M</span>ProfileRequest</a>
                </li>
              
                <li>
                  <a href="#admin.ProfileResponse"><span class="badge">M</span>ProfileResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.AuthedService"><span class="badge">S</span>AuthedService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#material.admin.proto">material.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.CreateMaterialGroupRequest"><span class="badge">M</span>CreateMaterialGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.CreateMaterialGroupResponse"><span class="badge">M</span>CreateMaterialGroupResponse</a>
                </li>
              
                <li>
                  <a href="#admin.DeleteMaterialGroupRequest"><span class="badge">M</span>DeleteMaterialGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.DeleteMaterialGroupResponse"><span class="badge">M</span>DeleteMaterialGroupResponse</a>
                </li>
              
                <li>
                  <a href="#admin.GetMaterialGroupRequest"><span class="badge">M</span>GetMaterialGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.GetMaterialGroupResponse"><span class="badge">M</span>GetMaterialGroupResponse</a>
                </li>
              
                <li>
                  <a href="#admin.QueryMaterialGroupListRequest"><span class="badge">M</span>QueryMaterialGroupListRequest</a>
                </li>
              
                <li>
                  <a href="#admin.QueryMaterialGroupListResponse"><span class="badge">M</span>QueryMaterialGroupListResponse</a>
                </li>
              
                <li>
                  <a href="#admin.QueryMaterialListRequest"><span class="badge">M</span>QueryMaterialListRequest</a>
                </li>
              
                <li>
                  <a href="#admin.QueryMaterialListResponse"><span class="badge">M</span>QueryMaterialListResponse</a>
                </li>
              
                <li>
                  <a href="#admin.UpdateMaterialGroupRequest"><span class="badge">M</span>UpdateMaterialGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.UpdateMaterialGroupResponse"><span class="badge">M</span>UpdateMaterialGroupResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.MaterialService"><span class="badge">S</span>MaterialService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#psd.admin.proto">psd.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.DeletePsdFileRequest"><span class="badge">M</span>DeletePsdFileRequest</a>
                </li>
              
                <li>
                  <a href="#admin.DeletePsdFileResponse"><span class="badge">M</span>DeletePsdFileResponse</a>
                </li>
              
                <li>
                  <a href="#admin.DownloadPsdFileRequest"><span class="badge">M</span>DownloadPsdFileRequest</a>
                </li>
              
                <li>
                  <a href="#admin.DownloadPsdFileResponse"><span class="badge">M</span>DownloadPsdFileResponse</a>
                </li>
              
                <li>
                  <a href="#admin.QueryPsdListRequest"><span class="badge">M</span>QueryPsdListRequest</a>
                </li>
              
                <li>
                  <a href="#admin.QueryPsdListResponse"><span class="badge">M</span>QueryPsdListResponse</a>
                </li>
              
                <li>
                  <a href="#admin.UploadPsdFileRequest"><span class="badge">M</span>UploadPsdFileRequest</a>
                </li>
              
                <li>
                  <a href="#admin.UploadPsdFileResponse"><span class="badge">M</span>UploadPsdFileResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.PsdService"><span class="badge">S</span>PsdService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#psdgroups.admin.proto">psdgroups.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.CreatePsdGroupRequest"><span class="badge">M</span>CreatePsdGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.CreatePsdGroupResponse"><span class="badge">M</span>CreatePsdGroupResponse</a>
                </li>
              
                <li>
                  <a href="#admin.DeletePsdGroupRequest"><span class="badge">M</span>DeletePsdGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.DeletePsdGroupResponse"><span class="badge">M</span>DeletePsdGroupResponse</a>
                </li>
              
                <li>
                  <a href="#admin.GetPsdGroupRequest"><span class="badge">M</span>GetPsdGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.GetPsdGroupResponse"><span class="badge">M</span>GetPsdGroupResponse</a>
                </li>
              
                <li>
                  <a href="#admin.QueryPSDListRequest"><span class="badge">M</span>QueryPSDListRequest</a>
                </li>
              
                <li>
                  <a href="#admin.QueryPSDListResponse"><span class="badge">M</span>QueryPSDListResponse</a>
                </li>
              
                <li>
                  <a href="#admin.QueryPsdGroupListRequest"><span class="badge">M</span>QueryPsdGroupListRequest</a>
                </li>
              
                <li>
                  <a href="#admin.QueryPsdGroupListResponse"><span class="badge">M</span>QueryPsdGroupListResponse</a>
                </li>
              
                <li>
                  <a href="#admin.UpdatePsdGroupRequest"><span class="badge">M</span>UpdatePsdGroupRequest</a>
                </li>
              
                <li>
                  <a href="#admin.UpdatePsdGroupResponse"><span class="badge">M</span>UpdatePsdGroupResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.PsdGroupService"><span class="badge">S</span>PsdGroupService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#temu.admin.proto">temu.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.QueryTemuListRequest"><span class="badge">M</span>QueryTemuListRequest</a>
                </li>
              
                <li>
                  <a href="#admin.QueryTemuListResponse"><span class="badge">M</span>QueryTemuListResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.TemuService"><span class="badge">S</span>TemuService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#trademark.admin.proto">trademark.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.QueryTrademarkListRequest"><span class="badge">M</span>QueryTrademarkListRequest</a>
                </li>
              
                <li>
                  <a href="#admin.QueryTrademarkListResponse"><span class="badge">M</span>QueryTrademarkListResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.TrademarkService"><span class="badge">S</span>TrademarkService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#uploaddata.admin.proto">uploaddata.admin.proto</a>
            <ul>
              
                <li>
                  <a href="#admin.TemuBmwRequest"><span class="badge">M</span>TemuBmwRequest</a>
                </li>
              
                <li>
                  <a href="#admin.TemuBmwResponse"><span class="badge">M</span>TemuBmwResponse</a>
                </li>
              
                <li>
                  <a href="#admin.TrademarkRequest"><span class="badge">M</span>TrademarkRequest</a>
                </li>
              
                <li>
                  <a href="#admin.TrademarkResponse"><span class="badge">M</span>TrademarkResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#admin.UploadDataService"><span class="badge">S</span>UploadDataService</a>
                </li>
              
            </ul>
          </li>
        
        <li><a href="#scalar-value-types">Scalar Value Types</a></li>
      </ul>
    </div>

    
      
      <div class="file-heading">
        <h2 id="auth.admin.proto">auth.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.LoginRequest">LoginRequest</h3>
        <p>登录请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>username</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>用户名 </p></td>
                </tr>
              
                <tr>
                  <td>password</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>密码 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.LoginResponse">LoginResponse</h3>
        <p>登录响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>username</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>用户名 </p></td>
                </tr>
              
                <tr>
                  <td>token</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>登录token </p></td>
                </tr>
              
                <tr>
                  <td>expire_at</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>过期时间 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="admin.AuthService">AuthService</h3>
        <p>未登录的后台账号功能 20250827</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>Login</td>
                <td><a href="#admin.LoginRequest">LoginRequest</a></td>
                <td><a href="#admin.LoginResponse">LoginResponse</a></td>
                <td><p>登录</p></td>
              </tr>
            
          </tbody>
        </table>

        
    
      
      <div class="file-heading">
        <h2 id="authed.admin.proto">authed.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.ProfileRequest">ProfileRequest</h3>
        <p>获取当前账号资料 请求</p>

        

        
      
        <h3 id="admin.ProfileResponse">ProfileResponse</h3>
        <p>获取当前账号资料 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>username</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>用户名 20250827 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="admin.AuthedService">AuthedService</h3>
        <p>已登录的后台账号管理 20250827</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>Profile</td>
                <td><a href="#admin.ProfileRequest">ProfileRequest</a></td>
                <td><a href="#admin.ProfileResponse">ProfileResponse</a></td>
                <td><p>获取当前账号资料 接口 20250827</p></td>
              </tr>
            
          </tbody>
        </table>

        
    
      
      <div class="file-heading">
        <h2 id="material.admin.proto">material.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.CreateMaterialGroupRequest">CreateMaterialGroupRequest</h3>
        <p>创建素材分组 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>分组名称 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.CreateMaterialGroupResponse">CreateMaterialGroupResponse</h3>
        <p>创建素材分组 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>item</td>
                  <td><a href="#msgpb.MaterialGroup">msgpb.MaterialGroup</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.DeleteMaterialGroupRequest">DeleteMaterialGroupRequest</h3>
        <p>删除素材分组 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td>repeated</td>
                  <td><p>分组ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.DeleteMaterialGroupResponse">DeleteMaterialGroupResponse</h3>
        <p>删除素材分组 响应</p>

        

        
      
        <h3 id="admin.GetMaterialGroupRequest">GetMaterialGroupRequest</h3>
        <p>获取素材分组详情 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>分组ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.GetMaterialGroupResponse">GetMaterialGroupResponse</h3>
        <p>获取素材分组详情 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>item</td>
                  <td><a href="#msgpb.MaterialGroup">msgpb.MaterialGroup</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryMaterialGroupListRequest">QueryMaterialGroupListRequest</h3>
        <p>查询素材分组列表 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page_size</td>
                  <td><a href="#msgpb.PageSizeRequest">msgpb.PageSizeRequest</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>分组名称筛选 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryMaterialGroupListResponse">QueryMaterialGroupListResponse</h3>
        <p>查询素材分组列表 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>total</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>items</td>
                  <td><a href="#msgpb.MaterialGroup">msgpb.MaterialGroup</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryMaterialListRequest">QueryMaterialListRequest</h3>
        <p>查询素材列表 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page_size</td>
                  <td><a href="#msgpb.PageSizeRequest">msgpb.PageSizeRequest</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>title</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>查询标题 20250904 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryMaterialListResponse">QueryMaterialListResponse</h3>
        <p>查询素材列表 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>total</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>items</td>
                  <td><a href="#msgpb.Material">msgpb.Material</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.UpdateMaterialGroupRequest">UpdateMaterialGroupRequest</h3>
        <p>更新素材分组 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>分组ID </p></td>
                </tr>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>分组名称 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.UpdateMaterialGroupResponse">UpdateMaterialGroupResponse</h3>
        <p>更新素材分组 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>item</td>
                  <td><a href="#msgpb.MaterialGroup">msgpb.MaterialGroup</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="admin.MaterialService">MaterialService</h3>
        <p>素材管理 20250904</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>QueryMaterialGroupList</td>
                <td><a href="#admin.QueryMaterialGroupListRequest">QueryMaterialGroupListRequest</a></td>
                <td><a href="#admin.QueryMaterialGroupListResponse">QueryMaterialGroupListResponse</a></td>
                <td><p>查询素材分组列表 接口 20250904</p></td>
              </tr>
            
              <tr>
                <td>CreateMaterialGroup</td>
                <td><a href="#admin.CreateMaterialGroupRequest">CreateMaterialGroupRequest</a></td>
                <td><a href="#admin.CreateMaterialGroupResponse">CreateMaterialGroupResponse</a></td>
                <td><p>创建素材分组 接口 20250904</p></td>
              </tr>
            
              <tr>
                <td>UpdateMaterialGroup</td>
                <td><a href="#admin.UpdateMaterialGroupRequest">UpdateMaterialGroupRequest</a></td>
                <td><a href="#admin.UpdateMaterialGroupResponse">UpdateMaterialGroupResponse</a></td>
                <td><p>更新素材分组 接口 20250904</p></td>
              </tr>
            
              <tr>
                <td>DeleteMaterialGroup</td>
                <td><a href="#admin.DeleteMaterialGroupRequest">DeleteMaterialGroupRequest</a></td>
                <td><a href="#admin.DeleteMaterialGroupResponse">DeleteMaterialGroupResponse</a></td>
                <td><p>删除素材分组 接口 20250904</p></td>
              </tr>
            
              <tr>
                <td>GetMaterialGroup</td>
                <td><a href="#admin.GetMaterialGroupRequest">GetMaterialGroupRequest</a></td>
                <td><a href="#admin.GetMaterialGroupResponse">GetMaterialGroupResponse</a></td>
                <td><p>获取素材分组详情 接口 20250904</p></td>
              </tr>
            
              <tr>
                <td>QueryMaterialList</td>
                <td><a href="#admin.QueryMaterialListRequest">QueryMaterialListRequest</a></td>
                <td><a href="#admin.QueryMaterialListResponse">QueryMaterialListResponse</a></td>
                <td><p>查询素材列表 接口 20250904</p></td>
              </tr>
            
          </tbody>
        </table>

        
    
      
      <div class="file-heading">
        <h2 id="psd.admin.proto">psd.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.DeletePsdFileRequest">DeletePsdFileRequest</h3>
        <p>删除PSD模板文件 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td>repeated</td>
                  <td><p>模板ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.DeletePsdFileResponse">DeletePsdFileResponse</h3>
        <p>删除PSD模板文件 响应</p>

        

        
      
        <h3 id="admin.DownloadPsdFileRequest">DownloadPsdFileRequest</h3>
        <p>下载PSD模板文件 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>模板ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.DownloadPsdFileResponse">DownloadPsdFileResponse</h3>
        <p>下载PSD模板文件 响应</p>

        

        
      
        <h3 id="admin.QueryPsdListRequest">QueryPsdListRequest</h3>
        <p>查询PSD模板列表 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page_size</td>
                  <td><a href="#msgpb.PageSizeRequest">msgpb.PageSizeRequest</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryPsdListResponse">QueryPsdListResponse</h3>
        <p>查询PSD模板列表 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>total</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>items</td>
                  <td><a href="#msgpb.PSD">msgpb.PSD</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.UploadPsdFileRequest">UploadPsdFileRequest</h3>
        <p>上传PSD模板文件 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>hash</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.UploadPsdFileResponse">UploadPsdFileResponse</h3>
        <p>上传PSD模板文件 响应</p>

        

        
      

      

      

      
        <h3 id="admin.PsdService">PsdService</h3>
        <p>PSD模板管理 20250916</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>QueryPsdList</td>
                <td><a href="#admin.QueryPsdListRequest">QueryPsdListRequest</a></td>
                <td><a href="#admin.QueryPsdListResponse">QueryPsdListResponse</a></td>
                <td><p>查询PSD模板列表 接口 20250916</p></td>
              </tr>
            
              <tr>
                <td>UploadPsdFile</td>
                <td><a href="#admin.UploadPsdFileRequest">UploadPsdFileRequest</a></td>
                <td><a href="#admin.UploadPsdFileResponse">UploadPsdFileResponse</a></td>
                <td><p>创建PSD分组 接口 20250916</p></td>
              </tr>
            
              <tr>
                <td>DownloadFile</td>
                <td><a href="#admin.DownloadPsdFileRequest">DownloadPsdFileRequest</a></td>
                <td><a href="#admin.DownloadPsdFileResponse">DownloadPsdFileResponse</a></td>
                <td><p>更新PSD分组 接口 20250916</p></td>
              </tr>
            
              <tr>
                <td>DeletePsdFile</td>
                <td><a href="#admin.DeletePsdFileRequest">DeletePsdFileRequest</a></td>
                <td><a href="#admin.DeletePsdFileResponse">DeletePsdFileResponse</a></td>
                <td><p>删除PSD分组 接口 20250916</p></td>
              </tr>
            
          </tbody>
        </table>

        
    
      
      <div class="file-heading">
        <h2 id="psdgroups.admin.proto">psdgroups.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.CreatePsdGroupRequest">CreatePsdGroupRequest</h3>
        <p>创建PSD分组 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>分组名称 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.CreatePsdGroupResponse">CreatePsdGroupResponse</h3>
        <p>创建PSD分组 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>item</td>
                  <td><a href="#msgpb.PSDGroup">msgpb.PSDGroup</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.DeletePsdGroupRequest">DeletePsdGroupRequest</h3>
        <p>删除PSD分组 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td>repeated</td>
                  <td><p>分组ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.DeletePsdGroupResponse">DeletePsdGroupResponse</h3>
        <p>删除PSD分组 响应</p>

        

        
      
        <h3 id="admin.GetPsdGroupRequest">GetPsdGroupRequest</h3>
        <p>获取PSD分组详情 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>分组ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.GetPsdGroupResponse">GetPsdGroupResponse</h3>
        <p>获取PSD分组详情 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>item</td>
                  <td><a href="#msgpb.PSDGroup">msgpb.PSDGroup</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryPSDListRequest">QueryPSDListRequest</h3>
        <p>查询PSD模板列表 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page_size</td>
                  <td><a href="#msgpb.PageSizeRequest">msgpb.PageSizeRequest</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>查询id </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryPSDListResponse">QueryPSDListResponse</h3>
        <p>查询PSD模板列表 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>total</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>items</td>
                  <td><a href="#msgpb.PSD">msgpb.PSD</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryPsdGroupListRequest">QueryPsdGroupListRequest</h3>
        <p>查询PSD分组列表 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page_size</td>
                  <td><a href="#msgpb.PageSizeRequest">msgpb.PageSizeRequest</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>分组名称筛选 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryPsdGroupListResponse">QueryPsdGroupListResponse</h3>
        <p>查询PSD分组列表 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>total</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>items</td>
                  <td><a href="#msgpb.PSDGroup">msgpb.PSDGroup</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.UpdatePsdGroupRequest">UpdatePsdGroupRequest</h3>
        <p>更新PSD分组 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>分组ID </p></td>
                </tr>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>更新的分组名称 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.UpdatePsdGroupResponse">UpdatePsdGroupResponse</h3>
        <p>更新PSD分组 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>item</td>
                  <td><a href="#msgpb.PSDGroup">msgpb.PSDGroup</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="admin.PsdGroupService">PsdGroupService</h3>
        <p>PSD分组管理 20250910</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>QueryPsdGroupList</td>
                <td><a href="#admin.QueryPsdGroupListRequest">QueryPsdGroupListRequest</a></td>
                <td><a href="#admin.QueryPsdGroupListResponse">QueryPsdGroupListResponse</a></td>
                <td><p>查询PSD分组列表 接口 20250910</p></td>
              </tr>
            
              <tr>
                <td>CreatePsdGroup</td>
                <td><a href="#admin.CreatePsdGroupRequest">CreatePsdGroupRequest</a></td>
                <td><a href="#admin.CreatePsdGroupResponse">CreatePsdGroupResponse</a></td>
                <td><p>创建PSD分组 接口 20250910</p></td>
              </tr>
            
              <tr>
                <td>UpdatePsdGroup</td>
                <td><a href="#admin.UpdatePsdGroupRequest">UpdatePsdGroupRequest</a></td>
                <td><a href="#admin.UpdatePsdGroupResponse">UpdatePsdGroupResponse</a></td>
                <td><p>更新PSD分组 接口 20250910</p></td>
              </tr>
            
              <tr>
                <td>DeletePsdGroup</td>
                <td><a href="#admin.DeletePsdGroupRequest">DeletePsdGroupRequest</a></td>
                <td><a href="#admin.DeletePsdGroupResponse">DeletePsdGroupResponse</a></td>
                <td><p>删除PSD分组 接口 20250910</p></td>
              </tr>
            
              <tr>
                <td>GetPsdGroup</td>
                <td><a href="#admin.GetPsdGroupRequest">GetPsdGroupRequest</a></td>
                <td><a href="#admin.GetPsdGroupResponse">GetPsdGroupResponse</a></td>
                <td><p>获取PSD分组详情 接口 20250910</p></td>
              </tr>
            
              <tr>
                <td>QueryPSDList</td>
                <td><a href="#admin.QueryPSDListRequest">QueryPSDListRequest</a></td>
                <td><a href="#admin.QueryPSDListResponse">QueryPSDListResponse</a></td>
                <td><p>查询PSD模板列表 接口 20250910</p></td>
              </tr>
            
          </tbody>
        </table>

        
    
      
      <div class="file-heading">
        <h2 id="temu.admin.proto">temu.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.QueryTemuListRequest">QueryTemuListRequest</h3>
        <p>查询Temu数据列表 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page_size</td>
                  <td><a href="#msgpb.PageSizeRequest">msgpb.PageSizeRequest</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>title</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>搜索标题 20250828 </p></td>
                </tr>
              
                <tr>
                  <td>temu_cats</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>过滤分类 20250829 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryTemuListResponse">QueryTemuListResponse</h3>
        <p>查询Temu数据列表 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>items</td>
                  <td><a href="#msgpb.ProductItem">msgpb.ProductItem</a></td>
                  <td>repeated</td>
                  <td><p>数据列表 20250828 </p></td>
                </tr>
              
                <tr>
                  <td>total</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>总数量 20250828 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="admin.TemuService">TemuService</h3>
        <p>Temu服务 20250827</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>QueryTemuList</td>
                <td><a href="#admin.QueryTemuListRequest">QueryTemuListRequest</a></td>
                <td><a href="#admin.QueryTemuListResponse">QueryTemuListResponse</a></td>
                <td><p>查询Temu数据列表 接口 20250828</p></td>
              </tr>
            
          </tbody>
        </table>

        
    
      
      <div class="file-heading">
        <h2 id="trademark.admin.proto">trademark.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.QueryTrademarkListRequest">QueryTrademarkListRequest</h3>
        <p>商标列表 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page_size</td>
                  <td><a href="#msgpb.PageSizeRequest">msgpb.PageSizeRequest</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.QueryTrademarkListResponse">QueryTrademarkListResponse</h3>
        <p>商标列表 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>total</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>items</td>
                  <td><a href="#msgpb.RegisteredTrademark">msgpb.RegisteredTrademark</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="admin.TrademarkService">TrademarkService</h3>
        <p>商标管理 20250808</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>QueryTrademarkList</td>
                <td><a href="#admin.QueryTrademarkListRequest">QueryTrademarkListRequest</a></td>
                <td><a href="#admin.QueryTrademarkListResponse">QueryTrademarkListResponse</a></td>
                <td><p>商标列表数据查询 接口 20250827</p></td>
              </tr>
            
          </tbody>
        </table>

        
    
      
      <div class="file-heading">
        <h2 id="uploaddata.admin.proto">uploaddata.admin.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="admin.TemuBmwRequest">TemuBmwRequest</h3>
        <p>Temu BMW数据上报 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>hash</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>数据校验码 20250808 </p></td>
                </tr>
              
                <tr>
                  <td>data</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>上报数据 20250808 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.TemuBmwResponse">TemuBmwResponse</h3>
        <p>Temu BMW数据上报 响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>success</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>skip</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>error</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>sales</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>有销量的产品数量 20250811 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.TrademarkRequest">TrademarkRequest</h3>
        <p>商标数据上报 请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>hash</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>数据校验码 20250808 </p></td>
                </tr>
              
                <tr>
                  <td>data</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>上报数据,结构为对象,里面必须包含字段 id(类型int),系统根据id进行更新或创建 20250808 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="admin.TrademarkResponse">TrademarkResponse</h3>
        <p>商标数据上报 响应</p>

        

        
      

      

      

      
        <h3 id="admin.UploadDataService">UploadDataService</h3>
        <p>采集数据上报 20250808</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>TemuBmw</td>
                <td><a href="#admin.TemuBmwRequest">TemuBmwRequest</a></td>
                <td><a href="#admin.TemuBmwResponse">TemuBmwResponse</a></td>
                <td><p>Temu BMW数据上报 接口 20250808</p></td>
              </tr>
            
              <tr>
                <td>Trademark</td>
                <td><a href="#admin.TrademarkRequest">TrademarkRequest</a></td>
                <td><a href="#admin.TrademarkResponse">TrademarkResponse</a></td>
                <td><p>商标数据上报 接口 20250827</p></td>
              </tr>
            
          </tbody>
        </table>

        
    

    <h2 id="scalar-value-types">Scalar Value Types</h2>
    <table class="scalar-value-types-table">
      <thead>
        <tr><td>.proto Type</td><td>Notes</td><td>C++</td><td>Java</td><td>Python</td><td>Go</td><td>C#</td><td>PHP</td><td>Ruby</td></tr>
      </thead>
      <tbody>
        
          <tr id="double">
            <td>double</td>
            <td></td>
            <td>double</td>
            <td>double</td>
            <td>float</td>
            <td>float64</td>
            <td>double</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="float">
            <td>float</td>
            <td></td>
            <td>float</td>
            <td>float</td>
            <td>float</td>
            <td>float32</td>
            <td>float</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="int32">
            <td>int32</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="int64">
            <td>int64</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="uint32">
            <td>uint32</td>
            <td>Uses variable-length encoding.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int/long</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="uint64">
            <td>uint64</td>
            <td>Uses variable-length encoding.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint32">
            <td>sint32</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint64">
            <td>sint64</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="fixed32">
            <td>fixed32</td>
            <td>Always four bytes. More efficient than uint32 if values are often greater than 2^28.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="fixed64">
            <td>fixed64</td>
            <td>Always eight bytes. More efficient than uint64 if values are often greater than 2^56.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="sfixed32">
            <td>sfixed32</td>
            <td>Always four bytes.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sfixed64">
            <td>sfixed64</td>
            <td>Always eight bytes.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="bool">
            <td>bool</td>
            <td></td>
            <td>bool</td>
            <td>boolean</td>
            <td>boolean</td>
            <td>bool</td>
            <td>bool</td>
            <td>boolean</td>
            <td>TrueClass/FalseClass</td>
          </tr>
        
          <tr id="string">
            <td>string</td>
            <td>A string must always contain UTF-8 encoded or 7-bit ASCII text.</td>
            <td>string</td>
            <td>String</td>
            <td>str/unicode</td>
            <td>string</td>
            <td>string</td>
            <td>string</td>
            <td>String (UTF-8)</td>
          </tr>
        
          <tr id="bytes">
            <td>bytes</td>
            <td>May contain any arbitrary sequence of bytes.</td>
            <td>string</td>
            <td>ByteString</td>
            <td>str</td>
            <td>[]byte</td>
            <td>ByteString</td>
            <td>string</td>
            <td>String (ASCII-8BIT)</td>
          </tr>
        
      </tbody>
    </table>
  </body>
</html>

