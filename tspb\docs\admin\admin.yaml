openapi: 3.1.0
info:
  description: "后台接口文档"
  title: 后台接口文档
  version: v1.0.0
paths:
  /admin.AuthService/Login:
    post:
      tags:
        - admin.AuthService
      summary: 登录
      description: 登录
      operationId: admin.AuthService.Login
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.LoginRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.LoginResponse'
  /admin.AuthedService/Profile:
    post:
      tags:
        - admin.AuthedService
      summary: 获取当前账号资料 接口 20250827
      description: 获取当前账号资料 接口 20250827
      operationId: admin.AuthedService.Profile
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.ProfileRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.ProfileResponse'
  /admin.MaterialService/CreateMaterialGroup:
    post:
      tags:
        - admin.MaterialService
      summary: 创建素材分组 接口 20250904
      description: 创建素材分组 接口 20250904
      operationId: admin.MaterialService.CreateMaterialGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.CreateMaterialGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.CreateMaterialGroupResponse'
  /admin.MaterialService/DeleteMaterialGroup:
    post:
      tags:
        - admin.MaterialService
      summary: 删除素材分组 接口 20250904
      description: 删除素材分组 接口 20250904
      operationId: admin.MaterialService.DeleteMaterialGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.DeleteMaterialGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.DeleteMaterialGroupResponse'
  /admin.MaterialService/GetMaterialGroup:
    post:
      tags:
        - admin.MaterialService
      summary: 获取素材分组详情 接口 20250904
      description: 获取素材分组详情 接口 20250904
      operationId: admin.MaterialService.GetMaterialGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.GetMaterialGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.GetMaterialGroupResponse'
  /admin.MaterialService/QueryMaterialGroupList:
    post:
      tags:
        - admin.MaterialService
      summary: 查询素材分组列表 接口 20250904
      description: 查询素材分组列表 接口 20250904
      operationId: admin.MaterialService.QueryMaterialGroupList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.QueryMaterialGroupListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.QueryMaterialGroupListResponse'
  /admin.MaterialService/QueryMaterialList:
    post:
      tags:
        - admin.MaterialService
      summary: 查询素材列表 接口 20250904
      description: 查询素材列表 接口 20250904
      operationId: admin.MaterialService.QueryMaterialList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.QueryMaterialListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.QueryMaterialListResponse'
  /admin.MaterialService/UpdateMaterialGroup:
    post:
      tags:
        - admin.MaterialService
      summary: 更新素材分组 接口 20250904
      description: 更新素材分组 接口 20250904
      operationId: admin.MaterialService.UpdateMaterialGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.UpdateMaterialGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.UpdateMaterialGroupResponse'
  /admin.PsdGroupService/CreatePsdGroup:
    post:
      tags:
        - admin.PsdGroupService
      summary: 创建PSD分组 接口 20250910
      description: 创建PSD分组 接口 20250910
      operationId: admin.PsdGroupService.CreatePsdGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.CreatePsdGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.CreatePsdGroupResponse'
  /admin.PsdGroupService/DeletePsdGroup:
    post:
      tags:
        - admin.PsdGroupService
      summary: 删除PSD分组 接口 20250910
      description: 删除PSD分组 接口 20250910
      operationId: admin.PsdGroupService.DeletePsdGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.DeletePsdGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.DeletePsdGroupResponse'
  /admin.PsdGroupService/GetPsdGroup:
    post:
      tags:
        - admin.PsdGroupService
      summary: 获取PSD分组详情 接口 20250910
      description: 获取PSD分组详情 接口 20250910
      operationId: admin.PsdGroupService.GetPsdGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.GetPsdGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.GetPsdGroupResponse'
  /admin.PsdGroupService/QueryPSDList:
    post:
      tags:
        - admin.PsdGroupService
      summary: 查询PSD模板列表 接口 20250910
      description: 查询PSD模板列表 接口 20250910
      operationId: admin.PsdGroupService.QueryPSDList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.QueryPSDListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.QueryPSDListResponse'
  /admin.PsdGroupService/QueryPsdGroupList:
    post:
      tags:
        - admin.PsdGroupService
      summary: 查询PSD分组列表 接口 20250910
      description: 查询PSD分组列表 接口 20250910
      operationId: admin.PsdGroupService.QueryPsdGroupList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.QueryPsdGroupListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.QueryPsdGroupListResponse'
  /admin.PsdGroupService/UpdatePsdGroup:
    post:
      tags:
        - admin.PsdGroupService
      summary: 更新PSD分组 接口 20250910
      description: 更新PSD分组 接口 20250910
      operationId: admin.PsdGroupService.UpdatePsdGroup
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.UpdatePsdGroupRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.UpdatePsdGroupResponse'
  /admin.PsdService/DeletePsdFile:
    post:
      tags:
        - admin.PsdService
      summary: 删除PSD分组 接口 20250916
      description: 删除PSD分组 接口 20250916
      operationId: admin.PsdService.DeletePsdFile
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.DeletePsdFileRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.DeletePsdFileResponse'
  /admin.PsdService/DownloadFile:
    post:
      tags:
        - admin.PsdService
      summary: 更新PSD分组 接口 20250916
      description: 更新PSD分组 接口 20250916
      operationId: admin.PsdService.DownloadFile
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.DownloadPsdFileRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.DownloadPsdFileResponse'
  /admin.PsdService/QueryPsdList:
    post:
      tags:
        - admin.PsdService
      summary: 查询PSD模板列表 接口 20250916
      description: 查询PSD模板列表 接口 20250916
      operationId: admin.PsdService.QueryPsdList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.QueryPsdListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.QueryPsdListResponse'
  /admin.PsdService/UploadPsdFile:
    post:
      tags:
        - admin.PsdService
      summary: 创建PSD分组 接口 20250916
      description: 创建PSD分组 接口 20250916
      operationId: admin.PsdService.UploadPsdFile
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.UploadPsdFileRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.UploadPsdFileResponse'
  /admin.TemuService/QueryTemuList:
    post:
      tags:
        - admin.TemuService
      summary: 查询Temu数据列表 接口 20250828
      description: 查询Temu数据列表 接口 20250828
      operationId: admin.TemuService.QueryTemuList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.QueryTemuListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.QueryTemuListResponse'
  /admin.TrademarkService/QueryTrademarkList:
    post:
      tags:
        - admin.TrademarkService
      summary: 商标列表数据查询 接口 20250827
      description: 商标列表数据查询 接口 20250827
      operationId: admin.TrademarkService.QueryTrademarkList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.QueryTrademarkListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.QueryTrademarkListResponse'
  /admin.UploadDataService/TemuBmw:
    post:
      tags:
        - admin.UploadDataService
      summary: Temu BMW数据上报 接口 20250808
      description: Temu BMW数据上报 接口 20250808
      operationId: admin.UploadDataService.TemuBmw
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.TemuBmwRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.TemuBmwResponse'
  /admin.UploadDataService/Trademark:
    post:
      tags:
        - admin.UploadDataService
      summary: 商标数据上报 接口 20250827
      description: 商标数据上报 接口 20250827
      operationId: admin.UploadDataService.Trademark
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/admin.TrademarkRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/admin.TrademarkResponse'
components:
  schemas:
    admin.CreateMaterialGroupRequest:
      type: object
      properties:
        name:
          type: string
          title: name
          description: 分组名称
      title: CreateMaterialGroupRequest
      additionalProperties: false
      description: 创建素材分组 请求
    admin.CreateMaterialGroupResponse:
      type: object
      properties:
        item:
          title: item
          $ref: '#/components/schemas/msgpb.MaterialGroup'
      title: CreateMaterialGroupResponse
      additionalProperties: false
      description: 创建素材分组 响应
    admin.CreatePsdGroupRequest:
      type: object
      properties:
        name:
          type: string
          title: name
          description: 分组名称
      title: CreatePsdGroupRequest
      additionalProperties: false
      description: 创建PSD分组 请求
    admin.CreatePsdGroupResponse:
      type: object
      properties:
        item:
          title: item
          $ref: '#/components/schemas/msgpb.PSDGroup'
      title: CreatePsdGroupResponse
      additionalProperties: false
      description: 创建PSD分组 响应
    admin.DeleteMaterialGroupRequest:
      type: object
      properties:
        id:
          type: array
          items:
            type:
              - integer
              - string
            format: int64
          title: id
          description: 分组ID
      title: DeleteMaterialGroupRequest
      additionalProperties: false
      description: 删除素材分组 请求
    admin.DeleteMaterialGroupResponse:
      type: object
      title: DeleteMaterialGroupResponse
      additionalProperties: false
      description: 删除素材分组 响应
    admin.DeletePsdFileRequest:
      type: object
      properties:
        id:
          type: array
          items:
            type:
              - integer
              - string
            format: int64
          title: id
          description: 模板ID
      title: DeletePsdFileRequest
      additionalProperties: false
      description: 删除PSD模板文件 请求
    admin.DeletePsdFileResponse:
      type: object
      title: DeletePsdFileResponse
      additionalProperties: false
      description: 删除PSD模板文件 响应
    admin.DeletePsdGroupRequest:
      type: object
      properties:
        id:
          type: array
          items:
            type:
              - integer
              - string
            format: int64
          title: id
          description: 分组ID
      title: DeletePsdGroupRequest
      additionalProperties: false
      description: 删除PSD分组 请求
    admin.DeletePsdGroupResponse:
      type: object
      title: DeletePsdGroupResponse
      additionalProperties: false
      description: 删除PSD分组 响应
    admin.DownloadPsdFileRequest:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: 模板ID
      title: DownloadPsdFileRequest
      additionalProperties: false
      description: 下载PSD模板文件 请求
    admin.DownloadPsdFileResponse:
      type: object
      title: DownloadPsdFileResponse
      additionalProperties: false
      description: 下载PSD模板文件 响应
    admin.GetMaterialGroupRequest:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: 分组ID
      title: GetMaterialGroupRequest
      additionalProperties: false
      description: 获取素材分组详情 请求
    admin.GetMaterialGroupResponse:
      type: object
      properties:
        item:
          title: item
          $ref: '#/components/schemas/msgpb.MaterialGroup'
      title: GetMaterialGroupResponse
      additionalProperties: false
      description: 获取素材分组详情 响应
    admin.GetPsdGroupRequest:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: 分组ID
      title: GetPsdGroupRequest
      additionalProperties: false
      description: 获取PSD分组详情 请求
    admin.GetPsdGroupResponse:
      type: object
      properties:
        item:
          title: item
          $ref: '#/components/schemas/msgpb.PSDGroup'
      title: GetPsdGroupResponse
      additionalProperties: false
      description: 获取PSD分组详情 响应
    admin.LoginRequest:
      type: object
      properties:
        username:
          type: string
          title: username
          maxLength: 30
          minLength: 6
          pattern: ^[a-zA-Z0-9]+$
          description: 用户名
        password:
          type: string
          title: password
          maxLength: 60
          minLength: 6
          description: 密码
      title: LoginRequest
      required:
        - username
        - password
      additionalProperties: false
      description: 登录请求
    admin.LoginResponse:
      type: object
      properties:
        username:
          type: string
          title: username
          description: 用户名
        token:
          type: string
          title: token
          description: 登录token
        expireAt:
          type:
            - integer
            - string
          title: expire_at
          format: int64
          description: 过期时间
      title: LoginResponse
      additionalProperties: false
      description: 登录响应
    admin.ProfileRequest:
      type: object
      title: ProfileRequest
      additionalProperties: false
      description: 获取当前账号资料 请求
    admin.ProfileResponse:
      type: object
      properties:
        username:
          type: string
          title: username
          description: 用户名 20250827
      title: ProfileResponse
      additionalProperties: false
      description: 获取当前账号资料 响应
    admin.QueryMaterialGroupListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
        name:
          type: string
          title: name
          description: 分组名称筛选
      title: QueryMaterialGroupListRequest
      additionalProperties: false
      description: 查询素材分组列表 请求
    admin.QueryMaterialGroupListResponse:
      type: object
      properties:
        total:
          type:
            - integer
            - string
          title: total
          format: int64
        items:
          type: array
          items:
            $ref: '#/components/schemas/msgpb.MaterialGroup'
          title: items
      title: QueryMaterialGroupListResponse
      additionalProperties: false
      description: 查询素材分组列表 响应
    admin.QueryMaterialListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
        title:
          type: string
          title: title
          description: 查询标题 20250904
      title: QueryMaterialListRequest
      additionalProperties: false
      description: 查询素材列表 请求
    admin.QueryMaterialListResponse:
      type: object
      properties:
        total:
          type:
            - integer
            - string
          title: total
          format: int64
        items:
          type: array
          items:
            $ref: '#/components/schemas/msgpb.Material'
          title: items
      title: QueryMaterialListResponse
      additionalProperties: false
      description: 查询素材列表 响应
    admin.QueryPSDListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: 查询id
      title: QueryPSDListRequest
      additionalProperties: false
      description: 查询PSD模板列表 请求
    admin.QueryPSDListResponse:
      type: object
      properties:
        total:
          type:
            - integer
            - string
          title: total
          format: int64
        items:
          type: array
          items:
            $ref: '#/components/schemas/msgpb.PSD'
          title: items
      title: QueryPSDListResponse
      additionalProperties: false
      description: 查询PSD模板列表 响应
    admin.QueryPsdGroupListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
        name:
          type: string
          title: name
          description: 分组名称筛选
      title: QueryPsdGroupListRequest
      additionalProperties: false
      description: 查询PSD分组列表 请求
    admin.QueryPsdGroupListResponse:
      type: object
      properties:
        total:
          type:
            - integer
            - string
          title: total
          format: int64
        items:
          type: array
          items:
            $ref: '#/components/schemas/msgpb.PSDGroup'
          title: items
      title: QueryPsdGroupListResponse
      additionalProperties: false
      description: 查询PSD分组列表 响应
    admin.QueryPsdListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
      title: QueryPsdListRequest
      additionalProperties: false
      description: 查询PSD模板列表 请求
    admin.QueryPsdListResponse:
      type: object
      properties:
        total:
          type:
            - integer
            - string
          title: total
          format: int64
        items:
          type: array
          items:
            $ref: '#/components/schemas/msgpb.PSD'
          title: items
      title: QueryPsdListResponse
      additionalProperties: false
      description: 查询PSD模板列表 响应
    admin.QueryTemuListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
        title:
          type: string
          title: title
          description: 搜索标题 20250828
        temuCats:
          type: array
          items:
            type: string
          title: temu_cats
          description: 过滤分类 20250829
      title: QueryTemuListRequest
      additionalProperties: false
      description: 查询Temu数据列表 请求
    admin.QueryTemuListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/msgpb.ProductItem'
          title: items
          description: 数据列表 20250828
        total:
          type:
            - integer
            - string
          title: total
          format: int64
          description: 总数量 20250828
      title: QueryTemuListResponse
      additionalProperties: false
      description: 查询Temu数据列表 响应
    admin.QueryTrademarkListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
      title: QueryTrademarkListRequest
      additionalProperties: false
      description: 商标列表 请求
    admin.QueryTrademarkListResponse:
      type: object
      properties:
        total:
          type:
            - integer
            - string
          title: total
          format: int64
        items:
          type: array
          items:
            $ref: '#/components/schemas/msgpb.RegisteredTrademark'
          title: items
      title: QueryTrademarkListResponse
      additionalProperties: false
      description: 商标列表 响应
    admin.TemuBmwRequest:
      type: object
      properties:
        hash:
          type: string
          title: hash
          maxLength: 40
          minLength: 40
          description: 数据校验码 20250808
        data:
          type: string
          title: data
          description: 上报数据 20250808
      title: TemuBmwRequest
      required:
        - hash
      additionalProperties: false
      description: Temu BMW数据上报 请求
    admin.TemuBmwResponse:
      type: object
      properties:
        success:
          type:
            - integer
            - string
          title: success
          format: int64
        skip:
          type:
            - integer
            - string
          title: skip
          format: int64
        error:
          type:
            - integer
            - string
          title: error
          format: int64
        sales:
          type:
            - integer
            - string
          title: sales
          format: int64
          description: 有销量的产品数量 20250811
      title: TemuBmwResponse
      additionalProperties: false
      description: Temu BMW数据上报 响应
    admin.TrademarkRequest:
      type: object
      properties:
        hash:
          type: string
          title: hash
          maxLength: 40
          minLength: 40
          description: 数据校验码 20250808
        data:
          type: string
          title: data
          description: 上报数据,结构为对象,里面必须包含字段 id(类型int),系统根据id进行更新或创建 20250808
      title: TrademarkRequest
      required:
        - hash
      additionalProperties: false
      description: 商标数据上报 请求
    admin.TrademarkResponse:
      type: object
      title: TrademarkResponse
      additionalProperties: false
      description: 商标数据上报 响应
    admin.UpdateMaterialGroupRequest:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: 分组ID
        name:
          type: string
          title: name
          description: 分组名称
      title: UpdateMaterialGroupRequest
      additionalProperties: false
      description: 更新素材分组 请求
    admin.UpdateMaterialGroupResponse:
      type: object
      properties:
        item:
          title: item
          $ref: '#/components/schemas/msgpb.MaterialGroup'
      title: UpdateMaterialGroupResponse
      additionalProperties: false
      description: 更新素材分组 响应
    admin.UpdatePsdGroupRequest:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: 分组ID
        name:
          type: string
          title: name
          description: 更新的分组名称
      title: UpdatePsdGroupRequest
      additionalProperties: false
      description: 更新PSD分组 请求
    admin.UpdatePsdGroupResponse:
      type: object
      properties:
        item:
          title: item
          $ref: '#/components/schemas/msgpb.PSDGroup'
      title: UpdatePsdGroupResponse
      additionalProperties: false
      description: 更新PSD分组 响应
    admin.UploadPsdFileRequest:
      type: object
      properties:
        hash:
          type: string
          title: hash
          maxLength: 40
          minLength: 40
      title: UploadPsdFileRequest
      required:
        - hash
      additionalProperties: false
      description: 上传PSD模板文件 请求
    admin.UploadPsdFileResponse:
      type: object
      title: UploadPsdFileResponse
      additionalProperties: false
      description: 上传PSD模板文件 响应
    connect-protocol-version:
      type: number
      title: Connect-Protocol-Version
      enum:
        - 1
      description: Define the version of the Connect protocol
      const: 1
    connect-timeout-header:
      type: number
      title: Connect-Timeout-Ms
      description: Define the timeout, in ms
    connect.error:
      type: object
      properties:
        code:
          type: string
          examples:
            - not_found
          enum:
            - canceled
            - unknown
            - invalid_argument
            - deadline_exceeded
            - not_found
            - already_exists
            - permission_denied
            - resource_exhausted
            - failed_precondition
            - aborted
            - out_of_range
            - unimplemented
            - internal
            - unavailable
            - data_loss
            - unauthenticated
          description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
        message:
          type: string
          description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
        details:
          type: array
          items:
            $ref: '#/components/schemas/connect.error_details.Any'
          description: A list of messages that carry the error details. There is no limit on the number of messages.
      title: Connect Error
      additionalProperties: true
      description: 'Error type returned by Connect: https://connectrpc.com/docs/go/errors/#http-representation'
    connect.error_details.Any:
      type: object
      properties:
        type:
          type: string
          description: 'A URL that acts as a globally unique identifier for the type of the serialized message. For example: `type.googleapis.com/google.rpc.ErrorInfo`. This is used to determine the schema of the data in the `value` field and is the discriminator for the `debug` field.'
        value:
          type: string
          format: binary
          description: The Protobuf message, serialized as bytes and base64-encoded. The specific message type is identified by the `type` field.
        debug:
          oneOf:
            - type: object
              title: Any
              additionalProperties: true
              description: Detailed error information.
          discriminator:
            propertyName: type
          title: Debug
          description: Deserialized error detail payload. The 'type' field indicates the schema. This field is for easier debugging and should not be relied upon for application logic.
      additionalProperties: true
      description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message, with an additional debug field for ConnectRPC error details.
    google.protobuf.Timestamp:
      type: string
      examples:
        - "2023-01-15T01:30:15.01Z"
        - "2024-12-25T12:00:00Z"
      format: date-time
      description: |-
        A Timestamp represents a point in time independent of any time zone or local
         calendar, encoded as a count of seconds and fractions of seconds at
         nanosecond resolution. The count is relative to an epoch at UTC midnight on
         January 1, 1970, in the proleptic Gregorian calendar which extends the
         Gregorian calendar backwards to year one.

         All minutes are 60 seconds long. Leap seconds are "smeared" so that no leap
         second table is needed for interpretation, using a [24-hour linear
         smear](https://developers.google.com/time/smear).

         The range is from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59.999999999Z. By
         restricting to that range, we ensure that we can convert to and from [RFC
         3339](https://www.ietf.org/rfc/rfc3339.txt) date strings.

         # Examples

         Example 1: Compute Timestamp from POSIX `time()`.

             Timestamp timestamp;
             timestamp.set_seconds(time(NULL));
             timestamp.set_nanos(0);

         Example 2: Compute Timestamp from POSIX `gettimeofday()`.

             struct timeval tv;
             gettimeofday(&tv, NULL);

             Timestamp timestamp;
             timestamp.set_seconds(tv.tv_sec);
             timestamp.set_nanos(tv.tv_usec * 1000);

         Example 3: Compute Timestamp from Win32 `GetSystemTimeAsFileTime()`.

             FILETIME ft;
             GetSystemTimeAsFileTime(&ft);
             UINT64 ticks = (((UINT64)ft.dwHighDateTime) << 32) | ft.dwLowDateTime;

             // A Windows tick is 100 nanoseconds. Windows epoch 1601-01-01T00:00:00Z
             // is 11644473600 seconds before Unix epoch 1970-01-01T00:00:00Z.
             Timestamp timestamp;
             timestamp.set_seconds((INT64) ((ticks / 10000000) - 11644473600LL));
             timestamp.set_nanos((INT32) ((ticks % 10000000) * 100));

         Example 4: Compute Timestamp from Java `System.currentTimeMillis()`.

             long millis = System.currentTimeMillis();

             Timestamp timestamp = Timestamp.newBuilder().setSeconds(millis / 1000)
                 .setNanos((int) ((millis % 1000) * 1000000)).build();

         Example 5: Compute Timestamp from Java `Instant.now()`.

             Instant now = Instant.now();

             Timestamp timestamp =
                 Timestamp.newBuilder().setSeconds(now.getEpochSecond())
                     .setNanos(now.getNano()).build();

         Example 6: Compute Timestamp from current time in Python.

             timestamp = Timestamp()
             timestamp.GetCurrentTime()

         # JSON Mapping

         In JSON format, the Timestamp type is encoded as a string in the
         [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format. That is, the
         format is "{year}-{month}-{day}T{hour}:{min}:{sec}[.{frac_sec}]Z"
         where {year} is always expressed using four digits while {month}, {day},
         {hour}, {min}, and {sec} are zero-padded to two digits each. The fractional
         seconds, which can go up to 9 digits (i.e. up to 1 nanosecond resolution),
         are optional. The "Z" suffix indicates the timezone ("UTC"); the timezone
         is required. A proto3 JSON serializer should always use UTC (as indicated by
         "Z") when printing the Timestamp type and a proto3 JSON parser should be
         able to accept both UTC and other timezones (as indicated by an offset).

         For example, "2017-01-15T01:30:15.01Z" encodes 15.01 seconds past
         01:30 UTC on January 15, 2017.

         In JavaScript, one can convert a Date object to this format using the
         standard
         [toISOString()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/toISOString)
         method. In Python, a standard `datetime.datetime` object can be converted
         to this format using
         [`strftime`](https://docs.python.org/2/library/time.html#time.strftime) with
         the time format spec '%Y-%m-%dT%H:%M:%S.%fZ'. Likewise, in Java, one can use
         the Joda Time's [`ISODateTimeFormat.dateTime()`](
         http://joda-time.sourceforge.net/apidocs/org/joda/time/format/ISODateTimeFormat.html#dateTime()
         ) to obtain a formatter capable of generating timestamps in this format.
    msgpb.Material:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: ID of the ent.
        createdTime:
          title: created_time
          description: 创建时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        updatedTime:
          title: updated_time
          description: 更新时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        title:
          type: string
          title: title
          description: 素材标题
        sourceUrl:
          type: string
          title: source_url
          description: 原始素材地址
        materialGroup:
          type: array
          items:
            type:
              - integer
              - string
            format: int64
          title: material_group
          description: 素材组
        flag:
          type: array
          items:
            type: string
          title: flag
          description: 素材标记
        sourceTemuId:
          type:
            - integer
            - string
          title: source_temu_id
          format: int64
          description: 关联Temu采集
        hash:
          type: string
          title: hash
          description: 文件校验码
        path:
          type: string
          title: path
          description: 素材S3存储路径
      title: Material
      additionalProperties: false
    msgpb.MaterialGroup:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: ID of the ent.
        createdTime:
          title: created_time
          description: 创建时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        updatedTime:
          title: updated_time
          description: 更新时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        name:
          type: string
          title: name
          description: Name holds the value of the "name" field.
      title: MaterialGroup
      additionalProperties: false
    msgpb.PSD:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: ID
        createdTime:
          title: created_time
          description: 创建时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        updatedTime:
          title: updated_time
          description: 更新时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        psdGroupId:
          type: array
          items:
            type:
              - integer
              - string
            format: int64
          title: psd_group_id
          description: 所属模型分组id
        desc:
          type: string
          title: desc
          description: 模型描述
        filePath:
          type: string
          title: file_path
          description: PSD文件路径
        fileSha1:
          type: string
          title: file_sha1
          description: PSD文件SHA1值
        fileValid:
          type: integer
          title: file_valid
          format: int32
          description: '文件是否有效，0: 无效, 1: 有效'
        weight:
          type: integer
          title: weight
          format: int32
          description: 模型权重，数值越大优先级越高
        isCover:
          type: integer
          title: is_cover
          format: int32
          description: '是否为封面模型，0: 否, 1: 是'
      title: PSD
      additionalProperties: false
      description: psd 模板
    msgpb.PSDGroup:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: ID
        name:
          type: string
          title: name
          description: 组名
        desc:
          type: string
          title: desc
          description: 描述
        createdTime:
          title: created_time
          description: 创建时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        updatedTime:
          title: updated_time
          description: 更新时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
      title: PSDGroup
      additionalProperties: false
      description: psd组
    msgpb.PageSizeRequest:
      type: object
      properties:
        page:
          type: integer
          title: page
          minimum: 0
          format: int32
          description: 20250413 页码
        size:
          type: integer
          title: size
          minimum: 0
          format: int32
          description: 20250413 每页数量
      title: PageSizeRequest
      additionalProperties: false
      description: 20250413 分页参数
    msgpb.ProductFilter:
      type: object
      properties:
        material:
          type: array
          items:
            type: string
          title: material
          description: 材质 20250806
        style:
          type: array
          items:
            type: string
          title: style
          description: 风格 20250806
        scene:
          type: array
          items:
            type: string
          title: scene
          description: 场景 20250806
        tag:
          type: array
          items:
            type: string
          title: tag
          description: 标签 20250806
      title: ProductFilter
      additionalProperties: false
      description: 产品过滤属性 20250806
    msgpb.ProductItem:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: ID of the ent.
        createdTime:
          title: created_time
          description: 创建时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        updatedTime:
          title: updated_time
          description: 更新时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        hash:
          type: string
          title: hash
          description: 数据唯一
        itemId:
          type: string
          title: item_id
          description: 产品ID
        platform:
          type: string
          title: platform
          description: 所属平台
        spec:
          title: spec
          description: 格式化的产品数据
          $ref: '#/components/schemas/msgpb.ProductSpec'
        filter:
          title: filter
          description: 产品过滤属性
          $ref: '#/components/schemas/msgpb.ProductFilter'
        mark:
          type: array
          items:
            type: string
          title: mark
          description: 数据处理标记
      title: ProductItem
      additionalProperties: false
      description: 产品项数据结构,db1的原型
    msgpb.ProductSpec:
      type: object
      properties:
        id:
          type: string
          title: id
          description: 产品id 20250806
        title:
          type: string
          title: title
          description: 产品标题 20250806
        link:
          type: string
          title: link
          description: 产品链接 20250806
        price:
          type:
            - integer
            - string
          title: price
          format: int64
          description: 价格 20250809
        sales:
          type:
            - integer
            - string
          title: sales
          format: int64
          description: 销量 20250806
        shopId:
          type: string
          title: shop_id
          description: 店铺id 20250806
        temuCats:
          type: array
          items:
            type: string
          title: temu_cats
          description: temu分类ID 20250806
        featuredImage:
          type: string
          title: featured_image
          description: 主图 20250809
        image:
          type: array
          items:
            type: string
          title: image
          description: 图片列表 20250809
        currency:
          type: string
          title: currency
          description: 币种 20250809
        skuId:
          type: string
          title: sku_id
      title: ProductSpec
      additionalProperties: false
      description: 格式化的采集产品结构 20250806
    msgpb.RegisteredTrademark:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: ID of the ent.
        createdTime:
          title: created_time
          description: 创建时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        updatedTime:
          title: updated_time
          description: 更新时间
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        tid:
          type: integer
          title: tid
          format: int32
          description: 商标ID
        data:
          type: string
          title: data
          description: 商标数据 (JSON格式)
      title: RegisteredTrademark
      additionalProperties: false
      description: 注册商标信息
security: []
tags:
  - name: admin.AuthService
    description: 未登录的后台账号功能 20250827
  - name: admin.AuthedService
    description: 已登录的后台账号管理 20250827
  - name: admin.MaterialService
    description: 素材管理 20250904
  - name: admin.PsdService
    description: PSD模板管理 20250916
  - name: admin.PsdGroupService
    description: PSD分组管理 20250910
  - name: admin.TemuService
    description: Temu服务 20250827
  - name: admin.TrademarkService
    description: 商标管理 20250808
  - name: admin.UploadDataService
    description: 采集数据上报 20250808
