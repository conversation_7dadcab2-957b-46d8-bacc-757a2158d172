<!DOCTYPE html>

<html>
  <head>
    <title>Protocol Documentation</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Ubuntu:400,700,400italic"/>
    <style>
      body {
        width: 60em;
        margin: 1em auto;
        color: #222;
        font-family: "Ubuntu", sans-serif;
        padding-bottom: 4em;
      }

      h1 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      h2 {
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
        margin: 1.5em 0;
      }

      h3 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      a {
        text-decoration: none;
        color: #567e25;
      }

      table {
        width: 100%;
        font-size: 80%;
        border-collapse: collapse;
      }

      thead {
        font-weight: 700;
        background-color: #dcdcdc;
      }

      tbody tr:nth-child(even) {
        background-color: #fbfbfb;
      }

      td {
        border: 1px solid #ccc;
        padding: 0.5ex 2ex;
      }

      td p {
        text-indent: 1em;
        margin: 0;
      }

      td p:nth-child(1) {
        text-indent: 0;  
      }

       
      .field-table td:nth-child(1) {  
        width: 10em;
      }
      .field-table td:nth-child(2) {  
        width: 10em;
      }
      .field-table td:nth-child(3) {  
        width: 6em;
      }
      .field-table td:nth-child(4) {  
        width: auto;
      }

       
      .extension-table td:nth-child(1) {  
        width: 10em;
      }
      .extension-table td:nth-child(2) {  
        width: 10em;
      }
      .extension-table td:nth-child(3) {  
        width: 10em;
      }
      .extension-table td:nth-child(4) {  
        width: 5em;
      }
      .extension-table td:nth-child(5) {  
        width: auto;
      }

       
      .enum-table td:nth-child(1) {  
        width: 10em;
      }
      .enum-table td:nth-child(2) {  
        width: 10em;
      }
      .enum-table td:nth-child(3) {  
        width: auto;
      }

       
      .scalar-value-types-table tr {
        height: 3em;
      }

       
      #toc-container ul {
        list-style-type: none;
        padding-left: 1em;
        line-height: 180%;
        margin: 0;
      }
      #toc > li > a {
        font-weight: bold;
      }

       
      .file-heading {
        width: 100%;
        display: table;
        border-bottom: 1px solid #aaa;
        margin: 4em 0 1.5em 0;
      }
      .file-heading h2 {
        border: none;
        display: table-cell;
      }
      .file-heading a {
        text-align: right;
        display: table-cell;
      }

       
      .badge {
        width: 1.6em;
        height: 1.6em;
        display: inline-block;

        line-height: 1.6em;
        text-align: center;
        font-weight: bold;
        font-size: 60%;

        color: #89ba48;
        background-color: #dff0c8;

        margin: 0.5ex 1em 0.5ex -1em;
        border: 1px solid #fbfbfb;
        border-radius: 1ex;
      }
    </style>

    
    <link rel="stylesheet" type="text/css" href="stylesheet.css"/>
  </head>

  <body>

    <h1 id="title">Protocol Documentation</h1>

    <h2>Table of Contents</h2>

    <div id="toc-container">
      <ul id="toc">
        
          
          <li>
            <a href="#common.msg.proto">common.msg.proto</a>
            <ul>
              
                <li>
                  <a href="#msgpb.PageSizeRequest"><span class="badge">M</span>PageSizeRequest</a>
                </li>
              
                <li>
                  <a href="#msgpb.ValidateMessage"><span class="badge">M</span>ValidateMessage</a>
                </li>
              
                <li>
                  <a href="#msgpb.ValidateMessages"><span class="badge">M</span>ValidateMessages</a>
                </li>
              
              
              
              
            </ul>
          </li>
        
          
          <li>
            <a href="#flow.msg.proto">flow.msg.proto</a>
            <ul>
              
                <li>
                  <a href="#msgpb.FaultMessage"><span class="badge">M</span>FaultMessage</a>
                </li>
              
              
              
              
            </ul>
          </li>
        
          
          <li>
            <a href="#material.msg.proto">material.msg.proto</a>
            <ul>
              
                <li>
                  <a href="#msgpb.Administrator"><span class="badge">M</span>Administrator</a>
                </li>
              
                <li>
                  <a href="#msgpb.Material"><span class="badge">M</span>Material</a>
                </li>
              
                <li>
                  <a href="#msgpb.MaterialGroup"><span class="badge">M</span>MaterialGroup</a>
                </li>
              
                <li>
                  <a href="#msgpb.RegisteredTrademark"><span class="badge">M</span>RegisteredTrademark</a>
                </li>
              
              
              
              
            </ul>
          </li>
        
          
          <li>
            <a href="#product.msg.proto">product.msg.proto</a>
            <ul>
              
                <li>
                  <a href="#msgpb.ProductFilter"><span class="badge">M</span>ProductFilter</a>
                </li>
              
                <li>
                  <a href="#msgpb.ProductItem"><span class="badge">M</span>ProductItem</a>
                </li>
              
                <li>
                  <a href="#msgpb.ProductSpec"><span class="badge">M</span>ProductSpec</a>
                </li>
              
              
              
              
            </ul>
          </li>
        
          
          <li>
            <a href="#psd.msg.proto">psd.msg.proto</a>
            <ul>
              
                <li>
                  <a href="#msgpb.PSD"><span class="badge">M</span>PSD</a>
                </li>
              
                <li>
                  <a href="#msgpb.PSDGroup"><span class="badge">M</span>PSDGroup</a>
                </li>
              
              
              
              
            </ul>
          </li>
        
          
          <li>
            <a href="#rule.msg.proto">rule.msg.proto</a>
            <ul>
              
              
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
                <li>
                  <a href="#rule.msg.proto-extensions"><span class="badge">X</span>File-level Extensions</a>
                </li>
              
              
            </ul>
          </li>
        
        <li><a href="#scalar-value-types">Scalar Value Types</a></li>
      </ul>
    </div>

    
      
      <div class="file-heading">
        <h2 id="common.msg.proto">common.msg.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="msgpb.PageSizeRequest">PageSizeRequest</h3>
        <p>20250413 分页参数</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>page</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>20250413 页码 </p></td>
                </tr>
              
                <tr>
                  <td>size</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>20250413 每页数量 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.ValidateMessage">ValidateMessage</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>field</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>message</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.ValidateMessages">ValidateMessages</h3>
        <p>proto验证错误转换 20250808</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>fields</td>
                  <td><a href="#msgpb.ValidateMessage">ValidateMessage</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
    
      
      <div class="file-heading">
        <h2 id="flow.msg.proto">flow.msg.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="msgpb.FaultMessage">FaultMessage</h3>
        <p>Flow业务状态消息,此消息返回不触发重试,error返回则触发重试 20250726</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>halt</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否停止运行并返回 20250726 </p></td>
                </tr>
              
                <tr>
                  <td>hint</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>原因提示 20250726 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
    
      
      <div class="file-heading">
        <h2 id="material.msg.proto">material.msg.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="msgpb.Administrator">Administrator</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID of the ent. </p></td>
                </tr>
              
                <tr>
                  <td>created_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>updated_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间 </p></td>
                </tr>
              
                <tr>
                  <td>username</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>用户名 </p></td>
                </tr>
              
                <tr>
                  <td>password</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>密码 </p></td>
                </tr>
              
                <tr>
                  <td>state</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>状态 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.Material">Material</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID of the ent. </p></td>
                </tr>
              
                <tr>
                  <td>created_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>updated_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间 </p></td>
                </tr>
              
                <tr>
                  <td>title</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>素材标题 </p></td>
                </tr>
              
                <tr>
                  <td>source_url</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>原始素材地址 </p></td>
                </tr>
              
                <tr>
                  <td>material_group</td>
                  <td><a href="#int64">int64</a></td>
                  <td>repeated</td>
                  <td><p>素材组 </p></td>
                </tr>
              
                <tr>
                  <td>flag</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>素材标记 </p></td>
                </tr>
              
                <tr>
                  <td>source_temu_id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>关联Temu采集 </p></td>
                </tr>
              
                <tr>
                  <td>hash</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>文件校验码 </p></td>
                </tr>
              
                <tr>
                  <td>path</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>素材S3存储路径 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.MaterialGroup">MaterialGroup</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID of the ent. </p></td>
                </tr>
              
                <tr>
                  <td>created_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>updated_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间 </p></td>
                </tr>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>Name holds the value of the &#34;name&#34; field. </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.RegisteredTrademark">RegisteredTrademark</h3>
        <p>注册商标信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID of the ent. </p></td>
                </tr>
              
                <tr>
                  <td>created_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>updated_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间 </p></td>
                </tr>
              
                <tr>
                  <td>tid</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>商标ID </p></td>
                </tr>
              
                <tr>
                  <td>data</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>商标数据 (JSON格式) </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
    
      
      <div class="file-heading">
        <h2 id="product.msg.proto">product.msg.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="msgpb.ProductFilter">ProductFilter</h3>
        <p>产品过滤属性 20250806</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>material</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>材质 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>style</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>风格 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>scene</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>场景 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>tag</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>标签 20250806 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.ProductItem">ProductItem</h3>
        <p>产品项数据结构,db1的原型</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID of the ent. </p></td>
                </tr>
              
                <tr>
                  <td>created_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>updated_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间 </p></td>
                </tr>
              
                <tr>
                  <td>hash</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>数据唯一 </p></td>
                </tr>
              
                <tr>
                  <td>item_id</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>产品ID </p></td>
                </tr>
              
                <tr>
                  <td>platform</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>所属平台 </p></td>
                </tr>
              
                <tr>
                  <td>spec</td>
                  <td><a href="#msgpb.ProductSpec">ProductSpec</a></td>
                  <td></td>
                  <td><p>格式化的产品数据 </p></td>
                </tr>
              
                <tr>
                  <td>filter</td>
                  <td><a href="#msgpb.ProductFilter">ProductFilter</a></td>
                  <td></td>
                  <td><p>产品过滤属性 </p></td>
                </tr>
              
                <tr>
                  <td>mark</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>数据处理标记 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.ProductSpec">ProductSpec</h3>
        <p>格式化的采集产品结构 20250806</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>产品id 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>title</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>产品标题 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>link</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>产品链接 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>price</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>价格 20250809 </p></td>
                </tr>
              
                <tr>
                  <td>sales</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>销量 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>shop_id</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>店铺id 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>temu_cats</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>temu分类ID 20250806 </p></td>
                </tr>
              
                <tr>
                  <td>featured_image</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>主图 20250809 </p></td>
                </tr>
              
                <tr>
                  <td>image</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>图片列表 20250809 </p></td>
                </tr>
              
                <tr>
                  <td>currency</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>币种 20250809 </p></td>
                </tr>
              
                <tr>
                  <td>sku_id</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
    
      
      <div class="file-heading">
        <h2 id="psd.msg.proto">psd.msg.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="msgpb.PSD">PSD</h3>
        <p>psd 模板</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID </p></td>
                </tr>
              
                <tr>
                  <td>created_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>updated_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间 </p></td>
                </tr>
              
                <tr>
                  <td>psd_group_id</td>
                  <td><a href="#int64">int64</a></td>
                  <td>repeated</td>
                  <td><p>所属模型分组id </p></td>
                </tr>
              
                <tr>
                  <td>desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>模型描述 </p></td>
                </tr>
              
                <tr>
                  <td>file_path</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>PSD文件路径 </p></td>
                </tr>
              
                <tr>
                  <td>file_sha1</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>PSD文件SHA1值 </p></td>
                </tr>
              
                <tr>
                  <td>file_valid</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>文件是否有效，0: 无效, 1: 有效 </p></td>
                </tr>
              
                <tr>
                  <td>weight</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>模型权重，数值越大优先级越高 </p></td>
                </tr>
              
                <tr>
                  <td>is_cover</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>是否为封面模型，0: 否, 1: 是 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="msgpb.PSDGroup">PSDGroup</h3>
        <p>psd组</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID </p></td>
                </tr>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>组名 </p></td>
                </tr>
              
                <tr>
                  <td>desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>描述 </p></td>
                </tr>
              
                <tr>
                  <td>created_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>updated_time</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
    
      
      <div class="file-heading">
        <h2 id="rule.msg.proto">rule.msg.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      

      

      
        <h3 id="rule.msg.proto-extensions">File-level Extensions</h3>
        <table class="extension-table">
          <thead>
            <tr><td>Extension</td><td>Type</td><td>Base</td><td>Number</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>validate_email</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500008</td>
                <td><p>邮箱/用户名验证 20250726</p></td>
              </tr>
            
              <tr>
                <td>validate_only_alpha_number</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500001</td>
                <td><p>20250413 验证用户名</p></td>
              </tr>
            
              <tr>
                <td>validate_password</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500007</td>
                <td><p>密码验证 20250726</p></td>
              </tr>
            
              <tr>
                <td>validate_sha1</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500006</td>
                <td><p></p></td>
              </tr>
            
              <tr>
                <td>validate_shopify_customer_id</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500009</td>
                <td><p>验证shopify customer id</p></td>
              </tr>
            
              <tr>
                <td>validate_shopify_product_id</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500004</td>
                <td><p>20250413 验证shopify product id</p></td>
              </tr>
            
              <tr>
                <td>validate_url_handle</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500002</td>
                <td><p>20250413 验证 handle</p></td>
              </tr>
            
              <tr>
                <td>validate_var_key</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500005</td>
                <td><p>20250423 验证变量key,格式 abc.cba.123</p></td>
              </tr>
            
              <tr>
                <td>validate_xid</td>
                <td><a href="#bool">bool</a></td>
                <td><a href="#buf.validate.StringRules">.buf.validate.StringRules</a></td>
                <td>98500003</td>
                <td><p>20250413 验证集合id,所有通过xid生成的字符串id</p></td>
              </tr>
            
          </tbody>
        </table>
      

      
    

    <h2 id="scalar-value-types">Scalar Value Types</h2>
    <table class="scalar-value-types-table">
      <thead>
        <tr><td>.proto Type</td><td>Notes</td><td>C++</td><td>Java</td><td>Python</td><td>Go</td><td>C#</td><td>PHP</td><td>Ruby</td></tr>
      </thead>
      <tbody>
        
          <tr id="double">
            <td>double</td>
            <td></td>
            <td>double</td>
            <td>double</td>
            <td>float</td>
            <td>float64</td>
            <td>double</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="float">
            <td>float</td>
            <td></td>
            <td>float</td>
            <td>float</td>
            <td>float</td>
            <td>float32</td>
            <td>float</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="int32">
            <td>int32</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="int64">
            <td>int64</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="uint32">
            <td>uint32</td>
            <td>Uses variable-length encoding.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int/long</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="uint64">
            <td>uint64</td>
            <td>Uses variable-length encoding.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint32">
            <td>sint32</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint64">
            <td>sint64</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="fixed32">
            <td>fixed32</td>
            <td>Always four bytes. More efficient than uint32 if values are often greater than 2^28.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="fixed64">
            <td>fixed64</td>
            <td>Always eight bytes. More efficient than uint64 if values are often greater than 2^56.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="sfixed32">
            <td>sfixed32</td>
            <td>Always four bytes.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sfixed64">
            <td>sfixed64</td>
            <td>Always eight bytes.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="bool">
            <td>bool</td>
            <td></td>
            <td>bool</td>
            <td>boolean</td>
            <td>boolean</td>
            <td>bool</td>
            <td>bool</td>
            <td>boolean</td>
            <td>TrueClass/FalseClass</td>
          </tr>
        
          <tr id="string">
            <td>string</td>
            <td>A string must always contain UTF-8 encoded or 7-bit ASCII text.</td>
            <td>string</td>
            <td>String</td>
            <td>str/unicode</td>
            <td>string</td>
            <td>string</td>
            <td>string</td>
            <td>String (UTF-8)</td>
          </tr>
        
          <tr id="bytes">
            <td>bytes</td>
            <td>May contain any arbitrary sequence of bytes.</td>
            <td>string</td>
            <td>ByteString</td>
            <td>str</td>
            <td>[]byte</td>
            <td>ByteString</td>
            <td>string</td>
            <td>String (ASCII-8BIT)</td>
          </tr>
        
      </tbody>
    </table>
  </body>
</html>

