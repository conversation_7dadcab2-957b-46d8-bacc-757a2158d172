openapi: 3.1.0
info:
  description: "Partner接口文档"
  title: Partner接口文档
  version: v1.0.0
paths:
  /partner.TemuService/QueryTemuList:
    post:
      tags:
        - partner.TemuService
      summary: 查询Temu数据列表 接口 20250828
      description: 查询Temu数据列表 接口 20250828
      operationId: partner.TemuService.QueryTemuList
      parameters:
        - name: Connect-Protocol-Version
          in: header
          required: true
          schema:
            $ref: '#/components/schemas/connect-protocol-version'
        - name: Connect-Timeout-Ms
          in: header
          schema:
            $ref: '#/components/schemas/connect-timeout-header'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/partner.QueryTemuListRequest'
        required: true
      responses:
        default:
          description: Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/connect.error'
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/partner.QueryTemuListResponse'
components:
  schemas:
    connect-protocol-version:
      type: number
      title: Connect-Protocol-Version
      enum:
        - 1
      description: Define the version of the Connect protocol
      const: 1
    connect-timeout-header:
      type: number
      title: Connect-Timeout-Ms
      description: Define the timeout, in ms
    connect.error:
      type: object
      properties:
        code:
          type: string
          examples:
            - not_found
          enum:
            - canceled
            - unknown
            - invalid_argument
            - deadline_exceeded
            - not_found
            - already_exists
            - permission_denied
            - resource_exhausted
            - failed_precondition
            - aborted
            - out_of_range
            - unimplemented
            - internal
            - unavailable
            - data_loss
            - unauthenticated
          description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
        message:
          type: string
          description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
        details:
          type: array
          items:
            $ref: '#/components/schemas/connect.error_details.Any'
          description: A list of messages that carry the error details. There is no limit on the number of messages.
      title: Connect Error
      additionalProperties: true
      description: 'Error type returned by Connect: https://connectrpc.com/docs/go/errors/#http-representation'
    connect.error_details.Any:
      type: object
      properties:
        type:
          type: string
          description: 'A URL that acts as a globally unique identifier for the type of the serialized message. For example: `type.googleapis.com/google.rpc.ErrorInfo`. This is used to determine the schema of the data in the `value` field and is the discriminator for the `debug` field.'
        value:
          type: string
          format: binary
          description: The Protobuf message, serialized as bytes and base64-encoded. The specific message type is identified by the `type` field.
        debug:
          oneOf:
            - type: object
              title: Any
              additionalProperties: true
              description: Detailed error information.
          discriminator:
            propertyName: type
          title: Debug
          description: Deserialized error detail payload. The 'type' field indicates the schema. This field is for easier debugging and should not be relied upon for application logic.
      additionalProperties: true
      description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message, with an additional debug field for ConnectRPC error details.
    google.protobuf.Timestamp:
      type: string
      examples:
        - "2023-01-15T01:30:15.01Z"
        - "2024-12-25T12:00:00Z"
      format: date-time
      description: |-
        A Timestamp represents a point in time independent of any time zone or local
         calendar, encoded as a count of seconds and fractions of seconds at
         nanosecond resolution. The count is relative to an epoch at UTC midnight on
         January 1, 1970, in the proleptic Gregorian calendar which extends the
         Gregorian calendar backwards to year one.

         All minutes are 60 seconds long. Leap seconds are "smeared" so that no leap
         second table is needed for interpretation, using a [24-hour linear
         smear](https://developers.google.com/time/smear).

         The range is from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59.999999999Z. By
         restricting to that range, we ensure that we can convert to and from [RFC
         3339](https://www.ietf.org/rfc/rfc3339.txt) date strings.

         # Examples

         Example 1: Compute Timestamp from POSIX `time()`.

             Timestamp timestamp;
             timestamp.set_seconds(time(NULL));
             timestamp.set_nanos(0);

         Example 2: Compute Timestamp from POSIX `gettimeofday()`.

             struct timeval tv;
             gettimeofday(&tv, NULL);

             Timestamp timestamp;
             timestamp.set_seconds(tv.tv_sec);
             timestamp.set_nanos(tv.tv_usec * 1000);

         Example 3: Compute Timestamp from Win32 `GetSystemTimeAsFileTime()`.

             FILETIME ft;
             GetSystemTimeAsFileTime(&ft);
             UINT64 ticks = (((UINT64)ft.dwHighDateTime) << 32) | ft.dwLowDateTime;

             // A Windows tick is 100 nanoseconds. Windows epoch 1601-01-01T00:00:00Z
             // is 11644473600 seconds before Unix epoch 1970-01-01T00:00:00Z.
             Timestamp timestamp;
             timestamp.set_seconds((INT64) ((ticks / 10000000) - 11644473600LL));
             timestamp.set_nanos((INT32) ((ticks % 10000000) * 100));

         Example 4: Compute Timestamp from Java `System.currentTimeMillis()`.

             long millis = System.currentTimeMillis();

             Timestamp timestamp = Timestamp.newBuilder().setSeconds(millis / 1000)
                 .setNanos((int) ((millis % 1000) * 1000000)).build();

         Example 5: Compute Timestamp from Java `Instant.now()`.

             Instant now = Instant.now();

             Timestamp timestamp =
                 Timestamp.newBuilder().setSeconds(now.getEpochSecond())
                     .setNanos(now.getNano()).build();

         Example 6: Compute Timestamp from current time in Python.

             timestamp = Timestamp()
             timestamp.GetCurrentTime()

         # JSON Mapping

         In JSON format, the Timestamp type is encoded as a string in the
         [RFC 3339](https://www.ietf.org/rfc/rfc3339.txt) format. That is, the
         format is "{year}-{month}-{day}T{hour}:{min}:{sec}[.{frac_sec}]Z"
         where {year} is always expressed using four digits while {month}, {day},
         {hour}, {min}, and {sec} are zero-padded to two digits each. The fractional
         seconds, which can go up to 9 digits (i.e. up to 1 nanosecond resolution),
         are optional. The "Z" suffix indicates the timezone ("UTC"); the timezone
         is required. A proto3 JSON serializer should always use UTC (as indicated by
         "Z") when printing the Timestamp type and a proto3 JSON parser should be
         able to accept both UTC and other timezones (as indicated by an offset).

         For example, "2017-01-15T01:30:15.01Z" encodes 15.01 seconds past
         01:30 UTC on January 15, 2017.

         In JavaScript, one can convert a Date object to this format using the
         standard
         [toISOString()](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/toISOString)
         method. In Python, a standard `datetime.datetime` object can be converted
         to this format using
         [`strftime`](https://docs.python.org/2/library/time.html#time.strftime) with
         the time format spec '%Y-%m-%dT%H:%M:%S.%fZ'. Likewise, in Java, one can use
         the Joda Time's [`ISODateTimeFormat.dateTime()`](
         http://joda-time.sourceforge.net/apidocs/org/joda/time/format/ISODateTimeFormat.html#dateTime()
         ) to obtain a formatter capable of generating timestamps in this format.
    msgpb.PageSizeRequest:
      type: object
      properties:
        page:
          type: integer
          title: page
          minimum: 0
          format: int32
          description: 20250413 页码
        size:
          type: integer
          title: size
          minimum: 0
          format: int32
          description: 20250413 每页数量
      title: PageSizeRequest
      additionalProperties: false
      description: 20250413 分页参数
    msgpb.ProductSpec:
      type: object
      properties:
        id:
          type: string
          title: id
          description: 产品id 20250806
        title:
          type: string
          title: title
          description: 产品标题 20250806
        link:
          type: string
          title: link
          description: 产品链接 20250806
        price:
          type:
            - integer
            - string
          title: price
          format: int64
          description: 价格 20250809
        sales:
          type:
            - integer
            - string
          title: sales
          format: int64
          description: 销量 20250806
        shopId:
          type: string
          title: shop_id
          description: 店铺id 20250806
        temuCats:
          type: array
          items:
            type: string
          title: temu_cats
          description: temu分类ID 20250806
        featuredImage:
          type: string
          title: featured_image
          description: 主图 20250809
        image:
          type: array
          items:
            type: string
          title: image
          description: 图片列表 20250809
        currency:
          type: string
          title: currency
          description: 币种 20250809
        skuId:
          type: string
          title: sku_id
      title: ProductSpec
      additionalProperties: false
      description: 格式化的采集产品结构 20250806
    partner.QueryTemuListRequest:
      type: object
      properties:
        pageSize:
          title: page_size
          $ref: '#/components/schemas/msgpb.PageSizeRequest'
        title:
          type: string
          title: title
          description: 搜索标题 20250828
        temuCats:
          type: array
          items:
            type: string
          title: temu_cats
          description: 过滤分类 20250829
      title: QueryTemuListRequest
      additionalProperties: false
      description: 查询Temu数据列表 请求
    partner.QueryTemuListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/partner.QueryTemuListResponse.Item'
          title: items
          description: 数据列表 20250828
        total:
          type:
            - integer
            - string
          title: total
          format: int64
          description: 总数量 20250828
      title: QueryTemuListResponse
      additionalProperties: false
      description: 查询Temu数据列表 响应
    partner.QueryTemuListResponse.Item:
      type: object
      properties:
        id:
          type:
            - integer
            - string
          title: id
          format: int64
          description: ID of the ent. 20250828
        createdTime:
          title: created_time
          description: 创建时间 20250828
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        updatedTime:
          title: updated_time
          description: 更新时间 20250828
          $ref: '#/components/schemas/google.protobuf.Timestamp'
        hash:
          type: string
          title: hash
          description: 数据唯一 20250828
        itemId:
          type: string
          title: item_id
          description: 产品ID 20250828
        platform:
          type: string
          title: platform
          description: 所属平台 20250828
        spec:
          title: spec
          description: 格式化的产品数据 20250828
          $ref: '#/components/schemas/msgpb.ProductSpec'
        data:
          type: string
          title: data
          description: 原始数据 20250828
      title: Item
      additionalProperties: false
      description: 列表项目 20250828
security: []
tags:
  - name: partner.TemuService
    description: Temu服务 20250827
