package types

import (
    "fmt"
)

// 项目运行时配置 20250806

type (
    Databases struct {
        Pgsql   Pgsql   `mapstructure:"pgsql"`
        Redis   Redis   `mapstructure:"redis"`
        Mongodb Mongodb `mapstructure:"mongodb"`
    }
    Mongodb struct {
        Host       string `mapstructure:"host"`
        Port       int    `mapstructure:"port"`
        Username   string `mapstructure:"username"`
        Password   string `mapstructure:"password"`
        Database   string `mapstructure:"database"`
        AuthSource string `mapstructure:"auth_source"`
    }
    Redis struct {
        Host     string `mapstructure:"host"`
        Port     int    `mapstructure:"port"`
        Password string `mapstructure:"password"`
        DB       int    `mapstructure:"db"`
    }
    Pgsql struct {
        Username string `mapstructure:"username"`
        Password string `mapstructure:"password"`
        Host     string `mapstructure:"host"`
        Port     int    `mapstructure:"port"`
        DBName   string `mapstructure:"dbname"`
    }

    // 上报接口key 20250808
    HashKeys struct {
        Bmw string `mapstructure:"bmw"`
    }
    Admin struct {
        ListenPort int    `mapstructure:"listen_port"`
        JwtKey     string `mapstructure:"jwt_key"`
        // 允许所有跨域 20250827
        AllowAllCors bool `mapstructure:"allow_all_cors"`
    }
    // 对外合作接口 20250901
    Partner struct {
        ListenPort int `mapstructure:"listen_port"`
    }
    // Comfyui服务桥接地址 20250901
    ComfyuiAdapter struct {
        // 支持多个地址,[]string{"http://********:8888"} 20250901
        Hosts []string `mapstructure:"hosts"`
    }
    Backblaze struct {
        Endpoint   string `mapstructure:"endpoint"`
        AccessID   string `mapstructure:"access_id"`
        AccessKey  string `mapstructure:"access_key"`
        BucketName string `mapstructure:"bucket_name"`
        CDNPrefix  string `mapstructure:"cdn_prefix"`
    }
    Temporal struct {
        Host     string `mapstructure:"host"`      // 20241209 服务器 by Node
        LogLevel int    `mapstructure:"log_level"` // 20241210 日志等级 by Node
        Namespace string `mapstructure:"namespace"` // 指定命名空间,多套程序共用一个平台需要隔离,否则就可以互相调用啦
    }
    // 总配置 20250806
    RemoteOptions struct {
        Temporal Temporal `mapstructure:"temporal"`
        Backblaze Backblaze `mapstructure:"backblaze"`
        ComfyuiAdapter ComfyuiAdapter `mapstructure:"comfyui_adapter"`
        Partner   Partner   `mapstructure:"partner"`
        Admin     Admin     `mapstructure:"admin"`
        HashKeys  HashKeys  `mapstructure:"hashkeys"`
        Databases Databases `mapstructure:"databases"`
    }
)

func (r *Mongodb) GetDSN() string {
    return fmt.Sprintf("mongodb://%s:%s@%s:%d/%s?authSource=%s",
        r.Username, r.Password, r.Host, r.Port, r.Database, r.AuthSource)
}
func (r *Pgsql) GetDSN() string {
    return fmt.Sprintf(
        "postgres://%s:%s@%s:%d/%s?sslmode=disable",
        r.Username,
        r.Password,
        r.Host,
        r.Port,
        r.DBName,
    )
}
