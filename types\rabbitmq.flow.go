package types

import (
    "omnix/genpb/enumpb"
    "time"

    "go.temporal.io/api/enums/v1"
    "go.temporal.io/sdk/client"
    "go.temporal.io/sdk/temporal"
)

type ScheduleFunc func(name string) client.ScheduleOptions

// 通过队列执行工作流 20250903
type RabbitFlowJob struct {
    // 为工作流指定一个唯一ID 20250903
    Id string `json:"id"`
    // 要执行的工作流名称 20250903
    Name                                     string                         `json:"name"`
    WorkflowExecutionTimeout                 time.Duration                  `json:"workflow_execution_timeout"`
    WorkflowIDReusePolicy                    enums.WorkflowIdReusePolicy    `json:"workflow_id_reuse_policy"`
    WorkflowIDConflictPolicy                 enums.WorkflowIdConflictPolicy `json:"workflow_id_conflict_policy"`
    WorkflowExecutionErrorWhenAlreadyStarted bool                           `json:"workflow_execution_error_when_already_started"`
    Args                                     []any                          `json:"args"`
    // 此处为工作流的队列,非rabbitmq的 20250903
    TaskQueue                                string                         `json:"task_queue"`
    RetryPolicy                              *temporal.RetryPolicy          `json:"retry_policy"`
}

func (r *RabbitFlowJob) Validate() error {
    if r.TaskQueue == "" {
        r.TaskQueue = enumpb.FLOW_QUEUE_DEFAULT.ToState()
    }
    return nil
}

func (r *RabbitFlowJob) GetRetryPolicy() *temporal.RetryPolicy {
    if r.RetryPolicy == nil {
        // 默认是不需要重试的,重试应在activity里面就完成 20250528
        //r.RetryPolicy = &temporal.RetryPolicy{
        //    InitialInterval:    time.Second * 5,
        //    MaximumAttempts:    2,
        //    BackoffCoefficient: 2,
        //    MaximumInterval:    time.Second * 30,
        //}
    }
    return r.RetryPolicy
}
